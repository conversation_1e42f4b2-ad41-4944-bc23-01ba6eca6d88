{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/PrivateRoute.js"], "sourcesContent": ["// components/PrivateRoute.js\r\n'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useEffect } from 'react';\r\n\r\nexport function PrivateRoute({ children }) {\r\n  const { user, loading } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('token');\r\n    \r\n    if (!loading && !user && !token && pathname !== '/login') {\r\n      router.push('/login');\r\n    }\r\n  }, [user, loading, router, pathname]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return user ? children : null;\r\n}"], "names": [], "mappings": "AAAA,6BAA6B;;;;;AAG7B;AACA;AACA;;;AAJA;;;;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAE;;IACvC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,aAAa,UAAU;gBACxD,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAM;QAAS;QAAQ;KAAS;IAEpC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,OAAO,OAAO,WAAW;AAC3B;GAtBgB;;QACY,iIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHd"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/hooks/useConstructionMessage.js"], "sourcesContent": ["\"use client\";\n\nimport { useConstruction } from '@/contexts/ConstructionContext';\n\n/**\n * Hook personalizado para facilitar o uso da funcionalidade \"Em Construção\"\n * \n * @returns {Object} Objeto com funções para mostrar mensagens \"Em Construção\"\n */\nexport const useConstructionMessage = () => {\n  const { showConstructionMessage } = useConstruction();\n\n  /**\n   * Função para adicionar a funcionalidade \"Em Construção\" a um elemento\n   * @param {Object} options - Opções para a mensagem\n   * @returns {Object} Objeto com props para adicionar ao elemento\n   */\n  const constructionProps = (options = {}) => {\n    return {\n      onClick: (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        \n        // Usar o elemento clicado como alvo para a mensagem\n        const targetElement = e.currentTarget;\n        const uniqueSelector = `construction-target-${Date.now()}`;\n        \n        // Adicionar um atributo temporário para identificar o elemento\n        targetElement.setAttribute('data-construction-target', uniqueSelector);\n        \n        // Mostrar a mensagem\n        showConstructionMessage({\n          ...options,\n          targetSelector: `[data-construction-target=\"${uniqueSelector}\"]`\n        });\n        \n        // Remover o atributo após um tempo\n        setTimeout(() => {\n          targetElement.removeAttribute('data-construction-target');\n        }, 5000);\n      }\n    };\n  };\n\n  return {\n    constructionProps,\n    showConstructionMessage\n  };\n};\n\nexport default useConstructionMessage;\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AASO,MAAM,yBAAyB;;IACpC,MAAM,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IAElD;;;;GAIC,GACD,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACrC,OAAO;YACL,SAAS,CAAC;gBACR,EAAE,cAAc;gBAChB,EAAE,eAAe;gBAEjB,oDAAoD;gBACpD,MAAM,gBAAgB,EAAE,aAAa;gBACrC,MAAM,iBAAiB,CAAC,oBAAoB,EAAE,KAAK,GAAG,IAAI;gBAE1D,+DAA+D;gBAC/D,cAAc,YAAY,CAAC,4BAA4B;gBAEvD,qBAAqB;gBACrB,wBAAwB;oBACtB,GAAG,OAAO;oBACV,gBAAgB,CAAC,2BAA2B,EAAE,eAAe,EAAE,CAAC;gBAClE;gBAEA,mCAAmC;gBACnC,WAAW;oBACT,cAAc,eAAe,CAAC;gBAChC,GAAG;YACL;QACF;IACF;IAEA,OAAO;QACL;QACA;IACF;AACF;GAvCa;;QACyB,yIAAA,CAAA,kBAAe;;;uCAwCtC"}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/construction/ConstructionButton.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\n\n/**\n * Botão que exibe uma mensagem \"Em Construção\" quando clicado\n * \n * @param {Object} props - Propriedades do componente\n * @param {string} props.title - <PERSON><PERSON><PERSON><PERSON> da mensagem\n * @param {string|React.ReactNode} props.content - Conteúdo/descrição da mensagem\n * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')\n * @param {string} props.icon - Ícone a ser exibido ('Construction', 'HardHat', 'Hammer', 'Wrench', 'AlertTriangle')\n * @param {React.ReactNode} props.children - Conteúdo do botão\n * @param {string} props.className - Classes CSS adicionais\n * @param {Object} props.buttonProps - Propriedades adicionais para o botão\n */\nconst ConstructionButton = ({\n  title = 'Em Construção',\n  content = 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',\n  position = 'auto',\n  icon = 'Construction',\n  children,\n  className = '',\n  ...buttonProps\n}) => {\n  const { constructionProps } = useConstructionMessage();\n\n  // Obter props para o botão \"Em Construção\"\n  const constructionButtonProps = constructionProps({\n    title,\n    content,\n    position,\n    icon\n  });\n\n  return (\n    <button\n      className={className}\n      {...buttonProps}\n      {...constructionButtonProps}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default ConstructionButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA;;;;;;;;;;;CAWC,GACD,MAAM,qBAAqB,CAAC,EAC1B,QAAQ,eAAe,EACvB,UAAU,2EAA2E,EACrF,WAAW,MAAM,EACjB,OAAO,cAAc,EACrB,QAAQ,EACR,YAAY,EAAE,EACd,GAAG,aACJ;;IACC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,yBAAsB,AAAD;IAEnD,2CAA2C;IAC3C,MAAM,0BAA0B,kBAAkB;QAChD;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACV,GAAG,WAAW;QACd,GAAG,uBAAuB;kBAE1B;;;;;;AAGP;GA5BM;;QAS0B,yIAAA,CAAA,yBAAsB;;;KAThD;uCA8BS"}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/construction/withConstruction.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\n\n/**\n * HOC (Higher-Order Component) que adiciona a funcionalidade \"Em Construção\" a um componente\n * \n * @param {React.ComponentType} Component - Componente a ser envolvido\n * @param {Object} options - Opções para a mensagem \"Em Construção\"\n * @returns {React.ComponentType} Componente envolvido com a funcionalidade \"Em Construção\"\n */\nexport const withConstruction = (Component, options = {}) => {\n  // Definir opções padrão\n  const defaultOptions = {\n    title: 'Em Construção',\n    content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',\n    position: 'auto',\n    icon: 'Construction'\n  };\n\n  // Mesclar opções padrão com as opções fornecidas\n  const mergedOptions = { ...defaultOptions, ...options };\n\n  // Retornar o componente envolvido\n  return function ConstructionWrapper(props) {\n    const { constructionProps } = useConstructionMessage();\n\n    // Adicionar a funcionalidade \"Em Construção\" ao componente\n    return <Component {...props} {...constructionProps(mergedOptions)} />;\n  };\n};\n\nexport default withConstruction;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYO,MAAM,mBAAmB,CAAC,WAAW,UAAU,CAAC,CAAC;;IACtD,wBAAwB;IACxB,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,iDAAiD;IACjD,MAAM,gBAAgB;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IAEtD,kCAAkC;IAClC,UAAO,SAAS,oBAAoB,KAAK;;QACvC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,yBAAsB,AAAD;QAEnD,2DAA2D;QAC3D,qBAAO,6LAAC;YAAW,GAAG,KAAK;YAAG,GAAG,kBAAkB,cAAc;;;;;;IACnE;;YAJgC,yIAAA,CAAA,yBAAsB;;;AAKxD;uCAEe"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ThemeToggle.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\nimport { Sun, Moon } from 'lucide-react';\r\n\r\nexport function ThemeToggle() {\r\n  const { theme, toggleTheme } = useTheme();\r\n  \r\n  return (\r\n    <button\r\n      onClick={toggleTheme}\r\n      className=\"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n      aria-label={theme === 'light' ? 'Ativar modo escuro' : 'Ativar modo claro'}\r\n    >\r\n      {theme === 'light' ? (\r\n        <Moon size={20} aria-hidden=\"true\" />\r\n      ) : (\r\n        <Sun size={20} aria-hidden=\"true\" />\r\n      )}\r\n    </button>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,UAAU,UAAU,uBAAuB;kBAEtD,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;YAAI,eAAY;;;;;iCAE5B,6LAAC,mMAAA,CAAA,MAAG;YAAC,MAAM;YAAI,eAAY;;;;;;;;;;;AAInC;GAhBgB;;QACiB,kIAAA,CAAA,WAAQ;;;KADzB"}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/config/appConfig.js"], "sourcesContent": ["/**\n * Configurações globais da aplicação\n */\n\n// Versão atual do sistema\nexport const APP_VERSION = 'v0.1';\n\n// Outras configurações globais podem ser adicionadas aqui\nexport const APP_NAME = 'High Tide Systems';\nexport const APP_DESCRIPTION = 'Sistema de gestão para clínicas e consultórios';\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,0BAA0B;;;;;;AACnB,MAAM,cAAc;AAGpB,MAAM,WAAW;AACjB,MAAM,kBAAkB"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/dashboard/components.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport {\r\n  Settings, Users, Calendar, DollarSign, LayoutDashboard, LogOut, Menu, X,\r\n  Calculator, FileText, Building, Clock, Lock, Database, CreditCard,\r\n  BarChart, TrendingUp, Gift, GraduationCap, Home, Box, UserPlus, UserCheck,\r\n  MapPin, Tag, Bell, ChevronDown, ShieldCheck, Shield, UserCog,\r\n  MessageCircle, Search, ChevronRight, ShieldIcon, Info,\r\n  Briefcase, Construction, HardHat, Brain, Activity, BookOpen, ClipboardList, Award\r\n} from 'lucide-react';\r\nimport { useQuickNav } from '@/contexts/QuickNavContext';\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\nimport { ChatButton } from '@/components/chat';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { ThemeToggle } from '@/components/ThemeToggle';\r\nimport { APP_VERSION } from '@/config/appConfig';\r\n\r\n// Header Component\r\nconst Header = ({ toggleSidebar, isSidebarOpen }) => {\r\n  const { user, logout } = useAuth();\r\n  const router = useRouter();\r\n  const { openQuickNav } = useQuickNav();\r\n\r\n  // Função para determinar o ícone e as cores do papel do usuário\r\n  const getRoleInfo = () => {\r\n    switch (user?.role) {\r\n      case 'SYSTEM_ADMIN':\r\n        return {\r\n          icon: ShieldCheck,\r\n          bgColor: 'bg-red-50 dark:bg-red-900',\r\n          textColor: 'text-red-700 dark:text-red-300',\r\n          name: 'Admin do Sistema'\r\n        };\r\n      case 'COMPANY_ADMIN':\r\n        return {\r\n          icon: Shield,\r\n          bgColor: 'bg-blue-50 dark:bg-blue-900',\r\n          textColor: 'text-blue-700 dark:text-blue-300',\r\n          name: 'Admin da Empresa'\r\n        };\r\n      default:\r\n        return {\r\n          icon: UserCog,\r\n          bgColor: 'bg-green-50 dark:bg-green-900',\r\n          textColor: 'text-green-700 dark:text-green-300',\r\n          name: 'Funcionário'\r\n        };\r\n    }\r\n  };\r\n\r\n  const roleInfo = getRoleInfo();\r\n  const RoleIcon = roleInfo.icon;\r\n\r\n  // Pegar primeira letra de cada nome para o avatar\r\n  const getInitials = () => {\r\n    if (!user?.fullName) return 'U';\r\n\r\n    const names = user.fullName.split(' ');\r\n    if (names.length === 1) return names[0].charAt(0);\r\n\r\n    return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]\">\r\n      {/* Lado esquerdo: Logo e Toggle */}\r\n      <div className=\"flex items-center gap-3\">\r\n        <button\r\n          onClick={toggleSidebar}\r\n          className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\"\r\n          aria-label={isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\"}\r\n        >\r\n          {isSidebarOpen ? <X size={22} aria-hidden=\"true\" /> : <Menu size={22} aria-hidden=\"true\" />}\r\n        </button>\r\n\r\n        <div className=\"flex items-center\">\r\n          <div className=\"relative\">\r\n            <img\r\n              src=\"/logo_horizontal_sem_fundo.png\"\r\n              alt=\"High Tide Logo\"\r\n              className=\"h-10 mr-2.5 dark:invert dark:text-white\"\r\n            />\r\n            <span className=\"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\">{APP_VERSION}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Lado direito: Pesquisa, Notificações e Perfil */}\r\n      <div className=\"flex items-center gap-3\">\r\n        {/* Botão de pesquisa rápida */}\r\n        <button\r\n          onClick={openQuickNav}\r\n          className=\"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\"\r\n          aria-label=\"Abrir pesquisa rápida\"\r\n        >\r\n          <Search size={18} className=\"text-gray-400 dark:text-gray-500\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Pesquisar...</span>\r\n          <div className=\"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\">\r\n            <span>Ctrl + K</span>\r\n          </div>\r\n        </button>\r\n\r\n        {/* Botões de notificação */}\r\n        <ChatButton />\r\n\r\n        <ConstructionButton\r\n          className=\"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors\"\r\n          aria-label=\"Notificações\"\r\n          title=\"Sistema de Notificações\"\r\n          content=\"O sistema de notificações está em desenvolvimento e estará disponível em breve. Você receberá alertas sobre eventos importantes no sistema.\"\r\n          icon=\"Bell\"\r\n        >\r\n          <Bell size={20} aria-hidden=\"true\" />\r\n          <span className=\"absolute top-1 right-1 h-2 w-2 rounded-full bg-primary-500\" aria-hidden=\"true\"></span>\r\n        </ConstructionButton>\r\n\r\n        {/* Botão de configurações */}\r\n        <button\r\n          onClick={() => router.push('/dashboard/admin/settings')}\r\n          className=\"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n          aria-label=\"Configurações\"\r\n        >\r\n          <Settings size={20} aria-hidden=\"true\" />\r\n        </button>\r\n\r\n        {/* Theme Toggle Button */}\r\n        <ThemeToggle />\r\n\r\n        {/* Divisor vertical */}\r\n        <div className=\"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\" aria-hidden=\"true\"></div>\r\n\r\n        {/* Dropdown de usuário */}\r\n        <div className=\"relative group\">\r\n          <button\r\n            className=\"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n            aria-expanded=\"false\"\r\n            aria-haspopup=\"true\"\r\n            aria-label=\"Menu do usuário\"\r\n          >\r\n            <div className=\"h-9 w-9 rounded-full flex items-center justify-center font-medium overflow-hidden\">\r\n              {user?.profileImageFullUrl ? (\r\n                <img\r\n                  src={user.profileImageFullUrl}\r\n                  alt={`Foto de perfil de ${user?.fullName || 'Usuário'}`}\r\n                  className=\"h-10 w-10 rounded-full object-cover\"\r\n                  onError={(e) => {\r\n                    e.target.onerror = null;\r\n                    e.target.style.display = 'none';\r\n                    e.target.parentNode.innerHTML = getInitials();\r\n                  }}\r\n                />\r\n              ) : (\r\n                getInitials()\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"hidden md:block text-left\">\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\">{user?.fullName || 'Usuário'}</p>\r\n              <div className={`text-xs ${roleInfo.textColor} px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 ${roleInfo.bgColor}`}>\r\n                <RoleIcon size={10} className=\"mr-1\" aria-hidden=\"true\" />\r\n                <span>{roleInfo.name}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <ChevronDown size={16} className=\"text-gray-400 dark:text-gray-500 hidden md:block\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Menu dropdown */}\r\n          <div className=\"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\"\r\n               role=\"menu\"\r\n               aria-orientation=\"vertical\"\r\n               aria-labelledby=\"user-menu-button\">\r\n            <div className=\"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\">\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">{user?.fullName || 'Usuário'}</p>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">{user?.email || '<EMAIL>'}</p>\r\n            </div>\r\n\r\n            <div className=\"px-4 py-2\">\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400\">Empresa</p>\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">{user?.company?.name || 'Minha Empresa'}</p>\r\n            </div>\r\n\r\n            <div className=\"border-t border-gray-100 dark:border-gray-700 pt-1 mt-1\">\r\n              <button\r\n                onClick={() => router.push('/dashboard/profile')}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                Meu Perfil\r\n              </button>\r\n              <button\r\n                onClick={logout}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                <LogOut size={14} className=\"mr-2\" aria-hidden=\"true\" />\r\n                Sair do Sistema\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\n// Navigation Link Component - Atualizado para usar o sistema de temas por módulo\r\nconst NavLink = ({ icon: Icon, title, href, isAccessible, moduleId = 'scheduler' }) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Verifica se o link está ativo comparando com o pathname atual\r\n  const isActive = pathname === href;\r\n\r\n  return (\r\n    <button\r\n      onClick={() => router.push(href)}\r\n      disabled={!isAccessible}\r\n      className={`\r\n        w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors\r\n        ${isActive\r\n          ? `bg-module-${moduleId}-bg text-module-${moduleId}-icon`\r\n          : isAccessible\r\n            ? 'text-gray-600 hover:bg-gray-100'\r\n            : 'opacity-50 cursor-not-allowed'\r\n        }\r\n      `}\r\n      aria-current={isActive ? 'page' : undefined}\r\n      role=\"link\"\r\n      aria-disabled={!isAccessible}\r\n    >\r\n      <Icon size={20} aria-hidden=\"true\" />\r\n      <span className=\"font-medium\">{title}</span>\r\n    </button>\r\n  );\r\n};\r\n\r\n// Configuração dos módulos principais - mantida igual\r\nexport const modules = [\r\n  {\r\n    id: 'admin',\r\n    title: 'Administração',\r\n    icon: ShieldIcon,\r\n    description: 'Gerencie todo o sistema, incluindo usuários e configurações.',\r\n    role: 'ADMIN'\r\n  },\r\n  {\r\n    id: 'financial',\r\n    title: 'Financeiro',\r\n    icon: DollarSign,\r\n    description: 'Controle receitas, despesas e gere relatórios financeiros detalhados.',\r\n    role: 'FINANCIAL'\r\n  },\r\n  {\r\n    id: 'hr',\r\n    title: 'RH',\r\n    icon: Users,\r\n    description: 'Gerencie informações de funcionários, admissões e folha de pagamento.',\r\n    role: 'RH'\r\n  },\r\n  {\r\n    id: 'people',\r\n    title: 'Pessoas',\r\n    icon: UserCheck,\r\n    description: 'Cadastre e gerencie informações de pacientes e clientes.',\r\n    role: 'BASIC'\r\n  },\r\n  {\r\n    id: 'scheduler',\r\n    title: 'Agendamento',\r\n    icon: Calendar,\r\n    description: 'Agende e gerencie compromissos, consultas e eventos.',\r\n    role: 'BASIC'\r\n  },\r\n  {\r\n    id: 'abaplus',\r\n    title: 'ABA+',\r\n    icon: Brain,\r\n    description: 'Gerencie programas ABA, acompanhamento terapêutico.',\r\n    role: 'BASIC'\r\n  }\r\n];\r\n\r\n// Submódulos para cada módulo principal - apenas ABA+ com grupos\r\nexport const moduleSubmenus = {\r\n  admin: [\r\n    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de administração' },\r\n    { id: 'users', title: 'Usuários', icon: Users, description: 'Gerenciar usuários do sistema' },\r\n    { id: 'professions', title: 'Profissões', icon: Briefcase, description: 'Gerenciar profissões e grupos' },\r\n    { id: 'settings', title: 'Configurações', icon: Settings, description: 'Configurações gerais do sistema' },\r\n    { id: 'logs', title: 'Logs', icon: FileText, description: 'Histórico de atividades do sistema' },\r\n    { id: 'backup', title: 'Backup', icon: Database, description: 'Gerenciamento de backup dos dados' },\r\n    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Visão geral do sistema' }\r\n  ],\r\n  financial: [\r\n    { id: 'invoices', title: 'Faturas', icon: FileText, description: 'Gerenciar faturas e cobranças' },\r\n    { id: 'payments', title: 'Pagamentos', icon: CreditCard, description: 'Controle de pagamentos' },\r\n    { id: 'expenses', title: 'Despesas', icon: DollarSign, description: 'Gestão de despesas' },\r\n    { id: 'reports', title: 'Relatórios', icon: BarChart, description: 'Relatórios financeiros' },\r\n    { id: 'cashflow', title: 'Fluxo de Caixa', icon: TrendingUp, description: 'Análise de fluxo de caixa' }\r\n  ],\r\n  hr: [\r\n    { id: 'employees', title: 'Funcionários', icon: Users, description: 'Gerenciar funcionários' },\r\n    { id: 'payroll', title: 'Folha de Pagamento', icon: Calculator, description: 'Processamento de salários' },\r\n    { id: 'documents', title: 'Documentos', icon: FileText, description: 'Documentos e formulários de RH' },\r\n    { id: 'departments', title: 'Departamentos', icon: Building, description: 'Gestão de departamentos' },\r\n    { id: 'attendance', title: 'Ponto', icon: Clock, description: 'Controle de ponto e ausências' },\r\n    { id: 'benefits', title: 'Benefícios', icon: Gift, description: 'Gestão de benefícios' },\r\n    { id: 'training', title: 'Treinamentos', icon: GraduationCap, description: 'Gestão de treinamentos' }\r\n  ],\r\n  people: [\r\n    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de pessoas' },\r\n    { id: 'clients', title: 'Clientes', icon: UserPlus, description: 'Gerenciar clientes e contas' },\r\n    { id: 'persons', title: 'Pacientes', icon: Users, description: 'Gerenciar cadastro de pacientes' },\r\n    { id: 'insurances', title: 'Convênios', icon: CreditCard, description: 'Gerenciar convênios associados' },\r\n    { id: 'insurance-limits', title: 'Limites de Convênio', icon: Shield, description: 'Gerenciar limites de serviço por convênio' },\r\n    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise e estatísticas de pessoas' }\r\n  ],\r\n  scheduler: [\r\n    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de agendamento' },\r\n    { id: 'calendar', title: 'Agendar Consulta', icon: Calendar, description: 'Visualizar agenda completa' },\r\n    { id: 'working-hours', title: 'Horários de Trabalho', icon: Clock, description: 'Configurar horários de trabalho' },\r\n    { id: 'service-types', title: 'Tipos de Serviço', icon: Tag, description: 'Gerenciar tipos de serviço' },\r\n    { id: 'locations', title: 'Localizações', icon: MapPin, description: 'Gerenciar localizações e endereços' },\r\n    { id: 'occupancy', title: 'Ocupação', icon: Briefcase, description: 'Análise detalhada da ocupação dos profissionais' },\r\n    { id: 'appointments-report', title: 'Relatório', icon: FileText, description: 'Gerenciar agendamentos em formato de lista' },\r\n    { id: 'appointments-dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise de agendamentos e estatísticas' }\r\n  ],\r\n  abaplus: [\r\n    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo ABA+' },\r\n    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise e estatísticas ABA+' },\r\n    {\r\n      id: 'cadastro',\r\n      title: 'Cadastro',\r\n      type: 'group',\r\n      items: [\r\n        { id: 'skills', title: 'Habilidades', icon: Activity, description: 'Gerenciar habilidades e competências' },\r\n        { id: 'programs', title: 'Programas', icon: BookOpen, description: 'Gerenciar programas terapêuticos' },\r\n        { id: 'evaluations', title: 'Avaliações', icon: ClipboardList, description: 'Gerenciar avaliações e protocolos' },\r\n        { id: 'standard-criteria', title: 'Critérios Padrão', icon: Award, description: 'Gerenciar critérios padrão' },\r\n        { id: 'curriculum-folders', title: 'Pastas Curriculares', icon: FileText, description: 'Gerenciar pastas curriculares dos aprendizes' },\r\n      ]\r\n    },\r\n    {\r\n      id: 'atendimento',\r\n      title: 'Atendimento',\r\n      type: 'group',\r\n      items: [\r\n        { id: 'anamnese', title: 'Anamnese', icon: ClipboardList, description: 'Gerenciar anamneses dos pacientes' },\r\n        { id: 'evolucoes-diarias', title: 'Evoluções Diárias', icon: FileText, description: 'Gerenciar evoluções diárias dos atendimentos' },\r\n        { id: 'sessao', title: 'Sessão', icon: Calendar, description: 'Gerenciar sessões de atendimento' },\r\n      ]\r\n    }\r\n  ]\r\n};\r\n\r\nexport { Header, NavLink };"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AADA;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;;;;;;;;AAoBA,mBAAmB;AACnB,MAAM,SAAS,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE;;IAC9C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAEnC,gEAAgE;IAChE,MAAM,cAAc;QAClB,OAAQ,MAAM;YACZ,KAAK;gBACH,OAAO;oBACL,MAAM,uNAAA,CAAA,cAAW;oBACjB,SAAS;oBACT,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,yMAAA,CAAA,SAAM;oBACZ,SAAS;oBACT,WAAW;oBACX,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,MAAM,+MAAA,CAAA,UAAO;oBACb,SAAS;oBACT,WAAW;oBACX,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,WAAW;IACjB,MAAM,WAAW,SAAS,IAAI;IAE9B,kDAAkD;IAClD,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,UAAU,OAAO;QAE5B,MAAM,QAAQ,KAAK,QAAQ,CAAC,KAAK,CAAC;QAClC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;QAE/C,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;IACpE;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAY,gBAAgB,wBAAwB;kCAEnD,8BAAgB,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;iDAAY,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAGpF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAiF,6HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMlH,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;;0CAEX,6LAAC,yMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;gCAAmC,eAAY;;;;;;0CAC3E,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAKV,6LAAC,mLAAA,CAAA,aAAU;;;;;kCAEX,6LAAC,2MAAA,CAAA,qBAAkB;wBACjB,WAAU;wBACV,cAAW;wBACX,OAAM;wBACN,SAAQ;wBACR,MAAK;;0CAEL,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,eAAY;;;;;;0CAC5B,6LAAC;gCAAK,WAAU;gCAA6D,eAAY;;;;;;;;;;;;kCAI3F,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAIlC,6LAAC,mIAAA,CAAA,cAAW;;;;;kCAGZ,6LAAC;wBAAI,WAAU;wBAAyD,eAAY;;;;;;kCAGpF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,iBAAc;gCACd,iBAAc;gCACd,cAAW;;kDAEX,6LAAC;wCAAI,WAAU;kDACZ,MAAM,oCACL,6LAAC;4CACC,KAAK,KAAK,mBAAmB;4CAC7B,KAAK,CAAC,kBAAkB,EAAE,MAAM,YAAY,WAAW;4CACvD,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,MAAM,CAAC,OAAO,GAAG;gDACnB,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;gDACzB,EAAE,MAAM,CAAC,UAAU,CAAC,SAAS,GAAG;4CAClC;;;;;mDAGF;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqE,MAAM,YAAY;;;;;;0DACpG,6LAAC;gDAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,SAAS,CAAC,0DAA0D,EAAE,SAAS,OAAO,EAAE;;kEAC1H,6LAAC;wDAAS,MAAM;wDAAI,WAAU;wDAAO,eAAY;;;;;;kEACjD,6LAAC;kEAAM,SAAS,IAAI;;;;;;;;;;;;;;;;;;kDAIxB,6LAAC,uNAAA,CAAA,cAAW;wCAAC,MAAM;wCAAI,WAAU;wCAAmD,eAAY;;;;;;;;;;;;0CAIlG,6LAAC;gCAAI,WAAU;gCACV,MAAK;gCACL,oBAAiB;gCACjB,mBAAgB;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAwD,MAAM,YAAY;;;;;;0DACvF,6LAAC;gDAAE,WAAU;0DAAqD,MAAM,SAAS;;;;;;;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DACxD,6LAAC;gDAAE,WAAU;0DAAwD,MAAM,SAAS,QAAQ;;;;;;;;;;;;kDAG9F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;gDACV,MAAK;0DACN;;;;;;0DAGD,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,MAAK;;kEAEL,6LAAC,6MAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;wDAAO,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GA1LM;;QACqB,iIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACC,qIAAA,CAAA,cAAW;;;KAHhC;AA4LN,iFAAiF;AACjF,MAAM,UAAU,CAAC,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,WAAW,EAAE;;IAChF,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,aAAa;IAE9B,qBACE,6LAAC;QACC,SAAS,IAAM,OAAO,IAAI,CAAC;QAC3B,UAAU,CAAC;QACX,WAAW,CAAC;;QAEV,EAAE,WACE,CAAC,UAAU,EAAE,SAAS,gBAAgB,EAAE,SAAS,KAAK,CAAC,GACvD,eACE,oCACA,gCACL;MACH,CAAC;QACD,gBAAc,WAAW,SAAS;QAClC,MAAK;QACL,iBAAe,CAAC;;0BAEhB,6LAAC;gBAAK,MAAM;gBAAI,eAAY;;;;;;0BAC5B,6LAAC;gBAAK,WAAU;0BAAe;;;;;;;;;;;;AAGrC;IA5BM;;QACW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;MAFxB;AA+BC,MAAM,UAAU;IACrB;QACE,IAAI;QACJ,OAAO;QACP,MAAM,6MAAA,CAAA,aAAU;QAChB,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,qNAAA,CAAA,aAAU;QAChB,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,mNAAA,CAAA,YAAS;QACf,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,iBAAiB;IAC5B,OAAO;QACL;YAAE,IAAI;YAAgB,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAAyC;QAC7G;YAAE,IAAI;YAAS,OAAO;YAAY,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAgC;QAC5F;YAAE,IAAI;YAAe,OAAO;YAAc,MAAM,+MAAA,CAAA,YAAS;YAAE,aAAa;QAAgC;QACxG;YAAE,IAAI;YAAY,OAAO;YAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAE,aAAa;QAAkC;QACzG;YAAE,IAAI;YAAQ,OAAO;YAAQ,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAAqC;QAC/F;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6MAAA,CAAA,WAAQ;YAAE,aAAa;QAAoC;QAClG;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,+NAAA,CAAA,kBAAe;YAAE,aAAa;QAAyB;KACrG;IACD,WAAW;QACT;YAAE,IAAI;YAAY,OAAO;YAAW,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAAgC;QACjG;YAAE,IAAI;YAAY,OAAO;YAAc,MAAM,qNAAA,CAAA,aAAU;YAAE,aAAa;QAAyB;QAC/F;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qNAAA,CAAA,aAAU;YAAE,aAAa;QAAqB;QACzF;YAAE,IAAI;YAAW,OAAO;YAAc,MAAM,gPAAA,CAAA,WAAQ;YAAE,aAAa;QAAyB;QAC5F;YAAE,IAAI;YAAY,OAAO;YAAkB,MAAM,qNAAA,CAAA,aAAU;YAAE,aAAa;QAA4B;KACvG;IACD,IAAI;QACF;YAAE,IAAI;YAAa,OAAO;YAAgB,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAyB;QAC7F;YAAE,IAAI;YAAW,OAAO;YAAsB,MAAM,iNAAA,CAAA,aAAU;YAAE,aAAa;QAA4B;QACzG;YAAE,IAAI;YAAa,OAAO;YAAc,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAAiC;QACtG;YAAE,IAAI;YAAe,OAAO;YAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAE,aAAa;QAA0B;QACpG;YAAE,IAAI;YAAc,OAAO;YAAS,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAgC;QAC9F;YAAE,IAAI;YAAY,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAAuB;QACvF;YAAE,IAAI;YAAY,OAAO;YAAgB,MAAM,2NAAA,CAAA,gBAAa;YAAE,aAAa;QAAyB;KACrG;IACD,QAAQ;QACN;YAAE,IAAI;YAAgB,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAAmC;QACvG;YAAE,IAAI;YAAW,OAAO;YAAY,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAA8B;QAC/F;YAAE,IAAI;YAAW,OAAO;YAAa,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAkC;QACjG;YAAE,IAAI;YAAc,OAAO;YAAa,MAAM,qNAAA,CAAA,aAAU;YAAE,aAAa;QAAiC;QACxG;YAAE,IAAI;YAAoB,OAAO;YAAuB,MAAM,yMAAA,CAAA,SAAM;YAAE,aAAa;QAA4C;QAC/H;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,+NAAA,CAAA,kBAAe;YAAE,aAAa;QAAoC;KAChH;IACD,WAAW;QACT;YAAE,IAAI;YAAgB,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAAuC;QAC3G;YAAE,IAAI;YAAY,OAAO;YAAoB,MAAM,6MAAA,CAAA,WAAQ;YAAE,aAAa;QAA6B;QACvG;YAAE,IAAI;YAAiB,OAAO;YAAwB,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAkC;QAClH;YAAE,IAAI;YAAiB,OAAO;YAAoB,MAAM,mMAAA,CAAA,MAAG;YAAE,aAAa;QAA6B;QACvG;YAAE,IAAI;YAAa,OAAO;YAAgB,MAAM,6MAAA,CAAA,SAAM;YAAE,aAAa;QAAqC;QAC1G;YAAE,IAAI;YAAa,OAAO;YAAY,MAAM,+MAAA,CAAA,YAAS;YAAE,aAAa;QAAkD;QACtH;YAAE,IAAI;YAAuB,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;YAAE,aAAa;QAA6C;QAC3H;YAAE,IAAI;YAA0B,OAAO;YAAa,MAAM,+NAAA,CAAA,kBAAe;YAAE,aAAa;QAAyC;KAClI;IACD,SAAS;QACP;YAAE,IAAI;YAAgB,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAA6B;QACjG;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,+NAAA,CAAA,kBAAe;YAAE,aAAa;QAA8B;QACzG;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBAAE,IAAI;oBAAU,OAAO;oBAAe,MAAM,6MAAA,CAAA,WAAQ;oBAAE,aAAa;gBAAuC;gBAC1G;oBAAE,IAAI;oBAAY,OAAO;oBAAa,MAAM,iNAAA,CAAA,WAAQ;oBAAE,aAAa;gBAAmC;gBACtG;oBAAE,IAAI;oBAAe,OAAO;oBAAc,MAAM,2NAAA,CAAA,gBAAa;oBAAE,aAAa;gBAAoC;gBAChH;oBAAE,IAAI;oBAAqB,OAAO;oBAAoB,MAAM,uMAAA,CAAA,QAAK;oBAAE,aAAa;gBAA6B;gBAC7G;oBAAE,IAAI;oBAAsB,OAAO;oBAAuB,MAAM,iNAAA,CAAA,WAAQ;oBAAE,aAAa;gBAA+C;aACvI;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBAAE,IAAI;oBAAY,OAAO;oBAAY,MAAM,2NAAA,CAAA,gBAAa;oBAAE,aAAa;gBAAoC;gBAC3G;oBAAE,IAAI;oBAAqB,OAAO;oBAAqB,MAAM,iNAAA,CAAA,WAAQ;oBAAE,aAAa;gBAA+C;gBACnI;oBAAE,IAAI;oBAAU,OAAO;oBAAU,MAAM,6MAAA,CAAA,WAAQ;oBAAE,aAAa;gBAAmC;aAClG;QACH;KACD;AACH"}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/services/exportService.js"], "sourcesContent": ["// services/exportService.js\r\nimport { saveAs } from \"file-saver\";\r\nimport { utils, write } from \"xlsx\";\r\nimport { jsPDF } from \"jspdf\";\r\nimport autoTable from \"jspdf-autotable\"; // Alterado para importar como função\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { api } from \"@/utils/api\";\r\nimport html2canvas from \"html2canvas\";\r\n\r\n// Formatação para campos comuns\r\nconst formatters = {\r\n  // Formata datas para o padrão brasileiro\r\n  date: (value) => {\r\n    if (!value) return \"\";\r\n    try {\r\n      return format(new Date(value), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return value;\r\n    }\r\n  },\r\n\r\n  // Formata CPF (000.000.000-00)\r\n  cpf: (value) => {\r\n    if (!value) return \"\";\r\n    const cpfNumbers = value.replace(/\\D/g, \"\");\r\n    return cpfNumbers.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n  },\r\n\r\n  // Formata telefone (00) 00000-0000\r\n  phone: (value) => {\r\n    if (!value) return \"\";\r\n    const phoneNumbers = value.replace(/\\D/g, \"\");\r\n    return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n  },\r\n\r\n  // Formata valores booleanos\r\n  boolean: (value, options = { trueText: \"Sim\", falseText: \"Não\" }) => {\r\n    return value ? options.trueText : options.falseText;\r\n  },\r\n\r\n  // Formata valores monetários\r\n  currency: (value) => {\r\n    if (value === null || value === undefined) return \"\";\r\n    return new Intl.NumberFormat(\"pt-BR\", {\r\n      style: \"currency\",\r\n      currency: \"BRL\",\r\n    }).format(value);\r\n  }\r\n};\r\n\r\nexport const exportService = {\r\n  /**\r\n   * Exporta dados para XLSX ou PDF\r\n   * @param {Object[]|Object} data - Dados a serem exportados (array de objetos para tabela única ou objeto com arrays para múltiplas tabelas)\r\n   * @param {Object} options - Opções de exportação\r\n   * @param {string} options.format - Formato de exportação ('xlsx', 'pdf' ou 'image')\r\n   * @param {string} options.filename - Nome do arquivo sem extensão\r\n   * @param {Object[]} options.columns - Definição das colunas (para tabela única)\r\n   * @param {Object} options.formatOptions - Opções adicionais de formatação\r\n   * @param {string} options.title - Título principal do documento\r\n   * @param {string} options.subtitle - Subtítulo do documento\r\n   * @param {boolean} options.multiTable - Indica se os dados contêm múltiplas tabelas\r\n   * @param {Object[]} options.tables - Definição das tabelas para exportação múltipla\r\n   * @param {string} options.tables[].name - Nome da propriedade no objeto data que contém os dados da tabela\r\n   * @param {string} options.tables[].title - Título da tabela\r\n   * @param {Object[]} options.tables[].columns - Definição das colunas para esta tabela\r\n   * @returns {Promise<boolean>} - Sucesso da exportação\r\n   */\r\n  exportData: async (data, options = {}) => {\r\n    try {\r\n      const {\r\n        format = \"xlsx\",\r\n        filename = \"export\",\r\n        columns = [],\r\n        formatOptions = {},\r\n        title = null,\r\n        subtitle = null,\r\n        multiTable = false,\r\n        tables = []\r\n      } = options;\r\n\r\n      // Verifica se estamos lidando com múltiplas tabelas\r\n      if (multiTable && !Array.isArray(data)) {\r\n        // Caso de múltiplas tabelas (objeto com arrays)\r\n\r\n        if (format === \"xlsx\") {\r\n          // Criar um workbook para múltiplas tabelas\r\n          const workbook = utils.book_new();\r\n\r\n          // Data atual formatada para o cabeçalho\r\n          const currentDate = new Date();\r\n          const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n          // Processar cada tabela definida\r\n          for (const table of tables) {\r\n            const tableData = data[table.name];\r\n\r\n            if (!tableData || !Array.isArray(tableData)) {\r\n              console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);\r\n              continue;\r\n            }\r\n\r\n            // Formatar os dados desta tabela\r\n            const formattedTableData = tableData.map(item => {\r\n              const formattedItem = {};\r\n\r\n              table.columns.forEach(col => {\r\n                let value = item[col.key];\r\n\r\n                // Aplica formatação personalizada se especificada\r\n                if (col.format && typeof col.format === 'function') {\r\n                  formattedItem[col.key] = col.format(value, item);\r\n                }\r\n                // Aplica formatação padrão se especificada\r\n                else if (col.type && formatters[col.type]) {\r\n                  formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\r\n                }\r\n                // Sem formatação\r\n                else {\r\n                  formattedItem[col.key] = value !== null && value !== undefined ? value : '';\r\n                }\r\n              });\r\n\r\n              return formattedItem;\r\n            });\r\n\r\n            // Preparar dados para a worksheet\r\n            let worksheetData = [];\r\n\r\n            // Adiciona título e subtítulo se existirem\r\n            if (title) {\r\n              worksheetData.push([title]);\r\n              if (table.title) {\r\n                worksheetData.push([table.title]);\r\n              }\r\n              worksheetData.push([`Gerado em: ${formattedDate}`]);\r\n              if (subtitle) {\r\n                worksheetData.push([subtitle]);\r\n              }\r\n              worksheetData.push([]); // Linha em branco\r\n            }\r\n\r\n            // Adiciona os cabeçalhos\r\n            const headers = table.columns.map(col => col.header || col.key);\r\n            worksheetData.push(headers);\r\n\r\n            // Adiciona os dados\r\n            formattedTableData.forEach(item => {\r\n              const row = table.columns.map(col => item[col.key]);\r\n              worksheetData.push(row);\r\n            });\r\n\r\n            // Cria a worksheet\r\n            const worksheet = utils.aoa_to_sheet(worksheetData);\r\n\r\n            // Configura os estilos\r\n            if (title) {\r\n              // Mescla células para o título\r\n              worksheet['!merges'] = [\r\n                { s: {r: 0, c: 0}, e: {r: 0, c: headers.length - 1} }, // Título\r\n              ];\r\n\r\n              if (table.title) {\r\n                worksheet['!merges'].push(\r\n                  { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} } // Subtítulo da tabela\r\n                );\r\n                worksheet['!merges'].push(\r\n                  { s: {r: 2, c: 0}, e: {r: 2, c: headers.length - 1} } // Data\r\n                );\r\n              } else {\r\n                worksheet['!merges'].push(\r\n                  { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} } // Data\r\n                );\r\n              }\r\n\r\n              if (subtitle) {\r\n                worksheet['!merges'].push(\r\n                  { s: {r: table.title ? 3 : 2, c: 0}, e: {r: table.title ? 3 : 2, c: headers.length - 1} } // Subtítulo\r\n                );\r\n              }\r\n\r\n              // Ajusta largura das colunas\r\n              if (!worksheet['!cols']) worksheet['!cols'] = [];\r\n              table.columns.forEach((col, idx) => {\r\n                // Calcula largura ideal para cada coluna\r\n                const headerWidth = (col.header || col.key).length * 1.2;\r\n                let maxDataWidth = 0;\r\n\r\n                // Verifica o tamanho máximo dos dados em cada coluna\r\n                formattedTableData.forEach(item => {\r\n                  const cellValue = item[col.key];\r\n                  const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';\r\n                  maxDataWidth = Math.max(maxDataWidth, cellText.length);\r\n                });\r\n\r\n                worksheet['!cols'][idx] = {\r\n                  wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))\r\n                };\r\n              });\r\n            }\r\n\r\n            // Limita o nome da planilha a 31 caracteres (limite do Excel)\r\n            let sheetName = table.title || table.name;\r\n\r\n            // Se o nome for muito longo, cria um nome curto\r\n            if (sheetName.length > 31) {\r\n              // Tenta usar apenas a primeira palavra do título\r\n              const firstWord = sheetName.split(' ')[0];\r\n              if (firstWord.length <= 28) {\r\n                sheetName = firstWord + \"...\";\r\n              } else {\r\n                // Se ainda for muito longo, trunca para 28 caracteres e adiciona \"...\"\r\n                sheetName = sheetName.substring(0, 28) + \"...\";\r\n              }\r\n            }\r\n\r\n            // Adiciona a worksheet ao workbook\r\n            utils.book_append_sheet(workbook, worksheet, sheetName);\r\n          }\r\n\r\n          try {\r\n            // Verifica se há pelo menos uma planilha no workbook\r\n            if (workbook.SheetNames && workbook.SheetNames.length > 0) {\r\n              // Verifica se todos os nomes de planilhas estão dentro do limite\r\n              let allNamesValid = true;\r\n              for (const sheetName of workbook.SheetNames) {\r\n                if (sheetName.length > 31) {\r\n                  console.error(`Nome de planilha muito longo: \"${sheetName}\" (${sheetName.length} caracteres)`);\r\n                  allNamesValid = false;\r\n                  break;\r\n                }\r\n              }\r\n\r\n              if (!allNamesValid) {\r\n                // Criar uma planilha padrão com mensagem de erro\r\n                workbook.SheetNames = []; // Limpa as planilhas existentes\r\n                workbook.Sheets = {};     // Limpa as planilhas existentes\r\n\r\n                const worksheet = utils.aoa_to_sheet([\r\n                  [\"Erro na exportação\"],\r\n                  [\"Um ou mais nomes de planilhas excedem o limite de 31 caracteres\"],\r\n                  [\"Por favor, contate o suporte técnico\"]\r\n                ]);\r\n\r\n                utils.book_append_sheet(workbook, worksheet, \"Erro\");\r\n              }\r\n\r\n              // Converte para binário e salva\r\n              const excelBuffer = write(workbook, {\r\n                bookType: \"xlsx\",\r\n                type: \"array\",\r\n                bookSST: false,\r\n                compression: true\r\n              });\r\n\r\n              const blob = new Blob([excelBuffer], { type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\" });\r\n              saveAs(blob, `${filename}.xlsx`);\r\n\r\n              return allNamesValid;\r\n            } else {\r\n              console.error(\"Nenhuma planilha foi criada no workbook\");\r\n\r\n              // Criar uma planilha padrão com mensagem de erro\r\n              const worksheet = utils.aoa_to_sheet([\r\n                [\"Erro na exportação\"],\r\n                [\"Não foi possível gerar as planilhas com os dados fornecidos\"],\r\n                [\"Por favor, tente novamente ou contate o suporte\"]\r\n              ]);\r\n\r\n              utils.book_append_sheet(workbook, worksheet, \"Erro\");\r\n\r\n              const excelBuffer = write(workbook, {\r\n                bookType: \"xlsx\",\r\n                type: \"array\",\r\n                bookSST: false,\r\n                compression: true\r\n              });\r\n\r\n              const blob = new Blob([excelBuffer], { type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\" });\r\n              saveAs(blob, `${filename}.xlsx`);\r\n\r\n              return false;\r\n            }\r\n          } catch (error) {\r\n            console.error(\"Erro ao gerar arquivo Excel:\", error);\r\n\r\n            try {\r\n              // Tentar criar um arquivo de erro\r\n              const workbook = utils.book_new();\r\n              const worksheet = utils.aoa_to_sheet([\r\n                [\"Erro na exportação\"],\r\n                [\"Ocorreu um erro ao gerar o arquivo Excel\"],\r\n                [\"Detalhes do erro: \" + (error.message || \"Erro desconhecido\")],\r\n                [\"Por favor, tente novamente ou contate o suporte\"]\r\n              ]);\r\n\r\n              utils.book_append_sheet(workbook, worksheet, \"Erro\");\r\n\r\n              const excelBuffer = write(workbook, {\r\n                bookType: \"xlsx\",\r\n                type: \"array\",\r\n                bookSST: false,\r\n                compression: true\r\n              });\r\n\r\n              const blob = new Blob([excelBuffer], { type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\" });\r\n              saveAs(blob, `${filename}-erro.xlsx`);\r\n            } catch (fallbackError) {\r\n              console.error(\"Erro ao gerar arquivo de erro:\", fallbackError);\r\n            }\r\n\r\n            return false;\r\n          }\r\n        }\r\n        // Exportação em formato PDF para múltiplas tabelas\r\n        else if (format === \"pdf\") {\r\n          // Cria um novo documento PDF no tamanho A4\r\n          const doc = new jsPDF({\r\n            orientation: \"portrait\",\r\n            unit: \"mm\",\r\n            format: \"a4\"\r\n          });\r\n\r\n          // Configurações da página\r\n          const pageWidth = doc.internal.pageSize.width;\r\n          const pageHeight = doc.internal.pageSize.height;\r\n          const margin = 10;\r\n\r\n          // Define cores do tema\r\n          const themeColors = {\r\n            primary: {\r\n              light: [255, 153, 51],    // #FF9933 (primary-500)\r\n              dark: [255, 127, 0],       // #FF7F00 (primary-600)\r\n              text: [255, 255, 255]      // #FFFFFF (texto branco)\r\n            },\r\n            secondary: {\r\n              light: [255, 237, 213],    // #FFEDD5 (orange-100)\r\n              dark: [154, 52, 18],       // #9A3412 (orange-800)\r\n              border: [251, 146, 60]     // #FB923C (orange-400)\r\n            }\r\n          };\r\n\r\n          // Desenha cabeçalho com título principal\r\n          const headerHeight = subtitle ? 30 : 25;\r\n\r\n          // Simulando um gradiente com múltiplos retângulos coloridos\r\n          const gradientSteps = 20;\r\n          const stepHeight = headerHeight / gradientSteps;\r\n\r\n          for (let i = 0; i < gradientSteps; i++) {\r\n            const ratio = i / gradientSteps;\r\n            // Interpola as cores para criar efeito de gradiente\r\n            const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);\r\n            const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);\r\n            const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);\r\n\r\n            doc.setFillColor(r, g, b);\r\n            doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');\r\n          }\r\n\r\n          // Adiciona título no cabeçalho\r\n          if (title) {\r\n            doc.setFont(\"helvetica\", \"bold\");\r\n            doc.setFontSize(18);\r\n            doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho\r\n            doc.text(title, pageWidth / 2, 15, { align: \"center\" });\r\n          }\r\n\r\n          // Adiciona subtítulo no cabeçalho se existir\r\n          if (subtitle) {\r\n            doc.setFont(\"helvetica\", \"normal\");\r\n            doc.setFontSize(10);\r\n            doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência\r\n            doc.text(subtitle, pageWidth / 2, 22, { align: \"center\" });\r\n          }\r\n\r\n          // Data de geração do relatório\r\n          const currentDate = new Date();\r\n          const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n          doc.setFontSize(8);\r\n          doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, { align: \"right\" });\r\n\r\n          // Posição vertical atual para adicionar tabelas\r\n          let yPosition = headerHeight + 10;\r\n\r\n          // Processar cada tabela\r\n          for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {\r\n            const table = tables[tableIndex];\r\n            const tableData = data[table.name];\r\n\r\n            if (!tableData || !Array.isArray(tableData)) {\r\n              console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);\r\n              continue;\r\n            }\r\n\r\n            // Formatar os dados desta tabela\r\n            const formattedTableData = tableData.map(item => {\r\n              const formattedItem = {};\r\n\r\n              table.columns.forEach(col => {\r\n                let value = item[col.key];\r\n\r\n                // Aplica formatação personalizada se especificada\r\n                if (col.format && typeof col.format === 'function') {\r\n                  formattedItem[col.key] = col.format(value, item);\r\n                }\r\n                // Aplica formatação padrão se especificada\r\n                else if (col.type && formatters[col.type]) {\r\n                  formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\r\n                }\r\n                // Sem formatação\r\n                else {\r\n                  formattedItem[col.key] = value !== null && value !== undefined ? value : '';\r\n                }\r\n              });\r\n\r\n              return formattedItem;\r\n            });\r\n\r\n            // Adiciona título da tabela\r\n            if (table.title) {\r\n              // Verifica se precisa adicionar uma nova página\r\n              if (yPosition > pageHeight - 40) {\r\n                doc.addPage();\r\n                yPosition = 20;\r\n              }\r\n\r\n              doc.setFont(\"helvetica\", \"bold\");\r\n              doc.setFontSize(14);\r\n              doc.setTextColor(50, 50, 50);\r\n              doc.text(table.title, margin, yPosition);\r\n              yPosition += 10;\r\n            }\r\n\r\n            // Prepara os dados para a tabela\r\n            const headers = table.columns.map(col => col.header || col.key);\r\n            const rows = formattedTableData.map(item => {\r\n              return table.columns.map(col => item[col.key]);\r\n            });\r\n\r\n            // Prepara cabeçalhos mais curtos para prevenir quebras\r\n            const shortHeaders = headers.map(header => {\r\n              // Substituições específicas para cabeçalhos problemáticos\r\n              const replacements = {\r\n                'Nome Completo': 'Nome',\r\n                'Data de Nascimento': 'Nascimento',\r\n                'Data de Cadastro': 'Cadastro',\r\n                'Relacionamento': 'Relação',\r\n                'Telefone': 'Telefone'\r\n              };\r\n\r\n              return replacements[header] || header;\r\n            });\r\n\r\n            // Adiciona a tabela\r\n            autoTable(doc, {\r\n              startY: yPosition,\r\n              head: [shortHeaders],\r\n              body: rows,\r\n              theme: \"grid\",\r\n              headStyles: {\r\n                fillColor: themeColors.primary.dark,\r\n                textColor: themeColors.primary.text,\r\n                fontStyle: \"bold\",\r\n                halign: \"center\",\r\n                fontSize: 9,\r\n                cellPadding: { top: 3, right: 2, bottom: 3, left: 2 },\r\n                lineWidth: 0.1,\r\n                minCellWidth: 15,\r\n                overflow: 'linebreak'\r\n              },\r\n              styles: {\r\n                fontSize: 9,\r\n                cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },\r\n                overflow: \"linebreak\",\r\n                lineColor: [220, 220, 220],\r\n                lineWidth: 0.1\r\n              },\r\n              tableWidth: 'auto',\r\n              bodyStyles: {\r\n                minCellHeight: 10\r\n              },\r\n              alternateRowStyles: {\r\n                fillColor: [252, 252, 252]\r\n              },\r\n              columnStyles: table.columns.reduce((styles, col, index) => {\r\n                // Definir larguras mínimas específicas para evitar quebras nos títulos\r\n                const minWidths = {\r\n                  'fullName': 30,\r\n                  'cpf': 20,\r\n                  'email': 28,\r\n                  'phone': 20,\r\n                  'birthDate': 20,\r\n                  'gender': 15,\r\n                  'relationship': 20,\r\n                  'createdAt': 20,\r\n                  'active': 15\r\n                };\r\n\r\n                // Define a largura baseada na configuração ou no mínimo predefinido\r\n                if (col.key && minWidths[col.key]) {\r\n                  styles[index] = {\r\n                    ...styles[index],\r\n                    cellWidth: minWidths[col.key],\r\n                    overflow: 'linebreak'\r\n                  };\r\n                } else if (col.width) {\r\n                  styles[index] = {\r\n                    ...styles[index],\r\n                    cellWidth: col.width\r\n                  };\r\n                }\r\n\r\n                // Aplica o alinhamento se definido\r\n                if (col.align) {\r\n                  styles[index] = {\r\n                    ...styles[index],\r\n                    halign: col.align\r\n                  };\r\n                }\r\n\r\n                return styles;\r\n              }, {}),\r\n              margin: { top: yPosition, left: margin, right: margin, bottom: margin + 15 },\r\n              didDrawPage: function(data) {\r\n                // Adiciona rodapé colorido em cada página\r\n                doc.setFillColor(240, 240, 240);\r\n                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');\r\n\r\n                // Linha sutil acima do rodapé\r\n                doc.setDrawColor(200, 200, 200);\r\n                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);\r\n\r\n                // Adiciona numeração de páginas no rodapé\r\n                doc.setFontSize(8);\r\n                doc.setTextColor(100, 100, 100);\r\n                doc.text(\r\n                  `Página ${data.pageNumber} de ${data.pageCount}`,\r\n                  pageWidth - margin - 2,\r\n                  pageHeight - 5,\r\n                  { align: \"right\" }\r\n                );\r\n\r\n                // Adiciona nome do sistema no rodapé\r\n                doc.setTextColor(80, 80, 80);\r\n                doc.setFontSize(8);\r\n                doc.text(\"High Tide Systems\", margin + 2, pageHeight - 5);\r\n              }\r\n            });\r\n\r\n            // Atualiza a posição Y para a próxima tabela\r\n            // Obtém a última posição Y após desenhar a tabela\r\n            yPosition = doc.lastAutoTable.finalY + 15;\r\n          }\r\n\r\n          // Salva o documento\r\n          doc.save(`${filename}.pdf`);\r\n\r\n          return true;\r\n        }\r\n        // Exportação em formato de imagem (PNG) para múltiplas tabelas\r\n        else if (format === \"image\") {\r\n          try {\r\n            // Criar um elemento temporário para renderizar todas as tabelas\r\n            const tempContainer = document.createElement('div');\r\n            tempContainer.style.position = 'absolute';\r\n            tempContainer.style.left = '-9999px';\r\n            tempContainer.style.top = '-9999px';\r\n            tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade\r\n            tempContainer.style.backgroundColor = '#ffffff';\r\n            tempContainer.style.padding = '20px';\r\n            tempContainer.style.fontFamily = 'Arial, sans-serif';\r\n            tempContainer.style.color = '#333333';\r\n            tempContainer.style.boxSizing = 'border-box';\r\n\r\n            // Adicionar título principal e subtítulo\r\n            if (title) {\r\n              const titleElement = document.createElement('h1');\r\n              titleElement.textContent = title;\r\n              titleElement.style.color = '#FF7F00'; // Cor laranja do tema\r\n              titleElement.style.marginBottom = '5px';\r\n              titleElement.style.fontSize = '28px';\r\n              titleElement.style.fontWeight = 'bold';\r\n              titleElement.style.textAlign = 'center';\r\n              tempContainer.appendChild(titleElement);\r\n            }\r\n\r\n            if (subtitle) {\r\n              const subtitleElement = document.createElement('p');\r\n              subtitleElement.textContent = subtitle;\r\n              subtitleElement.style.color = '#666666';\r\n              subtitleElement.style.marginBottom = '20px';\r\n              subtitleElement.style.fontSize = '16px';\r\n              subtitleElement.style.textAlign = 'center';\r\n              tempContainer.appendChild(subtitleElement);\r\n            }\r\n\r\n            // Data atual formatada\r\n            const currentDate = new Date();\r\n            const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n            const dateElement = document.createElement('p');\r\n            dateElement.textContent = `Gerado em: ${formattedDate}`;\r\n            dateElement.style.color = '#666666';\r\n            dateElement.style.marginBottom = '30px';\r\n            dateElement.style.fontSize = '12px';\r\n            dateElement.style.textAlign = 'center';\r\n            tempContainer.appendChild(dateElement);\r\n\r\n            // Processar cada tabela\r\n            for (const table of tables) {\r\n              const tableData = data[table.name];\r\n\r\n              if (!tableData || !Array.isArray(tableData)) {\r\n                console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);\r\n                continue;\r\n              }\r\n\r\n              // Adicionar título da seção\r\n              const sectionTitle = document.createElement('h2');\r\n              sectionTitle.textContent = table.title || table.name;\r\n              sectionTitle.style.color = '#FF7F00'; // Cor laranja do tema\r\n              sectionTitle.style.marginTop = '30px';\r\n              sectionTitle.style.marginBottom = '15px';\r\n              sectionTitle.style.fontSize = '20px';\r\n              sectionTitle.style.fontWeight = 'bold';\r\n              tempContainer.appendChild(sectionTitle);\r\n\r\n              // Criar a tabela\r\n              const tableElement = document.createElement('table');\r\n              tableElement.style.width = '100%';\r\n              tableElement.style.borderCollapse = 'collapse';\r\n              tableElement.style.marginBottom = '30px';\r\n              tableElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';\r\n\r\n              // Criar o cabeçalho da tabela\r\n              const thead = document.createElement('thead');\r\n              const headerRow = document.createElement('tr');\r\n              headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema\r\n              headerRow.style.color = '#ffffff';\r\n\r\n              table.columns.forEach(col => {\r\n                const th = document.createElement('th');\r\n                th.textContent = col.header || col.key;\r\n                th.style.padding = '10px';\r\n                th.style.textAlign = col.align || 'left';\r\n                th.style.fontWeight = 'bold';\r\n                th.style.fontSize = '14px';\r\n                th.style.borderBottom = '2px solid #FF9933';\r\n                headerRow.appendChild(th);\r\n              });\r\n\r\n              thead.appendChild(headerRow);\r\n              tableElement.appendChild(thead);\r\n\r\n              // Criar o corpo da tabela\r\n              const tbody = document.createElement('tbody');\r\n\r\n              tableData.forEach((item, rowIndex) => {\r\n                const row = document.createElement('tr');\r\n                row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';\r\n                row.style.borderBottom = '1px solid #eeeeee';\r\n\r\n                table.columns.forEach(col => {\r\n                  const td = document.createElement('td');\r\n\r\n                  // Formatar o valor da célula\r\n                  let value = item[col.key];\r\n\r\n                  // Aplica formatação personalizada se especificada\r\n                  if (col.format && typeof col.format === 'function') {\r\n                    value = col.format(value, item);\r\n                  }\r\n                  // Aplica formatação padrão se especificada\r\n                  else if (col.type && formatters[col.type]) {\r\n                    value = formatters[col.type](value, {});\r\n                  }\r\n\r\n                  td.textContent = value !== undefined && value !== null ? value : '';\r\n                  td.style.padding = '8px 10px';\r\n                  td.style.fontSize = '13px';\r\n                  td.style.textAlign = col.align || 'left';\r\n\r\n                  // Estilização especial para status\r\n                  if (col.key === 'active' || col.key === 'status') {\r\n                    if (td.textContent === 'Ativo') {\r\n                      td.style.color = '#10B981'; // Verde para ativo\r\n                      td.style.fontWeight = 'bold';\r\n                    } else if (td.textContent === 'Inativo') {\r\n                      td.style.color = '#DC2626'; // Vermelho para inativo\r\n                      td.style.fontWeight = 'bold';\r\n                    } else if (td.textContent === 'Crítica') {\r\n                      td.style.color = '#DC2626'; // Vermelho para crítica\r\n                      td.style.fontWeight = 'bold';\r\n                    } else if (td.textContent === 'Alta') {\r\n                      td.style.color = '#F59E0B'; // Âmbar para alta\r\n                      td.style.fontWeight = 'bold';\r\n                    } else if (td.textContent === 'Média') {\r\n                      td.style.color = '#10B981'; // Verde para média\r\n                      td.style.fontWeight = 'bold';\r\n                    } else if (td.textContent === 'Baixa') {\r\n                      td.style.color = '#3B82F6'; // Azul para baixa\r\n                      td.style.fontWeight = 'bold';\r\n                    }\r\n                  }\r\n\r\n                  row.appendChild(td);\r\n                });\r\n\r\n                tbody.appendChild(row);\r\n              });\r\n\r\n              tableElement.appendChild(tbody);\r\n              tempContainer.appendChild(tableElement);\r\n            }\r\n\r\n            // Adicionar rodapé com data de geração\r\n            const footer = document.createElement('div');\r\n            footer.style.fontSize = '12px';\r\n            footer.style.color = '#666666';\r\n            footer.style.textAlign = 'right';\r\n            footer.style.marginTop = '20px';\r\n            footer.style.borderTop = '1px solid #eeeeee';\r\n            footer.style.paddingTop = '10px';\r\n\r\n            footer.textContent = `High Tide Systems`;\r\n            tempContainer.appendChild(footer);\r\n\r\n            // Adicionar o container temporário ao DOM\r\n            document.body.appendChild(tempContainer);\r\n\r\n            // Usar html2canvas para converter a tabela em uma imagem\r\n            return new Promise((resolve) => {\r\n              // Adicionar um pequeno atraso para garantir que o DOM esteja pronto\r\n              setTimeout(() => {\r\n                html2canvas(tempContainer, {\r\n                  scale: 2, // Aumenta a escala para melhor qualidade\r\n                  useCORS: true,\r\n                  allowTaint: true,\r\n                  backgroundColor: '#ffffff',\r\n                  logging: false,\r\n                  letterRendering: true\r\n                }).then(canvas => {\r\n                  // Remover o container temporário\r\n                  document.body.removeChild(tempContainer);\r\n\r\n                  // Converter o canvas para blob e salvar\r\n                  canvas.toBlob(blob => {\r\n                    saveAs(blob, `${filename}.png`);\r\n                    resolve(true);\r\n                  }, 'image/png');\r\n                }).catch(error => {\r\n                  console.error(\"Erro ao converter tabelas para imagem:\", error);\r\n                  // Remover o container temporário em caso de erro\r\n                  if (document.body.contains(tempContainer)) {\r\n                    document.body.removeChild(tempContainer);\r\n                  }\r\n                  resolve(false);\r\n                });\r\n              }, 100); // Pequeno atraso para garantir que o DOM esteja pronto\r\n            });\r\n          } catch (error) {\r\n            console.error(\"Erro na exportação para imagem:\", error);\r\n            return false;\r\n          }\r\n        }\r\n        // Outros formatos não suportados para múltiplas tabelas\r\n        else {\r\n          console.error(\"Formato não suportado para exportação de múltiplas tabelas\");\r\n          return false;\r\n        }\r\n      } else {\r\n        // Caso de tabela única (array de objetos)\r\n        // Se não forem fornecidas colunas, usa as chaves dos objetos\r\n        const tableColumns = columns.length > 0\r\n          ? columns\r\n          : data.length > 0\r\n            ? Object.keys(data[0]).map(key => ({\r\n                key,\r\n                header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),\r\n                format: null\r\n              }))\r\n            : [];\r\n\r\n        // Formata os dados com base nas colunas definidas\r\n        const formattedData = data.map(item => {\r\n          const formattedItem = {};\r\n\r\n          tableColumns.forEach(col => {\r\n            let value = item[col.key];\r\n\r\n            // Aplica formatação personalizada se especificada\r\n            if (col.format && typeof col.format === 'function') {\r\n              formattedItem[col.key] = col.format(value, item);\r\n            }\r\n            // Aplica formatação padrão se especificada\r\n            else if (col.type && formatters[col.type]) {\r\n              formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\r\n            }\r\n            // Sem formatação\r\n            else {\r\n              formattedItem[col.key] = value !== null && value !== undefined ? value : '';\r\n            }\r\n          });\r\n\r\n          return formattedItem;\r\n        });\r\n\r\n        // Exportação em formato XLSX\r\n        if (format === \"xlsx\") {\r\n          return exportExcel(formattedData, tableColumns, filename, title);\r\n        }\r\n        // Exportação em formato PDF\r\n        else if (format === \"pdf\") {\r\n          return exportPdf(formattedData, tableColumns, filename, title, subtitle);\r\n        }\r\n        // Exportação em formato de imagem (PNG)\r\n        else if (format === \"image\") {\r\n          // Para exportação de imagem, precisamos de um elemento DOM\r\n          // Vamos criar uma tabela temporária e convertê-la em imagem\r\n          return exportImage(formattedData, tableColumns, filename, title, subtitle);\r\n        }\r\n        else {\r\n          console.error(\"Formato de exportação não suportado\");\r\n          return false;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro na exportação:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta dados da API com os filtros atuais\r\n   * @param {string} endpoint - Endpoint da API\r\n   * @param {Object} filters - Filtros a serem aplicados\r\n   * @param {Object} options - Opções de exportação\r\n   * @returns {Promise<boolean>} - Sucesso da exportação\r\n   */\r\n  exportFromApi: async (endpoint, filters = {}, options = {}) => {\r\n    try {\r\n      const {\r\n        format = \"xlsx\",\r\n        filename = \"export\",\r\n        columns = [],\r\n      } = options;\r\n\r\n      // Se a API suporta exportação direta\r\n      if (options.useApiExport) {\r\n        const response = await api.get(`${endpoint}/export`, {\r\n          params: { ...filters, format },\r\n          responseType: format === \"pdf\" ? \"arraybuffer\" : \"blob\",\r\n        });\r\n\r\n        const blob = new Blob(\r\n          [response.data],\r\n          { type: format === \"pdf\" ? \"application/pdf\" : \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\" }\r\n        );\r\n\r\n        saveAs(blob, `${filename}.${format}`);\r\n        return true;\r\n      }\r\n\r\n      // Se a API não suporta exportação, faz uma consulta normal e exporta os dados no cliente\r\n      const response = await api.get(endpoint, { params: filters });\r\n\r\n      // Tenta obter os dados de diferentes formatos de resposta\r\n      const data = response.data?.data || response.data?.items || response.data || [];\r\n\r\n      // Exporta os dados\r\n      return exportService.exportData(data, {\r\n        ...options,\r\n        format,\r\n        filename,\r\n        columns,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar dados da API:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\n// Funções auxiliares de exportação\r\n\r\n/**\r\n * Exporta dados para Excel com formatação aprimorada\r\n */\r\nfunction exportExcel(data, columns, filename, title) {\r\n  try {\r\n    // Cria um novo workbook\r\n    const workbook = utils.book_new();\r\n\r\n    // Se tiver título, adiciona como primeira linha\r\n    let worksheetData = [];\r\n\r\n    // Data atual formatada\r\n    const currentDate = new Date();\r\n    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n    // Adiciona título e data\r\n    if (title) {\r\n      worksheetData.push([title]);\r\n      worksheetData.push([`Gerado em: ${formattedDate}`]);\r\n      worksheetData.push([]); // Linha em branco\r\n    }\r\n\r\n    // Adiciona os cabeçalhos\r\n    const headers = columns.map(col => col.header || col.key);\r\n    worksheetData.push(headers);\r\n\r\n    // Adiciona os dados\r\n    data.forEach(item => {\r\n      const row = columns.map(col => item[col.key]);\r\n      worksheetData.push(row);\r\n    });\r\n\r\n    // Cria a worksheet\r\n    const worksheet = utils.aoa_to_sheet(worksheetData);\r\n\r\n    // Configura os estilos (apenas as propriedades básicas são suportadas no xlsx)\r\n    if (title) {\r\n      // Mescla células para o título\r\n      worksheet['!merges'] = [\r\n        { s: {r: 0, c: 0}, e: {r: 0, c: headers.length - 1} }, // Título\r\n        { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} }, // Data\r\n      ];\r\n\r\n      // Ajusta largura das colunas\r\n      if (!worksheet['!cols']) worksheet['!cols'] = [];\r\n      columns.forEach((col, idx) => {\r\n        // Calcula largura ideal para cada coluna\r\n        const headerWidth = (col.header || col.key).length * 1.2;\r\n        let maxDataWidth = 0;\r\n\r\n        // Verifica o tamanho máximo dos dados em cada coluna\r\n        data.forEach(item => {\r\n          const cellValue = item[col.key];\r\n          const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';\r\n          maxDataWidth = Math.max(maxDataWidth, cellText.length);\r\n        });\r\n\r\n        worksheet['!cols'][idx] = {\r\n          wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))\r\n        };\r\n      });\r\n    }\r\n\r\n    // Adiciona a worksheet ao workbook\r\n    utils.book_append_sheet(workbook, worksheet, \"Dados\");\r\n\r\n    // Converte para binário e salva\r\n    const excelBuffer = write(workbook, {\r\n      bookType: \"xlsx\",\r\n      type: \"array\",\r\n      bookSST: false,\r\n      compression: true\r\n    });\r\n\r\n    const blob = new Blob([excelBuffer], { type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\" });\r\n    saveAs(blob, `${filename}.xlsx`);\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"Erro na exportação Excel:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Exporta dados para PDF com design aprimorado\r\n */\r\nfunction exportPdf(data, columns, filename, title, subtitle) {\r\n  try {\r\n    // Cria um novo documento PDF no tamanho A4\r\n    const doc = new jsPDF({\r\n      orientation: \"portrait\",\r\n      unit: \"mm\",\r\n      format: \"a4\"\r\n    });\r\n\r\n    // Configurações da página\r\n    const pageWidth = doc.internal.pageSize.width;\r\n    const pageHeight = doc.internal.pageSize.height;\r\n    const margin = 10;\r\n    const contentWidth = pageWidth - (margin * 2);\r\n\r\n    // Define cores do tema baseadas no estilo do módulo people (laranja)\r\n    const themeColors = {\r\n      primary: {\r\n        light: [255, 153, 51],    // #FF9933 (primary-500)\r\n        dark: [255, 127, 0],       // #FF7F00 (primary-600)\r\n        text: [255, 255, 255]      // #FFFFFF (texto branco)\r\n      },\r\n      secondary: {\r\n        light: [255, 237, 213],    // #FFEDD5 (orange-100)\r\n        dark: [154, 52, 18],       // #9A3412 (orange-800)\r\n        border: [251, 146, 60]     // #FB923C (orange-400)\r\n      }\r\n    };\r\n\r\n    // ===== CABEÇALHO COM GRADIENTE =====\r\n\r\n    // Desenha um retângulo para o cabeçalho com gradiente\r\n    const headerHeight = subtitle ? 30 : 25;\r\n\r\n    // Simulando um gradiente com múltiplos retângulos coloridos\r\n    const gradientSteps = 20;\r\n    const stepHeight = headerHeight / gradientSteps;\r\n\r\n    for (let i = 0; i < gradientSteps; i++) {\r\n      const ratio = i / gradientSteps;\r\n      // Interpola as cores para criar efeito de gradiente\r\n      const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);\r\n      const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);\r\n      const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);\r\n\r\n      doc.setFillColor(r, g, b);\r\n      doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');\r\n    }\r\n\r\n    // Adiciona um pequeno ícone ou logotipo\r\n    doc.setDrawColor(255, 255, 255);\r\n    doc.setFillColor(255, 255, 255);\r\n\r\n    // Adiciona título no cabeçalho\r\n    if (title) {\r\n      doc.setFont(\"helvetica\", \"bold\");\r\n      doc.setFontSize(18);\r\n      doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho\r\n      doc.text(title, pageWidth / 2, 15, { align: \"center\" });\r\n    }\r\n\r\n    // Adiciona subtítulo no cabeçalho se existir\r\n    if (subtitle) {\r\n      doc.setFont(\"helvetica\", \"normal\");\r\n      doc.setFontSize(10);\r\n      doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência\r\n      doc.text(subtitle, pageWidth / 2, 22, { align: \"center\" });\r\n    }\r\n\r\n    // Data de geração do relatório\r\n    const currentDate = new Date();\r\n    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n    doc.setFontSize(8);\r\n    doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, { align: \"right\" });\r\n\r\n    // ===== CONTEÚDO DA TABELA =====\r\n\r\n    // Prepara os dados para a tabela\r\n    const headers = columns.map(col => col.header || col.key);\r\n    const rows = data.map(item => {\r\n      return columns.map(col => item[col.key]);\r\n    });\r\n\r\n    // Prepara cabeçalhos mais curtos para prevenir quebras\r\n    const shortHeaders = headers.map(header => {\r\n      // Substituições específicas para cabeçalhos problemáticos\r\n      const replacements = {\r\n        'Nome Completo': 'Nome',\r\n        'Data de Nascimento': 'Nascimento',\r\n        'Data de Cadastro': 'Cadastro',\r\n        'Relacionamento': 'Relação',\r\n        'Telefone': 'Telefone'\r\n      };\r\n\r\n      return replacements[header] || header;\r\n    });\r\n\r\n    // Adiciona a tabela com estilo aprimorado\r\n    autoTable(doc, {\r\n      startY: headerHeight + 5, // Inicia após o cabeçalho\r\n      head: [shortHeaders],\r\n      body: rows,\r\n      theme: \"grid\",\r\n      headStyles: {\r\n        fillColor: themeColors.primary.dark,\r\n        textColor: themeColors.primary.text,\r\n        fontStyle: \"bold\",\r\n        halign: \"center\",\r\n        fontSize: 9,\r\n        cellPadding: { top: 3, right: 2, bottom: 3, left: 2 },\r\n        lineWidth: 0.1,\r\n        minCellWidth: 15,\r\n        overflow: 'linebreak'  // Evita quebra de linha nos cabeçalhos\r\n      },\r\n      styles: {\r\n        fontSize: 9,\r\n        cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },\r\n        overflow: \"linebreak\",\r\n        lineColor: [220, 220, 220],\r\n        lineWidth: 0.1\r\n      },\r\n      tableWidth: 'auto',\r\n      bodyStyles: {\r\n        minCellHeight: 10\r\n      },\r\n      alternateRowStyles: {\r\n        fillColor: [252, 252, 252]\r\n      },\r\n      columnStyles: columns.reduce((styles, col, index) => {\r\n        // Definir larguras mínimas específicas para evitar quebras nos títulos\r\n        const minWidths = {\r\n          'fullName': 30,\r\n          'cpf': 20,\r\n          'email': 28,\r\n          'phone': 20,\r\n          'birthDate': 20,\r\n          'gender': 15,\r\n          'relationship': 20,\r\n          'createdAt': 20,\r\n          'active': 15\r\n        };\r\n\r\n        // Define a largura baseada na configuração ou no mínimo predefinido\r\n        if (col.key && minWidths[col.key]) {\r\n          styles[index] = {\r\n            ...styles[index],\r\n            cellWidth: minWidths[col.key],\r\n            overflow: 'linebreak'\r\n          };\r\n        } else if (col.width) {\r\n          styles[index] = {\r\n            ...styles[index],\r\n            cellWidth: col.width\r\n          };\r\n        }\r\n\r\n        // Aplica o alinhamento se definido\r\n        if (col.align) {\r\n          styles[index] = {\r\n            ...styles[index],\r\n            halign: col.align\r\n          };\r\n        }\r\n\r\n        return styles;\r\n      }, {}),\r\n      margin: { top: headerHeight + 5, left: margin, right: margin, bottom: margin + 15 },\r\n      didDrawPage: function(data) {\r\n        // Adiciona rodapé colorido em cada página\r\n        doc.setFillColor(240, 240, 240);\r\n        doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');\r\n\r\n        // Linha sutil acima do rodapé\r\n        doc.setDrawColor(200, 200, 200);\r\n        doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);\r\n\r\n        // Adiciona numeração de páginas no rodapé\r\n        doc.setFontSize(8);\r\n        doc.setTextColor(100, 100, 100);\r\n        doc.text(\r\n          `Página ${data.pageNumber} de ${data.pageCount}`,\r\n          pageWidth - margin - 2,\r\n          pageHeight - 5,\r\n          { align: \"right\" }\r\n        );\r\n\r\n        // Adiciona nome do sistema no rodapé\r\n        doc.setTextColor(80, 80, 80);\r\n        doc.setFontSize(8);\r\n        doc.text(\"High Tide Systems\", margin + 2, pageHeight - 5);\r\n      },\r\n      didParseCell: function(data) {\r\n        // Aplicar formatação específica para células com status\r\n        const col = columns[data.column.index];\r\n        if (col && col.key === 'active') {\r\n          if (data.cell.section === 'body') {\r\n            if (data.cell.raw === 'Ativo') {\r\n              data.cell.styles.fontStyle = 'bold';\r\n              data.cell.styles.textColor = [16, 185, 129]; // Cor verde para Ativo\r\n            } else if (data.cell.raw === 'Inativo') {\r\n              data.cell.styles.fontStyle = 'bold';\r\n              data.cell.styles.textColor = [220, 38, 38];  // Cor vermelha para Inativo\r\n            }\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    // Salva o documento\r\n    doc.save(`${filename}.pdf`);\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"Erro na exportação PDF:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Exporta dados para imagem (PNG) criando uma tabela HTML temporária\r\n */\r\nfunction exportImage(data, columns, filename, title, subtitle) {\r\n  try {\r\n    // Criar um elemento temporário para renderizar a tabela\r\n    const tempContainer = document.createElement('div');\r\n    tempContainer.style.position = 'absolute';\r\n    tempContainer.style.left = '-9999px';\r\n    tempContainer.style.top = '-9999px';\r\n    tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade\r\n    tempContainer.style.backgroundColor = '#ffffff';\r\n    tempContainer.style.padding = '20px';\r\n    tempContainer.style.fontFamily = 'Arial, sans-serif';\r\n    tempContainer.style.color = '#333333';\r\n    tempContainer.style.boxSizing = 'border-box';\r\n\r\n    // Adicionar título e subtítulo\r\n    if (title) {\r\n      const titleElement = document.createElement('h2');\r\n      titleElement.textContent = title;\r\n      titleElement.style.color = '#FF7F00'; // Cor laranja do tema\r\n      titleElement.style.marginBottom = '5px';\r\n      titleElement.style.fontSize = '24px';\r\n      titleElement.style.fontWeight = 'bold';\r\n      titleElement.style.textAlign = 'center';\r\n      tempContainer.appendChild(titleElement);\r\n    }\r\n\r\n    if (subtitle) {\r\n      const subtitleElement = document.createElement('p');\r\n      subtitleElement.textContent = subtitle;\r\n      subtitleElement.style.color = '#666666';\r\n      subtitleElement.style.marginBottom = '20px';\r\n      subtitleElement.style.fontSize = '14px';\r\n      subtitleElement.style.textAlign = 'center';\r\n      tempContainer.appendChild(subtitleElement);\r\n    }\r\n\r\n    // Criar a tabela\r\n    const table = document.createElement('table');\r\n    table.style.width = '100%';\r\n    table.style.borderCollapse = 'collapse';\r\n    table.style.marginBottom = '20px';\r\n    table.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';\r\n\r\n    // Criar o cabeçalho da tabela\r\n    const thead = document.createElement('thead');\r\n    const headerRow = document.createElement('tr');\r\n    headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema\r\n    headerRow.style.color = '#ffffff';\r\n\r\n    columns.forEach(col => {\r\n      const th = document.createElement('th');\r\n      th.textContent = col.header || col.key;\r\n      th.style.padding = '10px';\r\n      th.style.textAlign = 'left';\r\n      th.style.fontWeight = 'bold';\r\n      th.style.fontSize = '14px';\r\n      th.style.borderBottom = '2px solid #FF9933';\r\n      headerRow.appendChild(th);\r\n    });\r\n\r\n    thead.appendChild(headerRow);\r\n    table.appendChild(thead);\r\n\r\n    // Criar o corpo da tabela\r\n    const tbody = document.createElement('tbody');\r\n\r\n    data.forEach((item, rowIndex) => {\r\n      const row = document.createElement('tr');\r\n      row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';\r\n      row.style.borderBottom = '1px solid #eeeeee';\r\n\r\n      columns.forEach(col => {\r\n        const td = document.createElement('td');\r\n        td.textContent = item[col.key] !== undefined && item[col.key] !== null ? item[col.key] : '';\r\n        td.style.padding = '8px 10px';\r\n        td.style.fontSize = '13px';\r\n\r\n        // Estilização especial para status\r\n        if (col.key === 'active') {\r\n          if (item[col.key] === 'Ativo') {\r\n            td.style.color = '#10B981'; // Verde para ativo\r\n            td.style.fontWeight = 'bold';\r\n          } else if (item[col.key] === 'Inativo') {\r\n            td.style.color = '#DC2626'; // Vermelho para inativo\r\n            td.style.fontWeight = 'bold';\r\n          }\r\n        }\r\n\r\n        row.appendChild(td);\r\n      });\r\n\r\n      tbody.appendChild(row);\r\n    });\r\n\r\n    table.appendChild(tbody);\r\n    tempContainer.appendChild(table);\r\n\r\n    // Adicionar rodapé com data de geração\r\n    const footer = document.createElement('div');\r\n    footer.style.fontSize = '12px';\r\n    footer.style.color = '#666666';\r\n    footer.style.textAlign = 'right';\r\n    footer.style.marginTop = '10px';\r\n\r\n    const currentDate = new Date();\r\n    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;\r\n\r\n    footer.textContent = `Gerado em: ${formattedDate} | High Tide Systems`;\r\n    tempContainer.appendChild(footer);\r\n\r\n    // Adicionar o container temporário ao DOM\r\n    document.body.appendChild(tempContainer);\r\n\r\n    // Usar html2canvas para converter a tabela em uma imagem\r\n    return new Promise((resolve) => {\r\n      // Adicionar um pequeno atraso para garantir que o DOM esteja pronto\r\n      setTimeout(() => {\r\n        html2canvas(tempContainer, {\r\n          scale: 2, // Aumenta a escala para melhor qualidade\r\n          useCORS: true,\r\n          allowTaint: true,\r\n          backgroundColor: '#ffffff',\r\n          logging: false,\r\n          letterRendering: true\r\n        }).then(canvas => {\r\n          // Remover o container temporário\r\n          document.body.removeChild(tempContainer);\r\n\r\n          // Converter o canvas para blob e salvar\r\n          canvas.toBlob(blob => {\r\n            saveAs(blob, `${filename}.png`);\r\n            resolve(true);\r\n          }, 'image/png');\r\n        }).catch(error => {\r\n          console.error(\"Erro ao converter tabela para imagem:\", error);\r\n          // Remover o container temporário em caso de erro\r\n          if (document.body.contains(tempContainer)) {\r\n            document.body.removeChild(tempContainer);\r\n          }\r\n          resolve(false);\r\n        });\r\n      }, 100); // Pequeno atraso para garantir que o DOM esteja pronto\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Erro na exportação para imagem:\", error);\r\n    return false;\r\n  }\r\n}"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAC5B;AAEA;AACA,+SAAyC,qCAAqC;AAG9E;AACA;AAHA;AACA;AAJA;;;;;;;;;AAQA,gCAAgC;AAChC,MAAM,aAAa;IACjB,yCAAyC;IACzC,MAAM,CAAC;QACL,IAAI,CAAC,OAAO,OAAO;QACnB,IAAI;YACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,cAAc;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;QAC9D,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,+BAA+B;IAC/B,KAAK,CAAC;QACJ,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;QACxC,OAAO,WAAW,OAAO,CAAC,gCAAgC;IAC5D;IAEA,mCAAmC;IACnC,OAAO,CAAC;QACN,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO,aAAa,OAAO,CAAC,yBAAyB;IACvD;IAEA,4BAA4B;IAC5B,SAAS,CAAC,OAAO,UAAU;QAAE,UAAU;QAAO,WAAW;IAAM,CAAC;QAC9D,OAAO,QAAQ,QAAQ,QAAQ,GAAG,QAAQ,SAAS;IACrD;IAEA,6BAA6B;IAC7B,UAAU,CAAC;QACT,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;AACF;AAEO,MAAM,gBAAgB;IAC3B;;;;;;;;;;;;;;;;GAgBC,GACD,YAAY,OAAO,MAAM,UAAU,CAAC,CAAC;QACnC,IAAI;YACF,MAAM,EACJ,SAAS,MAAM,EACf,WAAW,QAAQ,EACnB,UAAU,EAAE,EACZ,gBAAgB,CAAC,CAAC,EAClB,QAAQ,IAAI,EACZ,WAAW,IAAI,EACf,aAAa,KAAK,EAClB,SAAS,EAAE,EACZ,GAAG;YAEJ,oDAAoD;YACpD,IAAI,cAAc,CAAC,MAAM,OAAO,CAAC,OAAO;gBACtC,gDAAgD;gBAEhD,IAAI,WAAW,QAAQ;oBACrB,2CAA2C;oBAC3C,MAAM,WAAW,gIAAA,CAAA,QAAK,CAAC,QAAQ;oBAE/B,wCAAwC;oBACxC,MAAM,cAAc,IAAI;oBACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;oBAEtR,iCAAiC;oBACjC,KAAK,MAAM,SAAS,OAAQ;wBAC1B,MAAM,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC;wBAElC,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY;4BAC3C,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC;4BAClF;wBACF;wBAEA,iCAAiC;wBACjC,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAA;4BACvC,MAAM,gBAAgB,CAAC;4BAEvB,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;gCACpB,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;gCAEzB,kDAAkD;gCAClD,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;oCAClD,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO;gCAC7C,OAEK,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE;oCACzC,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,IAAI,GAAG,CAAC;gCAC7E,OAEK;oCACH,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,QAAQ,UAAU,YAAY,QAAQ;gCAC3E;4BACF;4BAEA,OAAO;wBACT;wBAEA,kCAAkC;wBAClC,IAAI,gBAAgB,EAAE;wBAEtB,2CAA2C;wBAC3C,IAAI,OAAO;4BACT,cAAc,IAAI,CAAC;gCAAC;6BAAM;4BAC1B,IAAI,MAAM,KAAK,EAAE;gCACf,cAAc,IAAI,CAAC;oCAAC,MAAM,KAAK;iCAAC;4BAClC;4BACA,cAAc,IAAI,CAAC;gCAAC,CAAC,WAAW,EAAE,eAAe;6BAAC;4BAClD,IAAI,UAAU;gCACZ,cAAc,IAAI,CAAC;oCAAC;iCAAS;4BAC/B;4BACA,cAAc,IAAI,CAAC,EAAE,GAAG,kBAAkB;wBAC5C;wBAEA,yBAAyB;wBACzB,MAAM,UAAU,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM,IAAI,IAAI,GAAG;wBAC9D,cAAc,IAAI,CAAC;wBAEnB,oBAAoB;wBACpB,mBAAmB,OAAO,CAAC,CAAA;4BACzB,MAAM,MAAM,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,GAAG,CAAC;4BAClD,cAAc,IAAI,CAAC;wBACrB;wBAEA,mBAAmB;wBACnB,MAAM,YAAY,gIAAA,CAAA,QAAK,CAAC,YAAY,CAAC;wBAErC,uBAAuB;wBACvB,IAAI,OAAO;4BACT,+BAA+B;4BAC/B,SAAS,CAAC,UAAU,GAAG;gCACrB;oCAAE,GAAG;wCAAC,GAAG;wCAAG,GAAG;oCAAC;oCAAG,GAAG;wCAAC,GAAG;wCAAG,GAAG,QAAQ,MAAM,GAAG;oCAAC;gCAAE;6BACrD;4BAED,IAAI,MAAM,KAAK,EAAE;gCACf,SAAS,CAAC,UAAU,CAAC,IAAI,CACvB;oCAAE,GAAG;wCAAC,GAAG;wCAAG,GAAG;oCAAC;oCAAG,GAAG;wCAAC,GAAG;wCAAG,GAAG,QAAQ,MAAM,GAAG;oCAAC;gCAAE,EAAE,sBAAsB;;gCAE9E,SAAS,CAAC,UAAU,CAAC,IAAI,CACvB;oCAAE,GAAG;wCAAC,GAAG;wCAAG,GAAG;oCAAC;oCAAG,GAAG;wCAAC,GAAG;wCAAG,GAAG,QAAQ,MAAM,GAAG;oCAAC;gCAAE,EAAE,OAAO;;4BAEjE,OAAO;gCACL,SAAS,CAAC,UAAU,CAAC,IAAI,CACvB;oCAAE,GAAG;wCAAC,GAAG;wCAAG,GAAG;oCAAC;oCAAG,GAAG;wCAAC,GAAG;wCAAG,GAAG,QAAQ,MAAM,GAAG;oCAAC;gCAAE,EAAE,OAAO;;4BAEjE;4BAEA,IAAI,UAAU;gCACZ,SAAS,CAAC,UAAU,CAAC,IAAI,CACvB;oCAAE,GAAG;wCAAC,GAAG,MAAM,KAAK,GAAG,IAAI;wCAAG,GAAG;oCAAC;oCAAG,GAAG;wCAAC,GAAG,MAAM,KAAK,GAAG,IAAI;wCAAG,GAAG,QAAQ,MAAM,GAAG;oCAAC;gCAAE,EAAE,YAAY;;4BAE1G;4BAEA,6BAA6B;4BAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,GAAG,EAAE;4BAChD,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK;gCAC1B,yCAAyC;gCACzC,MAAM,cAAc,CAAC,IAAI,MAAM,IAAI,IAAI,GAAG,EAAE,MAAM,GAAG;gCACrD,IAAI,eAAe;gCAEnB,qDAAqD;gCACrD,mBAAmB,OAAO,CAAC,CAAA;oCACzB,MAAM,YAAY,IAAI,CAAC,IAAI,GAAG,CAAC;oCAC/B,MAAM,WAAW,cAAc,QAAQ,cAAc,YAAY,UAAU,QAAQ,KAAK;oCACxF,eAAe,KAAK,GAAG,CAAC,cAAc,SAAS,MAAM;gCACvD;gCAEA,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG;oCACxB,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,aAAa,eAAe;gCACtE;4BACF;wBACF;wBAEA,8DAA8D;wBAC9D,IAAI,YAAY,MAAM,KAAK,IAAI,MAAM,IAAI;wBAEzC,gDAAgD;wBAChD,IAAI,UAAU,MAAM,GAAG,IAAI;4BACzB,iDAAiD;4BACjD,MAAM,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;4BACzC,IAAI,UAAU,MAAM,IAAI,IAAI;gCAC1B,YAAY,YAAY;4BAC1B,OAAO;gCACL,uEAAuE;gCACvE,YAAY,UAAU,SAAS,CAAC,GAAG,MAAM;4BAC3C;wBACF;wBAEA,mCAAmC;wBACnC,gIAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,UAAU,WAAW;oBAC/C;oBAEA,IAAI;wBACF,qDAAqD;wBACrD,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,GAAG;4BACzD,iEAAiE;4BACjE,IAAI,gBAAgB;4BACpB,KAAK,MAAM,aAAa,SAAS,UAAU,CAAE;gCAC3C,IAAI,UAAU,MAAM,GAAG,IAAI;oCACzB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,UAAU,GAAG,EAAE,UAAU,MAAM,CAAC,YAAY,CAAC;oCAC7F,gBAAgB;oCAChB;gCACF;4BACF;4BAEA,IAAI,CAAC,eAAe;gCAClB,iDAAiD;gCACjD,SAAS,UAAU,GAAG,EAAE,EAAE,gCAAgC;gCAC1D,SAAS,MAAM,GAAG,CAAC,GAAO,gCAAgC;gCAE1D,MAAM,YAAY,gIAAA,CAAA,QAAK,CAAC,YAAY,CAAC;oCACnC;wCAAC;qCAAqB;oCACtB;wCAAC;qCAAkE;oCACnE;wCAAC;qCAAuC;iCACzC;gCAED,gIAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,UAAU,WAAW;4BAC/C;4BAEA,gCAAgC;4BAChC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAK,AAAD,EAAE,UAAU;gCAClC,UAAU;gCACV,MAAM;gCACN,SAAS;gCACT,aAAa;4BACf;4BAEA,MAAM,OAAO,IAAI,KAAK;gCAAC;6BAAY,EAAE;gCAAE,MAAM;4BAAoE;4BACjH,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,KAAK,CAAC;4BAE/B,OAAO;wBACT,OAAO;4BACL,QAAQ,KAAK,CAAC;4BAEd,iDAAiD;4BACjD,MAAM,YAAY,gIAAA,CAAA,QAAK,CAAC,YAAY,CAAC;gCACnC;oCAAC;iCAAqB;gCACtB;oCAAC;iCAA8D;gCAC/D;oCAAC;iCAAkD;6BACpD;4BAED,gIAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,UAAU,WAAW;4BAE7C,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAK,AAAD,EAAE,UAAU;gCAClC,UAAU;gCACV,MAAM;gCACN,SAAS;gCACT,aAAa;4BACf;4BAEA,MAAM,OAAO,IAAI,KAAK;gCAAC;6BAAY,EAAE;gCAAE,MAAM;4BAAoE;4BACjH,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,KAAK,CAAC;4BAE/B,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAE9C,IAAI;4BACF,kCAAkC;4BAClC,MAAM,WAAW,gIAAA,CAAA,QAAK,CAAC,QAAQ;4BAC/B,MAAM,YAAY,gIAAA,CAAA,QAAK,CAAC,YAAY,CAAC;gCACnC;oCAAC;iCAAqB;gCACtB;oCAAC;iCAA2C;gCAC5C;oCAAC,uBAAuB,CAAC,MAAM,OAAO,IAAI,mBAAmB;iCAAE;gCAC/D;oCAAC;iCAAkD;6BACpD;4BAED,gIAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,UAAU,WAAW;4BAE7C,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAK,AAAD,EAAE,UAAU;gCAClC,UAAU;gCACV,MAAM;gCACN,SAAS;gCACT,aAAa;4BACf;4BAEA,MAAM,OAAO,IAAI,KAAK;gCAAC;6BAAY,EAAE;gCAAE,MAAM;4BAAoE;4BACjH,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,UAAU,CAAC;wBACtC,EAAE,OAAO,eAAe;4BACtB,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;wBAEA,OAAO;oBACT;gBACF,OAEK,IAAI,WAAW,OAAO;oBACzB,2CAA2C;oBAC3C,MAAM,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC;wBACpB,aAAa;wBACb,MAAM;wBACN,QAAQ;oBACV;oBAEA,0BAA0B;oBAC1B,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK;oBAC7C,MAAM,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM;oBAC/C,MAAM,SAAS;oBAEf,uBAAuB;oBACvB,MAAM,cAAc;wBAClB,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAK;6BAAG;4BACrB,MAAM;gCAAC;gCAAK;gCAAK;6BAAE;4BACnB,MAAM;gCAAC;gCAAK;gCAAK;6BAAI,CAAM,yBAAyB;wBACtD;wBACA,WAAW;4BACT,OAAO;gCAAC;gCAAK;gCAAK;6BAAI;4BACtB,MAAM;gCAAC;gCAAK;gCAAI;6BAAG;4BACnB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAG,CAAK,uBAAuB;wBACpD;oBACF;oBAEA,yCAAyC;oBACzC,MAAM,eAAe,WAAW,KAAK;oBAErC,4DAA4D;oBAC5D,MAAM,gBAAgB;oBACtB,MAAM,aAAa,eAAe;oBAElC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,MAAM,QAAQ,IAAI;wBAClB,oDAAoD;wBACpD,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC5G,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC5G,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;wBAE5G,IAAI,YAAY,CAAC,GAAG,GAAG;wBACvB,IAAI,IAAI,CAAC,GAAG,IAAI,YAAY,WAAW,YAAY;oBACrD;oBAEA,+BAA+B;oBAC/B,IAAI,OAAO;wBACT,IAAI,OAAO,CAAC,aAAa;wBACzB,IAAI,WAAW,CAAC;wBAChB,IAAI,YAAY,CAAC,KAAK,KAAK,MAAM,gCAAgC;wBACjE,IAAI,IAAI,CAAC,OAAO,YAAY,GAAG,IAAI;4BAAE,OAAO;wBAAS;oBACvD;oBAEA,6CAA6C;oBAC7C,IAAI,UAAU;wBACZ,IAAI,OAAO,CAAC,aAAa;wBACzB,IAAI,WAAW,CAAC;wBAChB,IAAI,YAAY,CAAC,KAAK,KAAK,KAAK,MAAM,iCAAiC;wBACvE,IAAI,IAAI,CAAC,UAAU,YAAY,GAAG,IAAI;4BAAE,OAAO;wBAAS;oBAC1D;oBAEA,+BAA+B;oBAC/B,MAAM,cAAc,IAAI;oBACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;oBAEtR,IAAI,WAAW,CAAC;oBAChB,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,SAAS,GAAG,eAAe,GAAG;wBAAE,OAAO;oBAAQ;oBAEnG,gDAAgD;oBAChD,IAAI,YAAY,eAAe;oBAE/B,wBAAwB;oBACxB,IAAK,IAAI,aAAa,GAAG,aAAa,OAAO,MAAM,EAAE,aAAc;wBACjE,MAAM,QAAQ,MAAM,CAAC,WAAW;wBAChC,MAAM,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC;wBAElC,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY;4BAC3C,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC;4BAClF;wBACF;wBAEA,iCAAiC;wBACjC,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAA;4BACvC,MAAM,gBAAgB,CAAC;4BAEvB,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;gCACpB,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;gCAEzB,kDAAkD;gCAClD,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;oCAClD,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO;gCAC7C,OAEK,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE;oCACzC,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,IAAI,GAAG,CAAC;gCAC7E,OAEK;oCACH,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,QAAQ,UAAU,YAAY,QAAQ;gCAC3E;4BACF;4BAEA,OAAO;wBACT;wBAEA,4BAA4B;wBAC5B,IAAI,MAAM,KAAK,EAAE;4BACf,gDAAgD;4BAChD,IAAI,YAAY,aAAa,IAAI;gCAC/B,IAAI,OAAO;gCACX,YAAY;4BACd;4BAEA,IAAI,OAAO,CAAC,aAAa;4BACzB,IAAI,WAAW,CAAC;4BAChB,IAAI,YAAY,CAAC,IAAI,IAAI;4BACzB,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,QAAQ;4BAC9B,aAAa;wBACf;wBAEA,iCAAiC;wBACjC,MAAM,UAAU,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM,IAAI,IAAI,GAAG;wBAC9D,MAAM,OAAO,mBAAmB,GAAG,CAAC,CAAA;4BAClC,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,GAAG,CAAC;wBAC/C;wBAEA,uDAAuD;wBACvD,MAAM,eAAe,QAAQ,GAAG,CAAC,CAAA;4BAC/B,0DAA0D;4BAC1D,MAAM,eAAe;gCACnB,iBAAiB;gCACjB,sBAAsB;gCACtB,oBAAoB;gCACpB,kBAAkB;gCAClB,YAAY;4BACd;4BAEA,OAAO,YAAY,CAAC,OAAO,IAAI;wBACjC;wBAEA,oBAAoB;wBACpB,CAAA,GAAA,8KAAA,CAAA,UAAS,AAAD,EAAE,KAAK;4BACb,QAAQ;4BACR,MAAM;gCAAC;6BAAa;4BACpB,MAAM;4BACN,OAAO;4BACP,YAAY;gCACV,WAAW,YAAY,OAAO,CAAC,IAAI;gCACnC,WAAW,YAAY,OAAO,CAAC,IAAI;gCACnC,WAAW;gCACX,QAAQ;gCACR,UAAU;gCACV,aAAa;oCAAE,KAAK;oCAAG,OAAO;oCAAG,QAAQ;oCAAG,MAAM;gCAAE;gCACpD,WAAW;gCACX,cAAc;gCACd,UAAU;4BACZ;4BACA,QAAQ;gCACN,UAAU;gCACV,aAAa;oCAAE,KAAK;oCAAG,OAAO;oCAAG,QAAQ;oCAAG,MAAM;gCAAE;gCACpD,UAAU;gCACV,WAAW;oCAAC;oCAAK;oCAAK;iCAAI;gCAC1B,WAAW;4BACb;4BACA,YAAY;4BACZ,YAAY;gCACV,eAAe;4BACjB;4BACA,oBAAoB;gCAClB,WAAW;oCAAC;oCAAK;oCAAK;iCAAI;4BAC5B;4BACA,cAAc,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK;gCAC/C,uEAAuE;gCACvE,MAAM,YAAY;oCAChB,YAAY;oCACZ,OAAO;oCACP,SAAS;oCACT,SAAS;oCACT,aAAa;oCACb,UAAU;oCACV,gBAAgB;oCAChB,aAAa;oCACb,UAAU;gCACZ;gCAEA,oEAAoE;gCACpE,IAAI,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE;oCACjC,MAAM,CAAC,MAAM,GAAG;wCACd,GAAG,MAAM,CAAC,MAAM;wCAChB,WAAW,SAAS,CAAC,IAAI,GAAG,CAAC;wCAC7B,UAAU;oCACZ;gCACF,OAAO,IAAI,IAAI,KAAK,EAAE;oCACpB,MAAM,CAAC,MAAM,GAAG;wCACd,GAAG,MAAM,CAAC,MAAM;wCAChB,WAAW,IAAI,KAAK;oCACtB;gCACF;gCAEA,mCAAmC;gCACnC,IAAI,IAAI,KAAK,EAAE;oCACb,MAAM,CAAC,MAAM,GAAG;wCACd,GAAG,MAAM,CAAC,MAAM;wCAChB,QAAQ,IAAI,KAAK;oCACnB;gCACF;gCAEA,OAAO;4BACT,GAAG,CAAC;4BACJ,QAAQ;gCAAE,KAAK;gCAAW,MAAM;gCAAQ,OAAO;gCAAQ,QAAQ,SAAS;4BAAG;4BAC3E,aAAa,SAAS,IAAI;gCACxB,0CAA0C;gCAC1C,IAAI,YAAY,CAAC,KAAK,KAAK;gCAC3B,IAAI,IAAI,CAAC,GAAG,aAAa,IAAI,WAAW,IAAI;gCAE5C,8BAA8B;gCAC9B,IAAI,YAAY,CAAC,KAAK,KAAK;gCAC3B,IAAI,IAAI,CAAC,GAAG,aAAa,IAAI,WAAW,aAAa;gCAErD,0CAA0C;gCAC1C,IAAI,WAAW,CAAC;gCAChB,IAAI,YAAY,CAAC,KAAK,KAAK;gCAC3B,IAAI,IAAI,CACN,CAAC,OAAO,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE,KAAK,SAAS,EAAE,EAChD,YAAY,SAAS,GACrB,aAAa,GACb;oCAAE,OAAO;gCAAQ;gCAGnB,qCAAqC;gCACrC,IAAI,YAAY,CAAC,IAAI,IAAI;gCACzB,IAAI,WAAW,CAAC;gCAChB,IAAI,IAAI,CAAC,qBAAqB,SAAS,GAAG,aAAa;4BACzD;wBACF;wBAEA,6CAA6C;wBAC7C,kDAAkD;wBAClD,YAAY,IAAI,aAAa,CAAC,MAAM,GAAG;oBACzC;oBAEA,oBAAoB;oBACpB,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC;oBAE1B,OAAO;gBACT,OAEK,IAAI,WAAW,SAAS;oBAC3B,IAAI;wBACF,gEAAgE;wBAChE,MAAM,gBAAgB,SAAS,aAAa,CAAC;wBAC7C,cAAc,KAAK,CAAC,QAAQ,GAAG;wBAC/B,cAAc,KAAK,CAAC,IAAI,GAAG;wBAC3B,cAAc,KAAK,CAAC,GAAG,GAAG;wBAC1B,cAAc,KAAK,CAAC,KAAK,GAAG,UAAU,qCAAqC;wBAC3E,cAAc,KAAK,CAAC,eAAe,GAAG;wBACtC,cAAc,KAAK,CAAC,OAAO,GAAG;wBAC9B,cAAc,KAAK,CAAC,UAAU,GAAG;wBACjC,cAAc,KAAK,CAAC,KAAK,GAAG;wBAC5B,cAAc,KAAK,CAAC,SAAS,GAAG;wBAEhC,yCAAyC;wBACzC,IAAI,OAAO;4BACT,MAAM,eAAe,SAAS,aAAa,CAAC;4BAC5C,aAAa,WAAW,GAAG;4BAC3B,aAAa,KAAK,CAAC,KAAK,GAAG,WAAW,sBAAsB;4BAC5D,aAAa,KAAK,CAAC,YAAY,GAAG;4BAClC,aAAa,KAAK,CAAC,QAAQ,GAAG;4BAC9B,aAAa,KAAK,CAAC,UAAU,GAAG;4BAChC,aAAa,KAAK,CAAC,SAAS,GAAG;4BAC/B,cAAc,WAAW,CAAC;wBAC5B;wBAEA,IAAI,UAAU;4BACZ,MAAM,kBAAkB,SAAS,aAAa,CAAC;4BAC/C,gBAAgB,WAAW,GAAG;4BAC9B,gBAAgB,KAAK,CAAC,KAAK,GAAG;4BAC9B,gBAAgB,KAAK,CAAC,YAAY,GAAG;4BACrC,gBAAgB,KAAK,CAAC,QAAQ,GAAG;4BACjC,gBAAgB,KAAK,CAAC,SAAS,GAAG;4BAClC,cAAc,WAAW,CAAC;wBAC5B;wBAEA,uBAAuB;wBACvB,MAAM,cAAc,IAAI;wBACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;wBAEtR,MAAM,cAAc,SAAS,aAAa,CAAC;wBAC3C,YAAY,WAAW,GAAG,CAAC,WAAW,EAAE,eAAe;wBACvD,YAAY,KAAK,CAAC,KAAK,GAAG;wBAC1B,YAAY,KAAK,CAAC,YAAY,GAAG;wBACjC,YAAY,KAAK,CAAC,QAAQ,GAAG;wBAC7B,YAAY,KAAK,CAAC,SAAS,GAAG;wBAC9B,cAAc,WAAW,CAAC;wBAE1B,wBAAwB;wBACxB,KAAK,MAAM,SAAS,OAAQ;4BAC1B,MAAM,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC;4BAElC,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,YAAY;gCAC3C,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC;gCAClF;4BACF;4BAEA,4BAA4B;4BAC5B,MAAM,eAAe,SAAS,aAAa,CAAC;4BAC5C,aAAa,WAAW,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI;4BACpD,aAAa,KAAK,CAAC,KAAK,GAAG,WAAW,sBAAsB;4BAC5D,aAAa,KAAK,CAAC,SAAS,GAAG;4BAC/B,aAAa,KAAK,CAAC,YAAY,GAAG;4BAClC,aAAa,KAAK,CAAC,QAAQ,GAAG;4BAC9B,aAAa,KAAK,CAAC,UAAU,GAAG;4BAChC,cAAc,WAAW,CAAC;4BAE1B,iBAAiB;4BACjB,MAAM,eAAe,SAAS,aAAa,CAAC;4BAC5C,aAAa,KAAK,CAAC,KAAK,GAAG;4BAC3B,aAAa,KAAK,CAAC,cAAc,GAAG;4BACpC,aAAa,KAAK,CAAC,YAAY,GAAG;4BAClC,aAAa,KAAK,CAAC,SAAS,GAAG;4BAE/B,8BAA8B;4BAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;4BACrC,MAAM,YAAY,SAAS,aAAa,CAAC;4BACzC,UAAU,KAAK,CAAC,eAAe,GAAG,WAAW,sBAAsB;4BACnE,UAAU,KAAK,CAAC,KAAK,GAAG;4BAExB,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;gCACpB,MAAM,KAAK,SAAS,aAAa,CAAC;gCAClC,GAAG,WAAW,GAAG,IAAI,MAAM,IAAI,IAAI,GAAG;gCACtC,GAAG,KAAK,CAAC,OAAO,GAAG;gCACnB,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,KAAK,IAAI;gCAClC,GAAG,KAAK,CAAC,UAAU,GAAG;gCACtB,GAAG,KAAK,CAAC,QAAQ,GAAG;gCACpB,GAAG,KAAK,CAAC,YAAY,GAAG;gCACxB,UAAU,WAAW,CAAC;4BACxB;4BAEA,MAAM,WAAW,CAAC;4BAClB,aAAa,WAAW,CAAC;4BAEzB,0BAA0B;4BAC1B,MAAM,QAAQ,SAAS,aAAa,CAAC;4BAErC,UAAU,OAAO,CAAC,CAAC,MAAM;gCACvB,MAAM,MAAM,SAAS,aAAa,CAAC;gCACnC,IAAI,KAAK,CAAC,eAAe,GAAG,WAAW,MAAM,IAAI,YAAY;gCAC7D,IAAI,KAAK,CAAC,YAAY,GAAG;gCAEzB,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;oCACpB,MAAM,KAAK,SAAS,aAAa,CAAC;oCAElC,6BAA6B;oCAC7B,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;oCAEzB,kDAAkD;oCAClD,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;wCAClD,QAAQ,IAAI,MAAM,CAAC,OAAO;oCAC5B,OAEK,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE;wCACzC,QAAQ,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC;oCACvC;oCAEA,GAAG,WAAW,GAAG,UAAU,aAAa,UAAU,OAAO,QAAQ;oCACjE,GAAG,KAAK,CAAC,OAAO,GAAG;oCACnB,GAAG,KAAK,CAAC,QAAQ,GAAG;oCACpB,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,KAAK,IAAI;oCAElC,mCAAmC;oCACnC,IAAI,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG,KAAK,UAAU;wCAChD,IAAI,GAAG,WAAW,KAAK,SAAS;4CAC9B,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,mBAAmB;4CAC/C,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB,OAAO,IAAI,GAAG,WAAW,KAAK,WAAW;4CACvC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,wBAAwB;4CACpD,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB,OAAO,IAAI,GAAG,WAAW,KAAK,WAAW;4CACvC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,wBAAwB;4CACpD,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB,OAAO,IAAI,GAAG,WAAW,KAAK,QAAQ;4CACpC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,kBAAkB;4CAC9C,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB,OAAO,IAAI,GAAG,WAAW,KAAK,SAAS;4CACrC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,mBAAmB;4CAC/C,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB,OAAO,IAAI,GAAG,WAAW,KAAK,SAAS;4CACrC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,kBAAkB;4CAC9C,GAAG,KAAK,CAAC,UAAU,GAAG;wCACxB;oCACF;oCAEA,IAAI,WAAW,CAAC;gCAClB;gCAEA,MAAM,WAAW,CAAC;4BACpB;4BAEA,aAAa,WAAW,CAAC;4BACzB,cAAc,WAAW,CAAC;wBAC5B;wBAEA,uCAAuC;wBACvC,MAAM,SAAS,SAAS,aAAa,CAAC;wBACtC,OAAO,KAAK,CAAC,QAAQ,GAAG;wBACxB,OAAO,KAAK,CAAC,KAAK,GAAG;wBACrB,OAAO,KAAK,CAAC,SAAS,GAAG;wBACzB,OAAO,KAAK,CAAC,SAAS,GAAG;wBACzB,OAAO,KAAK,CAAC,SAAS,GAAG;wBACzB,OAAO,KAAK,CAAC,UAAU,GAAG;wBAE1B,OAAO,WAAW,GAAG,CAAC,iBAAiB,CAAC;wBACxC,cAAc,WAAW,CAAC;wBAE1B,0CAA0C;wBAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,yDAAyD;wBACzD,OAAO,IAAI,QAAQ,CAAC;4BAClB,oEAAoE;4BACpE,WAAW;gCACT,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,eAAe;oCACzB,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,iBAAiB;oCACjB,SAAS;oCACT,iBAAiB;gCACnB,GAAG,IAAI,CAAC,CAAA;oCACN,iCAAiC;oCACjC,SAAS,IAAI,CAAC,WAAW,CAAC;oCAE1B,wCAAwC;oCACxC,OAAO,MAAM,CAAC,CAAA;wCACZ,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,IAAI,CAAC;wCAC9B,QAAQ;oCACV,GAAG;gCACL,GAAG,KAAK,CAAC,CAAA;oCACP,QAAQ,KAAK,CAAC,0CAA0C;oCACxD,iDAAiD;oCACjD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,gBAAgB;wCACzC,SAAS,IAAI,CAAC,WAAW,CAAC;oCAC5B;oCACA,QAAQ;gCACV;4BACF,GAAG,MAAM,uDAAuD;wBAClE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,OAAO;oBACT;gBACF,OAEK;oBACH,QAAQ,KAAK,CAAC;oBACd,OAAO;gBACT;YACF,OAAO;gBACL,0CAA0C;gBAC1C,6DAA6D;gBAC7D,MAAM,eAAe,QAAQ,MAAM,GAAG,IAClC,UACA,KAAK,MAAM,GAAG,IACZ,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC/B;wBACA,QAAQ,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,YAAY;wBACvE,QAAQ;oBACV,CAAC,KACD,EAAE;gBAER,kDAAkD;gBAClD,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAA;oBAC7B,MAAM,gBAAgB,CAAC;oBAEvB,aAAa,OAAO,CAAC,CAAA;wBACnB,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC;wBAEzB,kDAAkD;wBAClD,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;4BAClD,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO;wBAC7C,OAEK,IAAI,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE;4BACzC,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,IAAI,GAAG,CAAC;wBAC7E,OAEK;4BACH,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,UAAU,QAAQ,UAAU,YAAY,QAAQ;wBAC3E;oBACF;oBAEA,OAAO;gBACT;gBAEA,6BAA6B;gBAC7B,IAAI,WAAW,QAAQ;oBACrB,OAAO,YAAY,eAAe,cAAc,UAAU;gBAC5D,OAEK,IAAI,WAAW,OAAO;oBACzB,OAAO,UAAU,eAAe,cAAc,UAAU,OAAO;gBACjE,OAEK,IAAI,WAAW,SAAS;oBAC3B,2DAA2D;oBAC3D,4DAA4D;oBAC5D,OAAO,YAAY,eAAe,cAAc,UAAU,OAAO;gBACnE,OACK;oBACH,QAAQ,KAAK,CAAC;oBACd,OAAO;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;IAEA;;;;;;GAMC,GACD,eAAe,OAAO,UAAU,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACxD,IAAI;YACF,MAAM,EACJ,SAAS,MAAM,EACf,WAAW,QAAQ,EACnB,UAAU,EAAE,EACb,GAAG;YAEJ,qCAAqC;YACrC,IAAI,QAAQ,YAAY,EAAE;gBACxB,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE;oBACnD,QAAQ;wBAAE,GAAG,OAAO;wBAAE;oBAAO;oBAC7B,cAAc,WAAW,QAAQ,gBAAgB;gBACnD;gBAEA,MAAM,OAAO,IAAI,KACf;oBAAC,SAAS,IAAI;iBAAC,EACf;oBAAE,MAAM,WAAW,QAAQ,oBAAoB;gBAAoE;gBAGrH,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,CAAC,EAAE,QAAQ;gBACpC,OAAO;YACT;YAEA,yFAAyF;YACzF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,UAAU;gBAAE,QAAQ;YAAQ;YAE3D,0DAA0D;YAC1D,MAAM,OAAO,SAAS,IAAI,EAAE,QAAQ,SAAS,IAAI,EAAE,SAAS,SAAS,IAAI,IAAI,EAAE;YAE/E,mBAAmB;YACnB,OAAO,cAAc,UAAU,CAAC,MAAM;gBACpC,GAAG,OAAO;gBACV;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;AACF;AAEA,mCAAmC;AAEnC;;CAEC,GACD,SAAS,YAAY,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;IACjD,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW,gIAAA,CAAA,QAAK,CAAC,QAAQ;QAE/B,gDAAgD;QAChD,IAAI,gBAAgB,EAAE;QAEtB,uBAAuB;QACvB,MAAM,cAAc,IAAI;QACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAEtR,yBAAyB;QACzB,IAAI,OAAO;YACT,cAAc,IAAI,CAAC;gBAAC;aAAM;YAC1B,cAAc,IAAI,CAAC;gBAAC,CAAC,WAAW,EAAE,eAAe;aAAC;YAClD,cAAc,IAAI,CAAC,EAAE,GAAG,kBAAkB;QAC5C;QAEA,yBAAyB;QACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM,IAAI,IAAI,GAAG;QACxD,cAAc,IAAI,CAAC;QAEnB,oBAAoB;QACpB,KAAK,OAAO,CAAC,CAAA;YACX,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,GAAG,CAAC;YAC5C,cAAc,IAAI,CAAC;QACrB;QAEA,mBAAmB;QACnB,MAAM,YAAY,gIAAA,CAAA,QAAK,CAAC,YAAY,CAAC;QAErC,+EAA+E;QAC/E,IAAI,OAAO;YACT,+BAA+B;YAC/B,SAAS,CAAC,UAAU,GAAG;gBACrB;oBAAE,GAAG;wBAAC,GAAG;wBAAG,GAAG;oBAAC;oBAAG,GAAG;wBAAC,GAAG;wBAAG,GAAG,QAAQ,MAAM,GAAG;oBAAC;gBAAE;gBACpD;oBAAE,GAAG;wBAAC,GAAG;wBAAG,GAAG;oBAAC;oBAAG,GAAG;wBAAC,GAAG;wBAAG,GAAG,QAAQ,MAAM,GAAG;oBAAC;gBAAE;aACrD;YAED,6BAA6B;YAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,GAAG,EAAE;YAChD,QAAQ,OAAO,CAAC,CAAC,KAAK;gBACpB,yCAAyC;gBACzC,MAAM,cAAc,CAAC,IAAI,MAAM,IAAI,IAAI,GAAG,EAAE,MAAM,GAAG;gBACrD,IAAI,eAAe;gBAEnB,qDAAqD;gBACrD,KAAK,OAAO,CAAC,CAAA;oBACX,MAAM,YAAY,IAAI,CAAC,IAAI,GAAG,CAAC;oBAC/B,MAAM,WAAW,cAAc,QAAQ,cAAc,YAAY,UAAU,QAAQ,KAAK;oBACxF,eAAe,KAAK,GAAG,CAAC,cAAc,SAAS,MAAM;gBACvD;gBAEA,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG;oBACxB,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,aAAa,eAAe;gBACtE;YACF;QACF;QAEA,mCAAmC;QACnC,gIAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,UAAU,WAAW;QAE7C,gCAAgC;QAChC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAK,AAAD,EAAE,UAAU;YAClC,UAAU;YACV,MAAM;YACN,SAAS;YACT,aAAa;QACf;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAoE;QACjH,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,KAAK,CAAC;QAE/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,UAAU,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;IACzD,IAAI;QACF,2CAA2C;QAC3C,MAAM,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC;YACpB,aAAa;YACb,MAAM;YACN,QAAQ;QACV;QAEA,0BAA0B;QAC1B,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK;QAC7C,MAAM,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM;QAC/C,MAAM,SAAS;QACf,MAAM,eAAe,YAAa,SAAS;QAE3C,qEAAqE;QACrE,MAAM,cAAc;YAClB,SAAS;gBACP,OAAO;oBAAC;oBAAK;oBAAK;iBAAG;gBACrB,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBACnB,MAAM;oBAAC;oBAAK;oBAAK;iBAAI,CAAM,yBAAyB;YACtD;YACA,WAAW;gBACT,OAAO;oBAAC;oBAAK;oBAAK;iBAAI;gBACtB,MAAM;oBAAC;oBAAK;oBAAI;iBAAG;gBACnB,QAAQ;oBAAC;oBAAK;oBAAK;iBAAG,CAAK,uBAAuB;YACpD;QACF;QAEA,sCAAsC;QAEtC,sDAAsD;QACtD,MAAM,eAAe,WAAW,KAAK;QAErC,4DAA4D;QAC5D,MAAM,gBAAgB;QACtB,MAAM,aAAa,eAAe;QAElC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,MAAM,QAAQ,IAAI;YAClB,oDAAoD;YACpD,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5G,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5G,MAAM,IAAI,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,EAAE;YAE5G,IAAI,YAAY,CAAC,GAAG,GAAG;YACvB,IAAI,IAAI,CAAC,GAAG,IAAI,YAAY,WAAW,YAAY;QACrD;QAEA,wCAAwC;QACxC,IAAI,YAAY,CAAC,KAAK,KAAK;QAC3B,IAAI,YAAY,CAAC,KAAK,KAAK;QAE3B,+BAA+B;QAC/B,IAAI,OAAO;YACT,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK,MAAM,gCAAgC;YACjE,IAAI,IAAI,CAAC,OAAO,YAAY,GAAG,IAAI;gBAAE,OAAO;YAAS;QACvD;QAEA,6CAA6C;QAC7C,IAAI,UAAU;YACZ,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK,KAAK,MAAM,iCAAiC;YACvE,IAAI,IAAI,CAAC,UAAU,YAAY,GAAG,IAAI;gBAAE,OAAO;YAAS;QAC1D;QAEA,+BAA+B;QAC/B,MAAM,cAAc,IAAI;QACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAEtR,IAAI,WAAW,CAAC;QAChB,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,SAAS,GAAG,eAAe,GAAG;YAAE,OAAO;QAAQ;QAEnG,iCAAiC;QAEjC,iCAAiC;QACjC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM,IAAI,IAAI,GAAG;QACxD,MAAM,OAAO,KAAK,GAAG,CAAC,CAAA;YACpB,OAAO,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,GAAG,CAAC;QACzC;QAEA,uDAAuD;QACvD,MAAM,eAAe,QAAQ,GAAG,CAAC,CAAA;YAC/B,0DAA0D;YAC1D,MAAM,eAAe;gBACnB,iBAAiB;gBACjB,sBAAsB;gBACtB,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;YACd;YAEA,OAAO,YAAY,CAAC,OAAO,IAAI;QACjC;QAEA,0CAA0C;QAC1C,CAAA,GAAA,8KAAA,CAAA,UAAS,AAAD,EAAE,KAAK;YACb,QAAQ,eAAe;YACvB,MAAM;gBAAC;aAAa;YACpB,MAAM;YACN,OAAO;YACP,YAAY;gBACV,WAAW,YAAY,OAAO,CAAC,IAAI;gBACnC,WAAW,YAAY,OAAO,CAAC,IAAI;gBACnC,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,aAAa;oBAAE,KAAK;oBAAG,OAAO;oBAAG,QAAQ;oBAAG,MAAM;gBAAE;gBACpD,WAAW;gBACX,cAAc;gBACd,UAAU,YAAa,uCAAuC;YAChE;YACA,QAAQ;gBACN,UAAU;gBACV,aAAa;oBAAE,KAAK;oBAAG,OAAO;oBAAG,QAAQ;oBAAG,MAAM;gBAAE;gBACpD,UAAU;gBACV,WAAW;oBAAC;oBAAK;oBAAK;iBAAI;gBAC1B,WAAW;YACb;YACA,YAAY;YACZ,YAAY;gBACV,eAAe;YACjB;YACA,oBAAoB;gBAClB,WAAW;oBAAC;oBAAK;oBAAK;iBAAI;YAC5B;YACA,cAAc,QAAQ,MAAM,CAAC,CAAC,QAAQ,KAAK;gBACzC,uEAAuE;gBACvE,MAAM,YAAY;oBAChB,YAAY;oBACZ,OAAO;oBACP,SAAS;oBACT,SAAS;oBACT,aAAa;oBACb,UAAU;oBACV,gBAAgB;oBAChB,aAAa;oBACb,UAAU;gBACZ;gBAEA,oEAAoE;gBACpE,IAAI,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE;oBACjC,MAAM,CAAC,MAAM,GAAG;wBACd,GAAG,MAAM,CAAC,MAAM;wBAChB,WAAW,SAAS,CAAC,IAAI,GAAG,CAAC;wBAC7B,UAAU;oBACZ;gBACF,OAAO,IAAI,IAAI,KAAK,EAAE;oBACpB,MAAM,CAAC,MAAM,GAAG;wBACd,GAAG,MAAM,CAAC,MAAM;wBAChB,WAAW,IAAI,KAAK;oBACtB;gBACF;gBAEA,mCAAmC;gBACnC,IAAI,IAAI,KAAK,EAAE;oBACb,MAAM,CAAC,MAAM,GAAG;wBACd,GAAG,MAAM,CAAC,MAAM;wBAChB,QAAQ,IAAI,KAAK;oBACnB;gBACF;gBAEA,OAAO;YACT,GAAG,CAAC;YACJ,QAAQ;gBAAE,KAAK,eAAe;gBAAG,MAAM;gBAAQ,OAAO;gBAAQ,QAAQ,SAAS;YAAG;YAClF,aAAa,SAAS,IAAI;gBACxB,0CAA0C;gBAC1C,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,IAAI,CAAC,GAAG,aAAa,IAAI,WAAW,IAAI;gBAE5C,8BAA8B;gBAC9B,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,IAAI,CAAC,GAAG,aAAa,IAAI,WAAW,aAAa;gBAErD,0CAA0C;gBAC1C,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,IAAI,CACN,CAAC,OAAO,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE,KAAK,SAAS,EAAE,EAChD,YAAY,SAAS,GACrB,aAAa,GACb;oBAAE,OAAO;gBAAQ;gBAGnB,qCAAqC;gBACrC,IAAI,YAAY,CAAC,IAAI,IAAI;gBACzB,IAAI,WAAW,CAAC;gBAChB,IAAI,IAAI,CAAC,qBAAqB,SAAS,GAAG,aAAa;YACzD;YACA,cAAc,SAAS,IAAI;gBACzB,wDAAwD;gBACxD,MAAM,MAAM,OAAO,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC;gBACtC,IAAI,OAAO,IAAI,GAAG,KAAK,UAAU;oBAC/B,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,QAAQ;wBAChC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,SAAS;4BAC7B,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;4BAC7B,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;gCAAC;gCAAI;gCAAK;6BAAI,EAAE,uBAAuB;wBACtE,OAAO,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,WAAW;4BACtC,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;4BAC7B,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;gCAAC;gCAAK;gCAAI;6BAAG,EAAG,4BAA4B;wBAC3E;oBACF;gBACF;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,YAAY,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;IAC3D,IAAI;QACF,wDAAwD;QACxD,MAAM,gBAAgB,SAAS,aAAa,CAAC;QAC7C,cAAc,KAAK,CAAC,QAAQ,GAAG;QAC/B,cAAc,KAAK,CAAC,IAAI,GAAG;QAC3B,cAAc,KAAK,CAAC,GAAG,GAAG;QAC1B,cAAc,KAAK,CAAC,KAAK,GAAG,UAAU,qCAAqC;QAC3E,cAAc,KAAK,CAAC,eAAe,GAAG;QACtC,cAAc,KAAK,CAAC,OAAO,GAAG;QAC9B,cAAc,KAAK,CAAC,UAAU,GAAG;QACjC,cAAc,KAAK,CAAC,KAAK,GAAG;QAC5B,cAAc,KAAK,CAAC,SAAS,GAAG;QAEhC,+BAA+B;QAC/B,IAAI,OAAO;YACT,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,WAAW,GAAG;YAC3B,aAAa,KAAK,CAAC,KAAK,GAAG,WAAW,sBAAsB;YAC5D,aAAa,KAAK,CAAC,YAAY,GAAG;YAClC,aAAa,KAAK,CAAC,QAAQ,GAAG;YAC9B,aAAa,KAAK,CAAC,UAAU,GAAG;YAChC,aAAa,KAAK,CAAC,SAAS,GAAG;YAC/B,cAAc,WAAW,CAAC;QAC5B;QAEA,IAAI,UAAU;YACZ,MAAM,kBAAkB,SAAS,aAAa,CAAC;YAC/C,gBAAgB,WAAW,GAAG;YAC9B,gBAAgB,KAAK,CAAC,KAAK,GAAG;YAC9B,gBAAgB,KAAK,CAAC,YAAY,GAAG;YACrC,gBAAgB,KAAK,CAAC,QAAQ,GAAG;YACjC,gBAAgB,KAAK,CAAC,SAAS,GAAG;YAClC,cAAc,WAAW,CAAC;QAC5B;QAEA,iBAAiB;QACjB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,KAAK,CAAC,KAAK,GAAG;QACpB,MAAM,KAAK,CAAC,cAAc,GAAG;QAC7B,MAAM,KAAK,CAAC,YAAY,GAAG;QAC3B,MAAM,KAAK,CAAC,SAAS,GAAG;QAExB,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,UAAU,KAAK,CAAC,eAAe,GAAG,WAAW,sBAAsB;QACnE,UAAU,KAAK,CAAC,KAAK,GAAG;QAExB,QAAQ,OAAO,CAAC,CAAA;YACd,MAAM,KAAK,SAAS,aAAa,CAAC;YAClC,GAAG,WAAW,GAAG,IAAI,MAAM,IAAI,IAAI,GAAG;YACtC,GAAG,KAAK,CAAC,OAAO,GAAG;YACnB,GAAG,KAAK,CAAC,SAAS,GAAG;YACrB,GAAG,KAAK,CAAC,UAAU,GAAG;YACtB,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,YAAY,GAAG;YACxB,UAAU,WAAW,CAAC;QACxB;QAEA,MAAM,WAAW,CAAC;QAClB,MAAM,WAAW,CAAC;QAElB,0BAA0B;QAC1B,MAAM,QAAQ,SAAS,aAAa,CAAC;QAErC,KAAK,OAAO,CAAC,CAAC,MAAM;YAClB,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,IAAI,KAAK,CAAC,eAAe,GAAG,WAAW,MAAM,IAAI,YAAY;YAC7D,IAAI,KAAK,CAAC,YAAY,GAAG;YAEzB,QAAQ,OAAO,CAAC,CAAA;gBACd,MAAM,KAAK,SAAS,aAAa,CAAC;gBAClC,GAAG,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG;gBACzF,GAAG,KAAK,CAAC,OAAO,GAAG;gBACnB,GAAG,KAAK,CAAC,QAAQ,GAAG;gBAEpB,mCAAmC;gBACnC,IAAI,IAAI,GAAG,KAAK,UAAU;oBACxB,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,SAAS;wBAC7B,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,mBAAmB;wBAC/C,GAAG,KAAK,CAAC,UAAU,GAAG;oBACxB,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,WAAW;wBACtC,GAAG,KAAK,CAAC,KAAK,GAAG,WAAW,wBAAwB;wBACpD,GAAG,KAAK,CAAC,UAAU,GAAG;oBACxB;gBACF;gBAEA,IAAI,WAAW,CAAC;YAClB;YAEA,MAAM,WAAW,CAAC;QACpB;QAEA,MAAM,WAAW,CAAC;QAClB,cAAc,WAAW,CAAC;QAE1B,uCAAuC;QACvC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,CAAC,QAAQ,GAAG;QACxB,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,OAAO,KAAK,CAAC,SAAS,GAAG;QACzB,OAAO,KAAK,CAAC,SAAS,GAAG;QAEzB,MAAM,cAAc,IAAI;QACxB,MAAM,gBAAgB,GAAG,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,YAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,WAAW,GAAG,IAAI,EAAE,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAEtR,OAAO,WAAW,GAAG,CAAC,WAAW,EAAE,cAAc,oBAAoB,CAAC;QACtE,cAAc,WAAW,CAAC;QAE1B,0CAA0C;QAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,yDAAyD;QACzD,OAAO,IAAI,QAAQ,CAAC;YAClB,oEAAoE;YACpE,WAAW;gBACT,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,eAAe;oBACzB,OAAO;oBACP,SAAS;oBACT,YAAY;oBACZ,iBAAiB;oBACjB,SAAS;oBACT,iBAAiB;gBACnB,GAAG,IAAI,CAAC,CAAA;oBACN,iCAAiC;oBACjC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAE1B,wCAAwC;oBACxC,OAAO,MAAM,CAAC,CAAA;wBACZ,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,IAAI,CAAC;wBAC9B,QAAQ;oBACV,GAAG;gBACL,GAAG,KAAK,CAAC,CAAA;oBACP,QAAQ,KAAK,CAAC,yCAAyC;oBACvD,iDAAiD;oBACjD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,gBAAgB;wBACzC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;oBACA,QAAQ;gBACV;YACF,GAAG,MAAM,uDAAuD;QAClE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF"}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/utils/apiResponseAdapter.js"], "sourcesContent": ["/**\r\n * Adaptador para processar respostas da API de forma consistente\r\n * Este utilitário garante que as respostas da API sejam processadas de forma padronizada,\r\n * independentemente do formato retornado pelo backend\r\n */\r\n\r\n/**\r\n * Extrai os dados de uma resposta da API, lidando com diferentes formatos possíveis\r\n * @param {Object} response - A resposta da API\r\n * @param {string} entityName - O nome da entidade principal (ex: 'persons', 'clients', 'appointments')\r\n * @param {string[]} alternativeNames - Nomes alternativos para a entidade (ex: ['people'] para 'persons')\r\n * @returns {Object} Dados normalizados com propriedades padronizadas\r\n */\r\nexport const extractData = (response, entityName, alternativeNames = []) => {\r\n  console.log(`extractData chamado para entidade '${entityName}' com alternativas:`, alternativeNames);\r\n  console.log(\"Resposta recebida:\", response);\r\n\r\n  // Se a resposta for nula ou indefinida, retornar objeto vazio\r\n  if (!response) {\r\n    console.log(\"Resposta nula ou indefinida, retornando objeto vazio\");\r\n    return {\r\n      [entityName]: [],\r\n      total: 0,\r\n      pages: 1\r\n    };\r\n  }\r\n\r\n  // Se a resposta já estiver no novo formato padronizado (com campo 'data')\r\n  if (response.success && response.data) {\r\n    console.log(\"Resposta no formato padronizado com campo 'data'\");\r\n    response = response.data; // Use o campo 'data' como a resposta real\r\n  }\r\n\r\n  // Extrair a lista de entidades\r\n  let entities = [];\r\n\r\n  // Verificar se a resposta é um array\r\n  if (Array.isArray(response)) {\r\n    console.log(\"Resposta é um array\");\r\n    entities = response;\r\n  }\r\n  // Verificar se a entidade está no campo esperado\r\n  else if (response[entityName] && Array.isArray(response[entityName])) {\r\n    console.log(`Entidade encontrada no campo esperado '${entityName}'`);\r\n    entities = response[entityName];\r\n  }\r\n  // Verificar nomes alternativos\r\n  else {\r\n    for (const name of alternativeNames) {\r\n      if (response[name] && Array.isArray(response[name])) {\r\n        console.log(`Entidade encontrada no campo alternativo '${name}'`);\r\n        entities = response[name];\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Se ainda não encontrou, verificar campo 'data' genérico\r\n    if (entities.length === 0 && response.data && Array.isArray(response.data)) {\r\n      console.log(\"Entidade encontrada no campo genérico 'data'\");\r\n      entities = response.data;\r\n    }\r\n  }\r\n\r\n  console.log(`Entidades extraídas (${entities.length}):`, entities.slice(0, 2));\r\n\r\n  // Extrair metadados de paginação\r\n  const total = response.total || entities.length || 0;\r\n  const pages = response.pages || Math.ceil(total / 10) || 1;\r\n\r\n  console.log(\"Metadados de paginação:\", { total, pages });\r\n\r\n  // Retornar objeto com formato padronizado\r\n  const result = {\r\n    [entityName]: entities,\r\n    total,\r\n    pages\r\n  };\r\n\r\n  console.log(\"Resultado final do extractData:\", result);\r\n  return result;\r\n};\r\n\r\n/**\r\n * Extrai uma única entidade de uma resposta da API\r\n * @param {Object} response - A resposta da API\r\n * @returns {Object} A entidade extraída\r\n */\r\nexport const extractEntity = (response) => {\r\n  if (!response) {\r\n    return null;\r\n  }\r\n\r\n  // Se a resposta já estiver no novo formato padronizado (com campo 'data')\r\n  if (response.success && response.data) {\r\n    return response.data;\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\n/**\r\n * Extrai mensagem de erro de uma resposta de erro da API\r\n * @param {Error} error - O objeto de erro\r\n * @returns {string} Mensagem de erro formatada\r\n */\r\nexport const extractErrorMessage = (error) => {\r\n  // Verificar se é um erro de resposta da API\r\n  if (error.response) {\r\n    // Novo formato padronizado\r\n    if (error.response.data && error.response.data.error) {\r\n      return error.response.data.error.message;\r\n    }\r\n\r\n    // Formato antigo\r\n    if (error.response.data && error.response.data.message) {\r\n      return error.response.data.message;\r\n    }\r\n\r\n    // Mensagem genérica baseada no status HTTP\r\n    if (error.response.status === 404) {\r\n      return 'Recurso não encontrado';\r\n    }\r\n    if (error.response.status === 401) {\r\n      return 'Não autorizado. Faça login novamente.';\r\n    }\r\n    if (error.response.status === 403) {\r\n      return 'Você não tem permissão para acessar este recurso';\r\n    }\r\n    if (error.response.status >= 500) {\r\n      return 'Erro interno do servidor. Tente novamente mais tarde.';\r\n    }\r\n  }\r\n\r\n  // Erro de rede ou outro erro\r\n  return error.message || 'Ocorreu um erro desconhecido';\r\n};\r\n\r\n/**\r\n * Processa dados de agendamentos para garantir compatibilidade com diferentes formatos de resposta\r\n * @param {Array} appointments - Lista de agendamentos\r\n * @returns {Array} Lista de agendamentos processados\r\n */\r\nexport const processAppointments = (appointments) => {\r\n  if (!appointments || !Array.isArray(appointments)) {\r\n    return [];\r\n  }\r\n\r\n  return appointments.map(appointment => {\r\n    // Extrair informações do provedor\r\n    const provider = appointment.provider || {};\r\n    const providerProfession = provider.profession ||\r\n                             (provider.professionObj ? provider.professionObj.name : null);\r\n\r\n    // Extrair informações da pessoa/paciente\r\n    const person = appointment.Person && appointment.Person.length > 0 ?\r\n                  appointment.Person[0] :\r\n                  appointment.person || {};\r\n\r\n    // Log detalhado para depuração\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      // console.log(\"[API-ADAPTER] Processando agendamento:\", {\r\n      //   id: appointment.id,\r\n      //   personId: appointment.personId,\r\n      //   clientId: appointment.clientId,\r\n      //   Person: appointment.Person,\r\n      //   person: appointment.person,\r\n      //   extractedPerson: person\r\n      // });\r\n    }\r\n\r\n    // Extrair informações do tipo de serviço\r\n    const serviceType = appointment.serviceType || {};\r\n\r\n    // Extrair informações do local\r\n    const location = appointment.location || {};\r\n\r\n    // Extrair o personId de todas as fontes possíveis\r\n    const personId = person.id ||\r\n                    appointment.personId ||\r\n                    appointment.clientId ||\r\n                    \"\";\r\n\r\n    // Log detalhado para depuração\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      // console.log(\"[API-ADAPTER] Fontes de personId:\", {\r\n      //   fromPerson: person.id,\r\n      //   fromPersonId: appointment.personId,\r\n      //   fromClientId: appointment.clientId,\r\n      //   final: personId\r\n      // });\r\n    }\r\n\r\n    // Retornar objeto processado\r\n    return {\r\n      ...appointment,\r\n      // Adicionar campos processados\r\n      providerfullName: provider.fullName || 'Profissional não especificado',\r\n      providerProfession: providerProfession || 'Sem profissão',\r\n      personfullName: person.fullName || 'Paciente não especificado',\r\n      serviceTypefullName: serviceType.name || 'Serviço não especificado',\r\n      locationName: location.name || 'Local não especificado',\r\n      // Garantir que o personId seja definido corretamente\r\n      personId: personId,\r\n      // Adicionar objetos completos para uso no modal\r\n      person: person,\r\n    };\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;CAMC;;;;;;AAmJO;AAlJD,MAAM,cAAc,CAAC,UAAU,YAAY,mBAAmB,EAAE;IACrE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,WAAW,mBAAmB,CAAC,EAAE;IACnF,QAAQ,GAAG,CAAC,sBAAsB;IAElC,8DAA8D;IAC9D,IAAI,CAAC,UAAU;QACb,QAAQ,GAAG,CAAC;QACZ,OAAO;YACL,CAAC,WAAW,EAAE,EAAE;YAChB,OAAO;YACP,OAAO;QACT;IACF;IAEA,0EAA0E;IAC1E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;QACrC,QAAQ,GAAG,CAAC;QACZ,WAAW,SAAS,IAAI,EAAE,0CAA0C;IACtE;IAEA,+BAA+B;IAC/B,IAAI,WAAW,EAAE;IAEjB,qCAAqC;IACrC,IAAI,MAAM,OAAO,CAAC,WAAW;QAC3B,QAAQ,GAAG,CAAC;QACZ,WAAW;IACb,OAEK,IAAI,QAAQ,CAAC,WAAW,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG;QACpE,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,CAAC;QACnE,WAAW,QAAQ,CAAC,WAAW;IACjC,OAEK;QACH,KAAK,MAAM,QAAQ,iBAAkB;YACnC,IAAI,QAAQ,CAAC,KAAK,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG;gBACnD,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;gBAChE,WAAW,QAAQ,CAAC,KAAK;gBACzB;YACF;QACF;QAEA,0DAA0D;QAC1D,IAAI,SAAS,MAAM,KAAK,KAAK,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YAC1E,QAAQ,GAAG,CAAC;YACZ,WAAW,SAAS,IAAI;QAC1B;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,SAAS,KAAK,CAAC,GAAG;IAE3E,iCAAiC;IACjC,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,MAAM,IAAI;IACnD,MAAM,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,OAAO;IAEzD,QAAQ,GAAG,CAAC,2BAA2B;QAAE;QAAO;IAAM;IAEtD,0CAA0C;IAC1C,MAAM,SAAS;QACb,CAAC,WAAW,EAAE;QACd;QACA;IACF;IAEA,QAAQ,GAAG,CAAC,mCAAmC;IAC/C,OAAO;AACT;AAOO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,0EAA0E;IAC1E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;AACT;AAOO,MAAM,sBAAsB,CAAC;IAClC,4CAA4C;IAC5C,IAAI,MAAM,QAAQ,EAAE;QAClB,2BAA2B;QAC3B,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACpD,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;QAC1C;QAEA,iBAAiB;QACjB,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;YACtD,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACpC;QAEA,2CAA2C;QAC3C,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACjC,OAAO;QACT;QACA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACjC,OAAO;QACT;QACA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACjC,OAAO;QACT;QACA,IAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,KAAK;YAChC,OAAO;QACT;IACF;IAEA,6BAA6B;IAC7B,OAAO,MAAM,OAAO,IAAI;AAC1B;AAOO,MAAM,sBAAsB,CAAC;IAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,eAAe;QACjD,OAAO,EAAE;IACX;IAEA,OAAO,aAAa,GAAG,CAAC,CAAA;QACtB,kCAAkC;QAClC,MAAM,WAAW,YAAY,QAAQ,IAAI,CAAC;QAC1C,MAAM,qBAAqB,SAAS,UAAU,IACrB,CAAC,SAAS,aAAa,GAAG,SAAS,aAAa,CAAC,IAAI,GAAG,IAAI;QAErF,yCAAyC;QACzC,MAAM,SAAS,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,MAAM,GAAG,IACnD,YAAY,MAAM,CAAC,EAAE,GACrB,YAAY,MAAM,IAAI,CAAC;QAErC,+BAA+B;QAC/B,IAAI,oDAAyB,cAAc;QACzC,0DAA0D;QAC1D,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,4BAA4B;QAC5B,MAAM;QACR;QAEA,yCAAyC;QACzC,MAAM,cAAc,YAAY,WAAW,IAAI,CAAC;QAEhD,+BAA+B;QAC/B,MAAM,WAAW,YAAY,QAAQ,IAAI,CAAC;QAE1C,kDAAkD;QAClD,MAAM,WAAW,OAAO,EAAE,IACV,YAAY,QAAQ,IACpB,YAAY,QAAQ,IACpB;QAEhB,+BAA+B;QAC/B,IAAI,oDAAyB,cAAc;QACzC,qDAAqD;QACrD,2BAA2B;QAC3B,wCAAwC;QACxC,wCAAwC;QACxC,oBAAoB;QACpB,MAAM;QACR;QAEA,6BAA6B;QAC7B,OAAO;YACL,GAAG,WAAW;YACd,+BAA+B;YAC/B,kBAAkB,SAAS,QAAQ,IAAI;YACvC,oBAAoB,sBAAsB;YAC1C,gBAAgB,OAAO,QAAQ,IAAI;YACnC,qBAAqB,YAAY,IAAI,IAAI;YACzC,cAAc,SAAS,IAAI,IAAI;YAC/B,qDAAqD;YACrD,UAAU;YACV,gDAAgD;YAChD,QAAQ;QACV;IACF;AACF"}}, {"offset": {"line": 2803, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/people/services/personsService.js"], "sourcesContent": ["// src/app/modules/people/services/personsService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { extractData, extractEntity, extractErrorMessage } from \"@/utils/apiResponseAdapter\";\r\n\r\n/**\r\n * Formata o gênero para exibição\r\n * @param {string} gender - Código do gênero (M, F, O, etc)\r\n * @returns {string} - Gênero formatado\r\n */\r\nfunction formatGender(gender) {\r\n  const genderMap = {\r\n    M: \"Masculino\",\r\n    F: \"Feminino\",\r\n    O: \"Outro\"\r\n  };\r\n\r\n  return genderMap[gender] || gender || \"Não informado\";\r\n}\r\n\r\nexport const personsService = {\r\n  // Get persons with optional filters\r\n  getPersons: async (filters = {}) => {\r\n    const {\r\n      page = 1,\r\n      limit = 10,\r\n      search = \"\",\r\n      personIds,\r\n      active,\r\n      clientId,\r\n      relationship,\r\n      companyId,\r\n      sortField = 'fullName', // Default sort by fullName\r\n      sortDirection = 'asc'   // Default sort direction\r\n    } = filters;\r\n\r\n    try {\r\n      console.log(\"getPersons chamado com filtros:\", filters);\r\n\r\n      // Construir parâmetros para a API\r\n      const params = {\r\n        page,\r\n        limit,\r\n        search: search || undefined,\r\n        active: active === undefined ? undefined : active,\r\n        clientId: clientId || undefined,\r\n        relationship: relationship || undefined,\r\n        companyId: companyId || undefined,\r\n        sortField,\r\n        sortDirection\r\n      };\r\n\r\n      // Adicionar personIds como parâmetros separados com notação de array\r\n      if (personIds && personIds.length > 0) {\r\n        // Garantir que personIds seja um array\r\n        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        personIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params[`personIds[${index}]`] = id;\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de pacientes:\", personIdsArray);\r\n      }\r\n\r\n      console.log(\"Enviando requisição para /persons com params:\", params);\r\n      const response = await api.get(\"/persons\", { params });\r\n      console.log(\"Resposta bruta da API de persons:\", response.data);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      const extractedData = extractData(response.data, 'persons', ['people']);\r\n      console.log(\"Dados extraídos pelo adaptador:\", extractedData);\r\n\r\n      return extractedData;\r\n    } catch (error) {\r\n      console.error(\"Error fetching persons:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get a single person by ID\r\n  getPerson: async (id) => {\r\n    try {\r\n      console.log(`Buscando dados da pessoa ${id}`);\r\n      const response = await api.get(`/persons/${id}`);\r\n      console.log('Dados da pessoa recebidos:', response.data);\r\n      console.log('URL da imagem de perfil:', response.data.profileImageFullUrl);\r\n      // Usar o adaptador para extrair a entidade\r\n      return extractEntity(response.data);\r\n    } catch (error) {\r\n      console.error(`Error fetching person ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create a new person\r\n  createPerson: async (personData) => {\r\n    try {\r\n      const response = await api.post(\"/persons\", personData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating person:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update an existing person\r\n  updatePerson: async (id, personData) => {\r\n    try {\r\n      const response = await api.put(`/persons/${id}`, personData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating person ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Toggle person active status\r\n  togglePersonStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/persons/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error toggling status for person ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a person (soft delete)\r\n  deletePerson: async (id) => {\r\n    try {\r\n      const response = await api.delete(`/persons/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error deleting person ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Upload profile image for a person\r\n  uploadProfileImage: async (id, imageFile) => {\r\n    try {\r\n      console.log(`Iniciando upload de imagem para pessoa ${id}`);\r\n      console.log('Arquivo:', imageFile.name, imageFile.type, imageFile.size);\r\n\r\n      const formData = new FormData();\r\n      formData.append('profileImage', imageFile);\r\n      console.log('FormData criado com sucesso');\r\n\r\n      // Verificar o token de autenticação\r\n      const token = localStorage.getItem('token');\r\n      console.log('Token de autenticação:', token ? 'Presente' : 'Ausente');\r\n\r\n      console.log(`Enviando requisição POST para /persons/${id}/profile-image`);\r\n      const response = await api.post(`/persons/${id}/profile-image`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n      });\r\n\r\n      console.log('Resposta recebida:', response.status, response.statusText);\r\n      console.log('Dados da resposta:', response.data);\r\n\r\n      // Verificar se a resposta contém a URL da imagem\r\n      if (response.data && response.data.fullImageUrl) {\r\n        console.log('URL da imagem recebida:', response.data.fullImageUrl);\r\n      } else {\r\n        console.warn('Resposta não contém a URL da imagem');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error uploading profile image for person ${id}:`, error);\r\n      console.error('Detalhes do erro:', error.response?.data || error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get profile image URL for a person\r\n  getProfileImageUrl: (id, profileImageUrl) => {\r\n    if (!id) return null;\r\n\r\n    // Se tiver a URL completa da imagem, usar ela diretamente\r\n    if (profileImageUrl) {\r\n      return `${api.defaults.baseURL}/uploads/${profileImageUrl}`;\r\n    }\r\n\r\n    // Caso contrário, usar a rota da API\r\n    return `${api.defaults.baseURL}/api/persons/${id}/profile-image`;\r\n  },\r\n\r\n  // Get all clients for dropdown selection\r\n  getClientsForSelect: async () => {\r\n    try {\r\n      const response = await api.get(\"/clients\", {\r\n        params: { active: true, limit: 100 }\r\n      });\r\n\r\n      return response.data.clients.map(client => ({\r\n        value: client.id,\r\n        label: client.login\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error fetching clients for select:\", error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get insurances for person\r\n  getPersonInsurances: async (personId) => {\r\n    try {\r\n      const response = await api.get(`/persons/${personId}/insurances`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching insurances for person ${personId}:`, error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Upload document for a person\r\n  uploadDocument: async (personId, file, documentType) => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('documents', file);\r\n      formData.append('types', JSON.stringify([documentType]));\r\n\r\n      const response = await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error uploading document for person ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create contact for a person\r\n  createContact: async (personId, contactData) => {\r\n    try {\r\n      const payload = {\r\n        personId: personId,\r\n        name: contactData.name,\r\n        relationship: contactData.relationship || null,\r\n        email: contactData.email || null,\r\n        phone: contactData.phone ? contactData.phone.replace(/\\D/g, \"\") : null,\r\n        notes: contactData.notes || null\r\n      };\r\n\r\n      const response = await api.post('/contacts', payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error creating contact for person ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Add insurance to a person\r\n  addPersonInsurance: async (personId, insuranceData) => {\r\n    try {\r\n      const payload = {\r\n        personId: personId,\r\n        insuranceId: insuranceData.insuranceId,\r\n        policyNumber: insuranceData.policyNumber || null,\r\n        validUntil: insuranceData.validUntil || null,\r\n        notes: insuranceData.notes || null\r\n      };\r\n\r\n      const response = await api.post('/person-insurances', payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error adding insurance for person ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de pessoas com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportPersons: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await personsService.getPersons({\r\n        ...filters,\r\n        limit: 1000, // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados das pessoas usando o adaptador\r\n      const { persons } = extractData(response, 'persons', ['people']);\r\n      const data = persons;\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"fullName\", header: \"Nome Completo\" },\r\n        { key: \"cpf\", header: \"CPF\", type: \"cpf\" },\r\n        { key: \"email\", header: \"Email\" },\r\n        { key: \"phone\", header: \"Telefone\", type: \"phone\" },\r\n        { key: \"birthDate\", header: \"Data de Nascimento\", type: \"date\" },\r\n        {\r\n          key: \"active\",\r\n          header: \"Status\",\r\n          format: (value) => value ? \"Ativo\" : \"Inativo\",\r\n          align: \"center\",\r\n          width: 20\r\n        },\r\n        { key: \"gender\", header: \"Gênero\", format: formatGender },\r\n        { key: \"relationship\", header: \"Relacionamento\" },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(person => {\r\n        // Para cada pessoa, montamos um objeto com todas as propriedades que queremos exportar\r\n        return {\r\n          fullName: person.fullName || \"\",\r\n          cpf: person.cpf || \"\",\r\n          email: person.email || \"\",\r\n          phone: person.phone || \"\",\r\n          birthDate: person.birthDate || \"\",\r\n          active: person.active,\r\n          gender: person.gender || \"\",\r\n          relationship: person.relationship || \"N/A\",\r\n          createdAt: person.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.personIds && filters.personIds.length > 0) {\r\n        subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);\r\n      }\r\n      if (filters.active !== undefined) {\r\n        subtitleParts.push(`Status: ${filters.active ? \"Ativos\" : \"Inativos\"}`);\r\n      }\r\n      if (filters.relationship) {\r\n        subtitleParts.push(`Tipo: ${filters.relationship}`);\r\n      }\r\n      if (filters.companyId) {\r\n        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"pacientes\",\r\n        columns,\r\n        title: \"Lista de Pacientes\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar pessoas:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default personsService;"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AACpD;AAGA;AACA;AAHA;AACA;;;;;;AAIA;;;;CAIC,GACD,SAAS,aAAa,MAAM;IAC1B,MAAM,YAAY;QAChB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI,UAAU;AACxC;AAEO,MAAM,iBAAiB;IAC5B,oCAAoC;IACpC,YAAY,OAAO,UAAU,CAAC,CAAC;QAC7B,MAAM,EACJ,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,SAAS,EACT,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,YAAY,UAAU,EACtB,gBAAgB,MAAQ,yBAAyB;QAA5B,EACtB,GAAG;QAEJ,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,kCAAkC;YAClC,MAAM,SAAS;gBACb;gBACA;gBACA,QAAQ,UAAU;gBAClB,QAAQ,WAAW,YAAY,YAAY;gBAC3C,UAAU,YAAY;gBACtB,cAAc,gBAAgB;gBAC9B,WAAW,aAAa;gBACxB;gBACA;YACF;YAEA,qEAAqE;YACrE,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,YAAY;oBAAC;iBAAU;gBAEzE,+CAA+C;gBAC/C,eAAe,OAAO,CAAC,CAAC,IAAI;oBAC1B,yDAAyD;oBACzD,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;gBAClC;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;YAEA,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;gBAAE;YAAO;YACpD,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI;YAE9D,8DAA8D;YAC9D,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,WAAW;gBAAC;aAAS;YACtE,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,IAAI;YAC5C,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;YAC/C,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YACvD,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI,CAAC,mBAAmB;YACzE,2CAA2C;YAC3C,OAAO,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;YAC5C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,cAAc,OAAO,IAAI;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,EAAE;YACzD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,oBAAoB,OAAO,IAAI;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI;YAC1D,QAAQ,GAAG,CAAC,YAAY,UAAU,IAAI,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;YAEtE,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,gBAAgB;YAChC,QAAQ,GAAG,CAAC;YAEZ,oCAAoC;YACpC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,QAAQ,GAAG,CAAC,0BAA0B,QAAQ,aAAa;YAE3D,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,GAAG,cAAc,CAAC;YACxE,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,GAAG,cAAc,CAAC,EAAE,UAAU;gBACxE,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,QAAQ,GAAG,CAAC,sBAAsB,SAAS,MAAM,EAAE,SAAS,UAAU;YACtE,QAAQ,GAAG,CAAC,sBAAsB,SAAS,IAAI;YAE/C,iDAAiD;YACjD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;gBAC/C,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI,CAAC,YAAY;YACnE,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC,EAAE;YACjE,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACxE,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,oBAAoB,CAAC,IAAI;QACvB,IAAI,CAAC,IAAI,OAAO;QAEhB,0DAA0D;QAC1D,IAAI,iBAAiB;YACnB,OAAO,GAAG,sHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,iBAAiB;QAC7D;QAEA,qCAAqC;QACrC,OAAO,GAAG,sHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,cAAc,CAAC;IAClE;IAEA,yCAAyC;IACzC,qBAAqB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;gBACzC,QAAQ;oBAAE,QAAQ;oBAAM,OAAO;gBAAI;YACrC;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC1C,OAAO,OAAO,EAAE;oBAChB,OAAO,OAAO,KAAK;gBACrB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;IACF;IAEA,4BAA4B;IAC5B,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,WAAW,CAAC;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC,EAAE;YACnE,OAAO,EAAE;QACX;IACF;IAEA,+BAA+B;IAC/B,gBAAgB,OAAO,UAAU,MAAM;QACrC,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC;gBAAC;aAAa;YAEtD,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,kBAAkB,CAAC,EAAE,UAAU;gBACpG,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,eAAe,OAAO,UAAU;QAC9B,IAAI;YACF,MAAM,UAAU;gBACd,UAAU;gBACV,MAAM,YAAY,IAAI;gBACtB,cAAc,YAAY,YAAY,IAAI;gBAC1C,OAAO,YAAY,KAAK,IAAI;gBAC5B,OAAO,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBAClE,OAAO,YAAY,KAAK,IAAI;YAC9B;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,aAAa;YAC7C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAChE,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,oBAAoB,OAAO,UAAU;QACnC,IAAI;YACF,MAAM,UAAU;gBACd,UAAU;gBACV,aAAa,cAAc,WAAW;gBACtC,cAAc,cAAc,YAAY,IAAI;gBAC5C,YAAY,cAAc,UAAU,IAAI;gBACxC,OAAO,cAAc,KAAK,IAAI;YAChC;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,sBAAsB;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAChE,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,eAAe,OAAO,SAAS,eAAe,MAAM;QAClD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,eAAe,UAAU,CAAC;gBAC/C,GAAG,OAAO;gBACV,OAAO;YACT;YAEA,kDAAkD;YAClD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,UAAU,WAAW;gBAAC;aAAS;YAC/D,MAAM,OAAO;YAEb,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAY,QAAQ;gBAAgB;gBAC3C;oBAAE,KAAK;oBAAO,QAAQ;oBAAO,MAAM;gBAAM;gBACzC;oBAAE,KAAK;oBAAS,QAAQ;gBAAQ;gBAChC;oBAAE,KAAK;oBAAS,QAAQ;oBAAY,MAAM;gBAAQ;gBAClD;oBAAE,KAAK;oBAAa,QAAQ;oBAAsB,MAAM;gBAAO;gBAC/D;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,UAAU;oBACrC,OAAO;oBACP,OAAO;gBACT;gBACA;oBAAE,KAAK;oBAAU,QAAQ;oBAAU,QAAQ;gBAAa;gBACxD;oBAAE,KAAK;oBAAgB,QAAQ;gBAAiB;gBAChD;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,uFAAuF;gBACvF,OAAO;oBACL,UAAU,OAAO,QAAQ,IAAI;oBAC7B,KAAK,OAAO,GAAG,IAAI;oBACnB,OAAO,OAAO,KAAK,IAAI;oBACvB,OAAO,OAAO,KAAK,IAAI;oBACvB,WAAW,OAAO,SAAS,IAAI;oBAC/B,QAAQ,OAAO,MAAM;oBACrB,QAAQ,OAAO,MAAM,IAAI;oBACzB,cAAc,OAAO,YAAY,IAAI;oBACrC,WAAW,OAAO,SAAS,IAAI;gBACjC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACtF;YACA,IAAI,QAAQ,MAAM,KAAK,WAAW;gBAChC,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,WAAW,YAAY;YACxE;YACA,IAAI,QAAQ,YAAY,EAAE;gBACxB,cAAc,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,YAAY,EAAE;YACpD;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE;YAC3E;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 3184, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3190, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/dashboard/ClientHeader.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport {\r\n  Menu, X, LogOut, User, Calendar, Clock,\r\n  Search, Settings, ChevronDown\r\n} from 'lucide-react';\r\nimport { useQuickNav } from '@/contexts/QuickNavContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport { ThemeToggle } from '@/components/ThemeToggle';\r\nimport { APP_VERSION } from '@/config/appConfig';\r\nimport { personsService } from '@/app/modules/people/services/personsService';\r\n\r\n// Header Component adaptado para clientes\r\nconst ClientHeader = ({ toggleSidebar, isSidebarOpen }) => {\r\n  const { user, logout } = useAuth();\r\n  const router = useRouter();\r\n  const { openQuickNav } = useQuickNav();\r\n\r\n  // Pegar primeira letra de cada nome para o avatar\r\n  const getInitials = () => {\r\n    // Verificar se temos uma pessoa associada ao cliente\r\n    if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {\r\n      const fullName = user.persons[0].fullName;\r\n      const names = fullName.split(' ');\r\n      if (names.length === 1) return names[0].charAt(0);\r\n      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;\r\n    }\r\n\r\n    // Fallback para o login do cliente\r\n    return user?.login?.charAt(0) || 'C';\r\n  };\r\n\r\n  // Obter o nome completo da pessoa associada ao cliente ou o login do cliente\r\n  const getDisplayName = () => {\r\n    if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {\r\n      return user.persons[0].fullName;\r\n    }\r\n    return user?.login || 'Cliente';\r\n  };\r\n\r\n  // Obter a URL da imagem de perfil da pessoa associada ao cliente\r\n  const getProfileImage = () => {\r\n    if (user?.persons && user.persons.length > 0) {\r\n      // Primeiro tenta usar a URL completa se disponível\r\n      if (user.persons[0].profileImageFullUrl) {\r\n        return user.persons[0].profileImageFullUrl;\r\n      }\r\n      // Se não tiver a URL completa, mas tiver a URL relativa, usa o serviço para construir a URL\r\n      else if (user.persons[0].profileImageUrl) {\r\n        return personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]\">\r\n      {/* Lado esquerdo: Logo e Toggle */}\r\n      <div className=\"flex items-center gap-3\">\r\n        <button\r\n          onClick={toggleSidebar}\r\n          className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\"\r\n          aria-label={isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\"}\r\n        >\r\n          {isSidebarOpen ? <X size={22} aria-hidden=\"true\" /> : <Menu size={22} aria-hidden=\"true\" />}\r\n        </button>\r\n\r\n        <div className=\"flex items-center\">\r\n          <div className=\"relative\">\r\n            <img\r\n              src=\"/logo_horizontal_sem_fundo.png\"\r\n              alt=\"High Tide Logo\"\r\n              className=\"h-10 mr-2.5 dark:invert dark:text-white\"\r\n            />\r\n            <span className=\"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\">{APP_VERSION}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Lado direito: Pesquisa e Perfil */}\r\n      <div className=\"flex items-center gap-3\">\r\n        {/* Botão de pesquisa rápida */}\r\n        <button\r\n          onClick={openQuickNav}\r\n          className=\"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\"\r\n          aria-label=\"Abrir pesquisa rápida\"\r\n        >\r\n          <Search size={18} className=\"text-gray-400 dark:text-gray-500\" aria-hidden=\"true\" />\r\n          <span className=\"hidden sm:inline\">Pesquisar...</span>\r\n          <div className=\"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\">\r\n            <span>Ctrl + K</span>\r\n          </div>\r\n        </button>\r\n\r\n        {/* Atalhos rápidos para clientes */}\r\n        <button\r\n          onClick={() => router.push('/dashboard/scheduler/calendar')}\r\n          className=\"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n          aria-label=\"Calendário\"\r\n          title=\"Ver calendário\"\r\n        >\r\n          <Calendar size={20} aria-hidden=\"true\" />\r\n        </button>\r\n\r\n        <button\r\n          onClick={() => router.push('/dashboard/scheduler/appointments-report')}\r\n          className=\"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n          aria-label=\"Meus Agendamentos\"\r\n          title=\"Ver meus agendamentos\"\r\n        >\r\n          <Clock size={20} aria-hidden=\"true\" />\r\n        </button>\r\n\r\n        {/* Theme Toggle Button */}\r\n        <ThemeToggle />\r\n\r\n        {/* Divisor vertical */}\r\n        <div className=\"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\" aria-hidden=\"true\"></div>\r\n\r\n        {/* Dropdown de usuário */}\r\n        <div className=\"relative group\">\r\n          <button\r\n            className=\"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n            aria-expanded=\"false\"\r\n            aria-haspopup=\"true\"\r\n            aria-label=\"Menu do usuário\"\r\n          >\r\n            <div className=\"h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden\">\r\n              {getProfileImage() ? (\r\n                <img\r\n                  src={getProfileImage()}\r\n                  alt={`Foto de perfil de ${getDisplayName()}`}\r\n                  className=\"h-10 w-10 rounded-full object-cover\"\r\n                  onError={(e) => {\r\n                    e.target.onerror = null;\r\n                    e.target.style.display = 'none';\r\n                    e.target.parentNode.innerHTML = getInitials();\r\n                  }}\r\n                />\r\n              ) : (\r\n                getInitials()\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"hidden md:block text-left\">\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\">{getDisplayName()}</p>\r\n              <div className=\"text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30\">\r\n                <User size={10} className=\"mr-1\" aria-hidden=\"true\" />\r\n                <span>Cliente</span>\r\n              </div>\r\n            </div>\r\n\r\n            <ChevronDown size={16} className=\"text-gray-400 dark:text-gray-500 hidden md:block\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Menu dropdown */}\r\n          <div className=\"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\"\r\n               role=\"menu\"\r\n               aria-orientation=\"vertical\"\r\n               aria-labelledby=\"user-menu-button\">\r\n            <div className=\"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\">\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">{getDisplayName()}</p>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">{user?.email || '<EMAIL>'}</p>\r\n            </div>\r\n\r\n            {/* <div className=\"px-4 py-2\">\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400\">Empresa</p>\r\n              <p className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">{user?.company?.name || 'Minha Empresa'}</p>\r\n            </div> */}\r\n\r\n            <div className=\"pt-1 mt-1\">\r\n              <button\r\n                onClick={() => router.push('/dashboard/profile')}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                Meu Perfil\r\n              </button>\r\n              <button\r\n                onClick={() => router.push('/dashboard/people/persons')}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                Minhas Pessoas\r\n              </button>\r\n              <button\r\n                onClick={() => router.push('/dashboard/scheduler/appointments-report')}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                Meus Agendamentos\r\n              </button>\r\n              <button\r\n                onClick={logout}\r\n                className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\"\r\n                role=\"menuitem\"\r\n              >\r\n                <LogOut size={14} className=\"mr-2\" aria-hidden=\"true\" />\r\n                Sair do Sistema\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default ClientHeader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AACA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;;;;;;AAcA,0CAA0C;AAC1C,MAAM,eAAe,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE;;IACpD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAEnC,kDAAkD;IAClD,MAAM,cAAc;QAClB,qDAAqD;QACrD,IAAI,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE;YACxE,MAAM,WAAW,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;YACzC,MAAM,QAAQ,SAAS,KAAK,CAAC;YAC7B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;YAC/C,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI;QACpE;QAEA,mCAAmC;QACnC,OAAO,MAAM,OAAO,OAAO,MAAM;IACnC;IAEA,6EAA6E;IAC7E,MAAM,iBAAiB;QACrB,IAAI,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE;YACxE,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QACjC;QACA,OAAO,MAAM,SAAS;IACxB;IAEA,iEAAiE;IACjE,MAAM,kBAAkB;QACtB,IAAI,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;YAC5C,mDAAmD;YACnD,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE;gBACvC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,mBAAmB;YAC5C,OAEK,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE;gBACxC,OAAO,gKAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,eAAe;YAC9F;QACF;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAY,gBAAgB,wBAAwB;kCAEnD,8BAAgB,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;iDAAY,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAGpF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAiF,6HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;0BAMlH,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;;0CAEX,6LAAC,yMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;gCAAmC,eAAY;;;;;;0CAC3E,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,cAAW;wBACX,OAAM;kCAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAGlC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,cAAW;wBACX,OAAM;kCAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAI/B,6LAAC,mIAAA,CAAA,cAAW;;;;;kCAGZ,6LAAC;wBAAI,WAAU;wBAAyD,eAAY;;;;;;kCAGpF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,iBAAc;gCACd,iBAAc;gCACd,cAAW;;kDAEX,6LAAC;wCAAI,WAAU;kDACZ,kCACC,6LAAC;4CACC,KAAK;4CACL,KAAK,CAAC,kBAAkB,EAAE,kBAAkB;4CAC5C,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,MAAM,CAAC,OAAO,GAAG;gDACnB,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;gDACzB,EAAE,MAAM,CAAC,UAAU,CAAC,SAAS,GAAG;4CAClC;;;;;mDAGF;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqE;;;;;;0DAClF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;wDAAO,eAAY;;;;;;kEAC7C,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,6LAAC,uNAAA,CAAA,cAAW;wCAAC,MAAM;wCAAI,WAAU;wCAAmD,eAAY;;;;;;;;;;;;0CAIlG,6LAAC;gCAAI,WAAU;gCACV,MAAK;gCACL,oBAAiB;gCACjB,mBAAgB;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAwD;;;;;;0DACrE,6LAAC;gDAAE,WAAU;0DAAqD,MAAM,SAAS;;;;;;;;;;;;kDAQnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;gDACV,MAAK;0DACN;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;gDACV,MAAK;0DACN;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;gDACV,MAAK;0DACN;;;;;;0DAGD,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,MAAK;;kEAEL,6LAAC,6MAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;wDAAO,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GAjMM;;QACqB,iIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACC,qIAAA,CAAA,cAAW;;;KAHhC;uCAmMS"}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3643, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/utils/constructionUtils.js"], "sourcesContent": ["\"use client\";\n\n// Lista de módulos sem funcionalidade implementada\nexport const underConstructionModules = [\n  'financial',\n  'hr'\n];\n\n// Lista de submenus sem funcionalidade implementada\nexport const underConstructionSubmenus = [\n  'admin.logs',\n  'admin.backup',\n  'financial.reports',\n  'financial.invoices',\n  'financial.payments',\n  'financial.expenses',\n  'hr.payroll',\n  'hr.benefits',\n  'hr.attendance',\n  'scheduling.appointments-dashboard'\n];\n\n// Mensagens personalizadas para cada submenu em construção\nexport const constructionMessages = {\n  'admin.logs': {\n    title: 'Logs do Sistema em Construção',\n    content: 'O módulo de logs do sistema está em desenvolvimento. Em breve você poderá visualizar todas as atividades realizadas no sistema.',\n    icon: 'AlertTriangle'\n  },\n  'admin.backup': {\n    title: 'Backup em Construção',\n    content: 'O módulo de backup está em desenvolvimento. Em breve você poderá realizar backups e restaurações dos dados do sistema.',\n    icon: 'Database'\n  },\n  'financial.reports': {\n    title: 'Relatórios Financeiros em Construção',\n    content: 'O módulo de relatórios financeiros está em desenvolvimento. Em breve você poderá gerar relatórios detalhados sobre as finanças da empresa.',\n    icon: 'BarChart'\n  },\n  'financial.invoices': {\n    title: 'Faturas em Construção',\n    content: 'O módulo de faturas está em desenvolvimento. Em breve você poderá gerenciar todas as faturas da empresa.',\n    icon: 'FileText'\n  },\n  'financial.payments': {\n    title: 'Pagamentos em Construção',\n    content: 'O módulo de pagamentos está em desenvolvimento. Em breve você poderá gerenciar todos os pagamentos da empresa.',\n    icon: 'CreditCard'\n  },\n  'financial.expenses': {\n    title: 'Despesas em Construção',\n    content: 'O módulo de despesas está em desenvolvimento. Em breve você poderá gerenciar todas as despesas da empresa.',\n    icon: 'DollarSign'\n  },\n  'hr.payroll': {\n    title: 'Folha de Pagamento em Construção',\n    content: 'O módulo de folha de pagamento está em desenvolvimento. Em breve você poderá gerenciar os salários e benefícios dos funcionários.',\n    icon: 'Briefcase'\n  },\n  'hr.benefits': {\n    title: 'Benefícios em Construção',\n    content: 'O módulo de benefícios está em desenvolvimento. Em breve você poderá gerenciar os benefícios oferecidos aos funcionários.',\n    icon: 'Gift'\n  },\n  'hr.attendance': {\n    title: 'Presença em Construção',\n    content: 'O módulo de controle de presença está em desenvolvimento. Em breve você poderá gerenciar a frequência dos funcionários.',\n    icon: 'Clock'\n  },\n  'scheduling.appointments-dashboard': {\n    title: 'Dashboard de Agendamentos em Construção',\n    content: 'O dashboard de agendamentos está em desenvolvimento. Em breve você poderá visualizar estatísticas e análises sobre os agendamentos.',\n    icon: 'BarChart'\n  }\n};\n\n/**\n * Verifica se um módulo ou submódulo está em construção\n * @param {string} moduleId - ID do módulo\n * @param {string} [submenuId] - ID do submenu (opcional)\n * @returns {boolean} - Retorna true se o módulo ou submódulo estiver em construção\n */\nexport const isUnderConstruction = (moduleId, submenuId = null) => {\n  // Verificar se o módulo inteiro está em construção\n  if (underConstructionModules.includes(moduleId)) {\n    return true;\n  }\n\n  // Verificar se o submenu específico está em construção\n  if (submenuId) {\n    return underConstructionSubmenus.includes(`${moduleId}.${submenuId}`);\n  }\n\n  // Caso especial: o módulo admin não deve ser considerado em construção na página inicial\n  if (moduleId === 'admin') {\n    return false;\n  }\n\n  // Verificar se todos os submenus do módulo estão em construção\n  return underConstructionSubmenus.filter(key => key.startsWith(`${moduleId}.`)).length > 0;\n};\n\n/**\n * Obtém a mensagem de construção para um módulo ou submódulo\n * @param {string} moduleId - ID do módulo\n * @param {string} [submenuId] - ID do submenu (opcional)\n * @returns {Object} - Retorna a mensagem de construção\n */\nexport const getConstructionMessage = (moduleId, submenuId = null) => {\n  // Mensagens específicas para módulos\n  const moduleMessages = {\n    'financial': {\n      title: 'Módulo Financeiro em Construção',\n      content: 'O módulo financeiro está em desenvolvimento e estará disponível em breve. Você poderá gerenciar faturas, pagamentos, despesas e relatórios financeiros.',\n      icon: 'DollarSign'\n    },\n    'hr': {\n      title: 'Módulo de RH em Construção',\n      content: 'O módulo de Recursos Humanos está em desenvolvimento e estará disponível em breve. Você poderá gerenciar funcionários, folha de pagamento, benefícios e muito mais.',\n      icon: 'Users'\n    }\n  };\n\n  // Se for um módulo inteiro em construção\n  if (underConstructionModules.includes(moduleId) && !submenuId) {\n    return moduleMessages[moduleId] || {\n      title: 'Módulo em Construção',\n      content: 'Este módulo está em desenvolvimento e estará disponível em breve.',\n      icon: 'Construction'\n    };\n  }\n\n  // Se for um submenu específico\n  if (submenuId) {\n    const key = `${moduleId}.${submenuId}`;\n    return constructionMessages[key] || {\n      title: 'Em Construção',\n      content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',\n      icon: 'Construction'\n    };\n  }\n\n  return {\n    title: 'Módulo em Construção',\n    content: 'Este módulo está em desenvolvimento e estará disponível em breve.',\n    icon: 'Construction'\n  };\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AAGO,MAAM,2BAA2B;IACtC;IACA;CACD;AAGM,MAAM,4BAA4B;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,uBAAuB;IAClC,cAAc;QACZ,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,gBAAgB;QACd,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,qBAAqB;QACnB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,sBAAsB;QACpB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,sBAAsB;QACpB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,sBAAsB;QACpB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,cAAc;QACZ,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,eAAe;QACb,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,qCAAqC;QACnC,OAAO;QACP,SAAS;QACT,MAAM;IACR;AACF;AAQO,MAAM,sBAAsB,CAAC,UAAU,YAAY,IAAI;IAC5D,mDAAmD;IACnD,IAAI,yBAAyB,QAAQ,CAAC,WAAW;QAC/C,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI,WAAW;QACb,OAAO,0BAA0B,QAAQ,CAAC,GAAG,SAAS,CAAC,EAAE,WAAW;IACtE;IAEA,yFAAyF;IACzF,IAAI,aAAa,SAAS;QACxB,OAAO;IACT;IAEA,+DAA+D;IAC/D,OAAO,0BAA0B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,MAAM,GAAG;AAC1F;AAQO,MAAM,yBAAyB,CAAC,UAAU,YAAY,IAAI;IAC/D,qCAAqC;IACrC,MAAM,iBAAiB;QACrB,aAAa;YACX,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,OAAO;YACP,SAAS;YACT,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,IAAI,yBAAyB,QAAQ,CAAC,aAAa,CAAC,WAAW;QAC7D,OAAO,cAAc,CAAC,SAAS,IAAI;YACjC,OAAO;YACP,SAAS;YACT,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,IAAI,WAAW;QACb,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,WAAW;QACtC,OAAO,oBAAoB,CAAC,IAAI,IAAI;YAClC,OAAO;YACP,SAAS;YACT,MAAM;QACR;IACF;IAEA,OAAO;QACL,OAAO;QACP,SAAS;QACT,MAAM;IACR;AACF"}}, {"offset": {"line": 3775, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3781, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/dashboard/Sidebar/index.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useCallback, useState, useEffect } from 'react';\r\nimport { ChevronLeft, ChevronDown, ChevronRight, Construction, HardHat, Hammer, Wrench, AlertTriangle } from 'lucide-react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { modules, moduleSubmenus } from '@/app/dashboard/components';\r\nimport { usePermissions } from '@/hooks/usePermissions';\r\nimport CustomScrollArea from '@/components/ui/CustomScrollArea';\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\nimport { underConstructionSubmenus, constructionMessages, isUnderConstruction, getConstructionMessage } from '@/utils/constructionUtils';\r\n\r\nconst ModuleTitle = ({ moduleId, title, icon: Icon }) => {\r\n  return (\r\n    <div className=\"mb-6\">\r\n      {/* Card com gradiente e borda temática */}\r\n      <div className={`\r\n        relative overflow-hidden rounded-xl p-4 mb-4\r\n        bg-gradient-to-r from-white dark:from-gray-800 to-module-${moduleId}-bg/30 dark:to-module-${moduleId}-bg-dark/30\r\n        border-l-4 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark\r\n        shadow-sm dark:shadow-md dark:shadow-black/20\r\n      `}>\r\n        <div className=\"flex items-center\">\r\n          {/* Ícone grande do módulo */}\r\n          <div className={`\r\n            w-12 h-12 rounded-lg mr-3 flex items-center justify-center\r\n            bg-module-${moduleId}-bg/70 dark:bg-module-${moduleId}-bg-dark/70\r\n            text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark\r\n          `}>\r\n            <Icon size={26} />\r\n          </div>\r\n\r\n          <div>\r\n            {/* Título em duas partes com pesos diferentes */}\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold\">Módulo</span>\r\n              <h2 className=\"text-xl font-bold text-gray-800 dark:text-gray-100\">{title}</h2>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n\r\n\r\n// Mapeamento de submenus para permissões\r\nconst submenuPermissionsMap = {\r\n  // Admin\r\n  'admin.introduction': 'admin.dashboard.view',\r\n  'admin.dashboard': 'admin.dashboard.view',\r\n  'admin.users': 'admin.users.view',\r\n  'admin.professions': ['admin.professions.view', 'admin.profession-groups.view'],\r\n  'admin.logs': 'admin.logs.view',\r\n  'admin.settings': 'admin.config.edit',\r\n  'admin.backup': 'admin.config.edit',\r\n\r\n  // ABA+\r\n  'abaplus.anamnese': 'abaplus.anamnese.view',\r\n  'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\r\n  'abaplus.sessao': 'abaplus.sessao.view',\r\n\r\n  // Financeiro\r\n  'financial.invoices': 'financial.invoices.view',\r\n  'financial.payments': 'financial.payments.view',\r\n  'financial.expenses': 'financial.expenses.view',\r\n  'financial.reports': 'financial.reports.view',\r\n  'financial.cashflow': 'financial.reports.view',\r\n\r\n  // RH\r\n  'hr.employees': 'rh.employees.view',\r\n  'hr.payroll': 'rh.payroll.view',\r\n  'hr.documents': 'rh.employees.view',\r\n  'hr.departments': 'rh.employees.view',\r\n  'hr.attendance': 'rh.attendance.view',\r\n  'hr.benefits': 'rh.benefits.view',\r\n  'hr.training': 'rh.employees.view',\r\n\r\n  // Pessoas\r\n  'people.clients': 'people.clients.view',\r\n  'people.persons': 'people.persons.view',\r\n  'people.insurances': 'people.insurances.view',\r\n  'people.insurance-limits': 'people.insurance-limits.view',\r\n\r\n  // Agendamento\r\n  'scheduler.calendar': 'scheduling.calendar.view',\r\n  'scheduler.working-hours': 'scheduling.working-hours.view',\r\n  'scheduler.service-types': 'scheduling.service-types.view',\r\n  'scheduler.locations': 'scheduling.locations.view',\r\n  'scheduler.occupancy': 'scheduling.occupancy.view',\r\n  'scheduler.appointments-report': 'scheduling.appointments-report.view',\r\n  'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view',\r\n};\r\n\r\nconst Sidebar = ({\r\n  activeModule,\r\n  activeModuleTitle,\r\n  isSubmenuActive,\r\n  handleModuleSubmenuClick,\r\n  handleBackToModules,\r\n  isSidebarOpen\r\n}) => {\r\n  const { can, hasModule, isAdmin } = usePermissions();\r\n  const pathname = usePathname();\r\n\r\n  // Inicializar o estado de grupos expandidos a partir do localStorage\r\n  const [expandedGroups, setExpandedGroups] = useState(() => {\r\n    // Verificar se estamos no cliente (browser) antes de acessar localStorage\r\n    if (typeof window !== 'undefined') {\r\n      const savedState = localStorage.getItem('sidebarExpandedGroups');\r\n      return savedState ? JSON.parse(savedState) : {};\r\n    }\r\n    return {};\r\n  });\r\n\r\n  // Encontrar o objeto do módulo ativo para acessar seu ícone\r\n  const activeModuleObject = modules.find(m => m.id === activeModule);\r\n  const ModuleIcon = activeModuleObject?.icon;\r\n\r\n  // Função para verificar se o usuário tem permissão para ver um submenu\r\n  const hasPermissionForSubmenu = useCallback((moduleId, submenuId) => {\r\n    // Ignorar verificação para grupos, pois a permissão é verificada para cada item\r\n    if (submenuId === 'cadastro' || submenuId === 'configuracoes' ||\r\n      submenuId === 'gestao' || submenuId === 'convenios' ||\r\n      submenuId === 'financeiro' || submenuId === 'relatorios') {\r\n      return true;\r\n    }\r\n\r\n    const permissionKey = `${moduleId}.${submenuId}`;\r\n    const requiredPermission = submenuPermissionsMap[permissionKey];\r\n\r\n    // Se não há mapeamento de permissão, permitir acesso\r\n    if (!requiredPermission) return true;\r\n\r\n    // Administradores têm acesso a tudo\r\n    if (isAdmin()) return true;\r\n\r\n    // Verificar permissão específica\r\n    if (Array.isArray(requiredPermission)) {\r\n      // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\r\n      return requiredPermission.some(perm => can(perm));\r\n    } else {\r\n      // Se for uma única permissão, verificar normalmente\r\n      return can(requiredPermission);\r\n    }\r\n  }, [can, isAdmin]);\r\n\r\n  // Função para alternar a expansão de um grupo\r\n  const toggleGroup = (groupId) => {\r\n    setExpandedGroups(prev => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId]\r\n    }));\r\n  };\r\n\r\n  // Verificar se algum item dentro de um grupo está ativo\r\n  const isGroupActive = useCallback((moduleId, groupItems) => {\r\n    return groupItems.some(item => isSubmenuActive(moduleId, item.id));\r\n  }, [isSubmenuActive]);\r\n\r\n  // Expandir automaticamente grupos que contêm o item ativo\r\n  useEffect(() => {\r\n    if (activeModule && moduleSubmenus[activeModule]) {\r\n      const newExpandedGroups = { ...expandedGroups };\r\n\r\n      moduleSubmenus[activeModule].forEach(submenu => {\r\n        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\r\n          newExpandedGroups[submenu.id] = true;\r\n        }\r\n      });\r\n\r\n      setExpandedGroups(newExpandedGroups);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [activeModule, pathname, isGroupActive]);\r\n\r\n  // Verificar se um item de submenu tem permissão para ser exibido\r\n  const hasPermissionForSubmenuItem = useCallback((moduleId, submenuId) => {\r\n    return hasPermissionForSubmenu(moduleId, submenuId);\r\n  }, [hasPermissionForSubmenu]);\r\n\r\n  return (\r\n    <aside\r\n      className={`w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'\r\n        }`}\r\n      aria-label=\"Navegação lateral\"\r\n    >\r\n      {/* Conteúdo principal da sidebar */}\r\n      <CustomScrollArea className=\"flex-1 p-5\" moduleColor={activeModule}>\r\n        {/* Título do módulo estilizado */}\r\n        {activeModuleObject && (\r\n          <ModuleTitle\r\n            moduleId={activeModule}\r\n            title={activeModuleTitle}\r\n            icon={ModuleIcon}\r\n          />\r\n        )}\r\n\r\n        <nav\r\n          className=\"space-y-2\"\r\n          aria-labelledby=\"sidebar-heading\"\r\n        >\r\n          {activeModule && moduleSubmenus[activeModule]?.map((submenu) => {\r\n            // Verificar se é um grupo ou um item individual\r\n            if (submenu.type === 'group') {\r\n              // Verificar se algum item do grupo tem permissão para ser exibido\r\n              const hasAnyPermission = submenu.items.some(item =>\r\n                hasPermissionForSubmenuItem(activeModule, item.id)\r\n              );\r\n\r\n              if (!hasAnyPermission) {\r\n                return null; // Não renderizar o grupo se nenhum item tiver permissão\r\n              }\r\n\r\n              const isGroupExpanded = expandedGroups[submenu.id] || false;\r\n              const isAnyItemActive = isGroupActive(activeModule, submenu.items);\r\n\r\n              return (\r\n                <div key={submenu.id} className=\"space-y-1\">\r\n                  {/* Cabeçalho do grupo */}\r\n                  <button\r\n                    onClick={() => toggleGroup(submenu.id)}\r\n                    className={`\r\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\r\n                      ${isAnyItemActive\r\n                        ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark font-medium`\r\n                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                      }\r\n                    `}\r\n                    aria-expanded={isGroupExpanded}\r\n                  >\r\n                    <span className=\"font-medium text-left\">{submenu.title}</span>\r\n                    <div className=\"text-gray-500 dark:text-gray-400\">\r\n                      {isGroupExpanded ? (\r\n                        <ChevronDown size={18} aria-hidden=\"true\" />\r\n                      ) : (\r\n                        <ChevronRight size={18} aria-hidden=\"true\" />\r\n                      )}\r\n                    </div>\r\n                  </button>\r\n\r\n                  {/* Itens do grupo - visíveis apenas quando expandido */}\r\n                  {isGroupExpanded && (\r\n                    <div className=\"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\">\r\n                      {submenu.items.map(item => {\r\n                        const isItemActive = isSubmenuActive(activeModule, item.id);\r\n\r\n                        // Verificar permissão antes de renderizar o item\r\n                        if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\r\n                          return null; // Não renderizar se não tiver permissão\r\n                        }\r\n\r\n                        // Verificar se o item está em construção\r\n                        const itemKey = `${activeModule}.${item.id}`;\r\n                        const isItemUnderConstruction = isUnderConstruction(activeModule, item.id);\r\n                        const constructionMessage = getConstructionMessage(activeModule, item.id);\r\n\r\n                        // Estilo comum para os itens\r\n                        const itemClassName = `\r\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\r\n                          ${isItemActive\r\n                            ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark\r\n                               bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90\r\n                               shadow-md dark:shadow-black/20`\r\n                            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                          }\r\n                        `;\r\n\r\n                        // Se estiver em construção, usar o ConstructionButton\r\n                        if (isItemUnderConstruction) {\r\n                          return (\r\n                            <ConstructionButton\r\n                              key={item.id}\r\n                              className={itemClassName}\r\n                              aria-current={isItemActive ? 'page' : undefined}\r\n                              title={constructionMessage.title}\r\n                              content={constructionMessage.content}\r\n                              icon={constructionMessage.icon}\r\n                              position=\"right\"\r\n                            >\r\n                              {item.icon && (\r\n                                <div className={`\r\n                                  ${isItemActive\r\n                                    ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`\r\n                                    : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`\r\n                                  }\r\n                                `}>\r\n                                  <item.icon\r\n                                    size={18}\r\n                                    aria-hidden=\"true\"\r\n                                  />\r\n                                </div>\r\n                              )}\r\n                              <span className={`font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`}>{item.title}</span>\r\n                            </ConstructionButton>\r\n                          );\r\n                        }\r\n\r\n                        // Se não estiver em construção, usar o botão normal\r\n                        return (\r\n                          <button\r\n                            key={item.id}\r\n                            onClick={() => handleModuleSubmenuClick(activeModule, item.id)}\r\n                            className={itemClassName}\r\n                            aria-current={isItemActive ? 'page' : undefined}\r\n                          >\r\n                            {item.icon && (\r\n                              <div className={`\r\n                                ${isItemActive\r\n                                  ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`\r\n                                  : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`\r\n                                }\r\n                              `}>\r\n                                <item.icon\r\n                                  size={18}\r\n                                  aria-hidden=\"true\"\r\n                                />\r\n                              </div>\r\n                            )}\r\n                            <span className={`font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`}>{item.title}</span>\r\n                          </button>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              );\r\n            } else {\r\n              // Renderização de itens individuais (não agrupados)\r\n              const isActive = isSubmenuActive(activeModule, submenu.id);\r\n\r\n              // Verificar permissão antes de renderizar o item\r\n              if (!hasPermissionForSubmenu(activeModule, submenu.id)) {\r\n                return null; // Não renderizar se não tiver permissão\r\n              }\r\n\r\n              // Verificar se o submenu está em construção\r\n              const submenuKey = `${activeModule}.${submenu.id}`;\r\n              const isSubmenuUnderConstruction = isUnderConstruction(activeModule, submenu.id);\r\n              const constructionMessage = getConstructionMessage(activeModule, submenu.id);\r\n\r\n              // Estilo comum para ambos os tipos de botões\r\n              const buttonClassName = `\r\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\r\n                ${isActive\r\n                  ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark\r\n                     bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90\r\n                     shadow-md dark:shadow-black/20`\r\n                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                }\r\n              `;\r\n\r\n              // Se estiver em construção, usar o ConstructionButton\r\n              if (isSubmenuUnderConstruction) {\r\n                return (\r\n                  <ConstructionButton\r\n                    key={submenu.id}\r\n                    className={buttonClassName}\r\n                    aria-current={isActive ? 'page' : undefined}\r\n                    title={constructionMessage.title}\r\n                    content={constructionMessage.content}\r\n                    icon={constructionMessage.icon}\r\n                    position=\"right\"\r\n                  >\r\n                    {submenu.icon && (\r\n                      <div className={`\r\n                      ${isActive\r\n                          ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`\r\n                          : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`\r\n                        }\r\n                    `}>\r\n                        <submenu.icon\r\n                          size={20}\r\n                          aria-hidden=\"true\"\r\n                        />\r\n                      </div>\r\n                    )}\r\n                    <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>\r\n                  </ConstructionButton>\r\n                );\r\n              }\r\n\r\n              // Se não estiver em construção, usar o botão normal\r\n              return (\r\n                <button\r\n                  key={submenu.id}\r\n                  onClick={() => handleModuleSubmenuClick(activeModule, submenu.id)}\r\n                  className={buttonClassName}\r\n                  aria-current={isActive ? 'page' : undefined}\r\n                >\r\n                  {submenu.icon && (\r\n                    <div className={`\r\n                      ${isActive\r\n                        ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`\r\n                        : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`\r\n                      }\r\n                    `}>\r\n                      <submenu.icon\r\n                        size={20}\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                  <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>\r\n                </button>\r\n              );\r\n            }\r\n          })}\r\n        </nav>\r\n      </CustomScrollArea>\r\n\r\n      {/* Botão de voltar fixo na parte inferior */}\r\n      <div className=\"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\">\r\n        <button\r\n          onClick={handleBackToModules}\r\n          className={`\r\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\r\n            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark\r\n            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10\r\n            transition-all duration-300\r\n          `}\r\n          aria-label=\"Voltar para o dashboard principal\"\r\n        >\r\n          <div className={`\r\n            flex items-center justify-center w-8 h-8 rounded-full\r\n            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70\r\n            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark\r\n            shadow-sm dark:shadow-md dark:shadow-black/20\r\n            group-hover:scale-110 transition-transform duration-200\r\n          `}>\r\n            <ChevronLeft size={20} aria-hidden=\"true\" />\r\n          </div>\r\n          <span className=\"font-medium text-gray-800 dark:text-gray-200\">Voltar a Tela Inicial</span>\r\n        </button>\r\n      </div>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AANA;AAAA;AAAA;;;AAHA;;;;;;;;;;AAYA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE;IAClD,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAW,CAAC;;iEAE0C,EAAE,SAAS,sBAAsB,EAAE,SAAS;iCAC5E,EAAE,SAAS,2BAA2B,EAAE,SAAS;;MAE5E,CAAC;sBACC,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC;;sBAEL,EAAE,SAAS,sBAAsB,EAAE,SAAS;wBAC1C,EAAE,SAAS,uBAAuB,EAAE,SAAS;UAC3D,CAAC;kCACC,cAAA,6LAAC;4BAAK,MAAM;;;;;;;;;;;kCAGd,6LAAC;kCAEC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkF;;;;;;8CAClG,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;KA/BM;AAmCN,yCAAyC;AACzC,MAAM,wBAAwB;IAC5B,QAAQ;IACR,sBAAsB;IACtB,mBAAmB;IACnB,eAAe;IACf,qBAAqB;QAAC;QAA0B;KAA+B;IAC/E,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAEhB,OAAO;IACP,oBAAoB;IACpB,6BAA6B;IAC7B,kBAAkB;IAElB,aAAa;IACb,sBAAsB;IACtB,sBAAsB;IACtB,sBAAsB;IACtB,qBAAqB;IACrB,sBAAsB;IAEtB,KAAK;IACL,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,kBAAkB;IAClB,iBAAiB;IACjB,eAAe;IACf,eAAe;IAEf,UAAU;IACV,kBAAkB;IAClB,kBAAkB;IAClB,qBAAqB;IACrB,2BAA2B;IAE3B,cAAc;IACd,sBAAsB;IACtB,2BAA2B;IAC3B,2BAA2B;IAC3B,uBAAuB;IACvB,uBAAuB;IACvB,iCAAiC;IACjC,oCAAoC;AACtC;AAEA,MAAM,UAAU,CAAC,EACf,YAAY,EACZ,iBAAiB,EACjB,eAAe,EACf,wBAAwB,EACxB,mBAAmB,EACnB,aAAa,EACd;;IACC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qEAAqE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;4BAAE;YACnD,0EAA0E;YAC1E,wCAAmC;gBACjC,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,OAAO,aAAa,KAAK,KAAK,CAAC,cAAc,CAAC;YAChD;;QAEF;;IAEA,4DAA4D;IAC5D,MAAM,qBAAqB,wIAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtD,MAAM,aAAa,oBAAoB;IAEvC,uEAAuE;IACvE,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,UAAU;YACrD,gFAAgF;YAChF,IAAI,cAAc,cAAc,cAAc,mBAC5C,cAAc,YAAY,cAAc,eACxC,cAAc,gBAAgB,cAAc,cAAc;gBAC1D,OAAO;YACT;YAEA,MAAM,gBAAgB,GAAG,SAAS,CAAC,EAAE,WAAW;YAChD,MAAM,qBAAqB,qBAAqB,CAAC,cAAc;YAE/D,qDAAqD;YACrD,IAAI,CAAC,oBAAoB,OAAO;YAEhC,oCAAoC;YACpC,IAAI,WAAW,OAAO;YAEtB,iCAAiC;YACjC,IAAI,MAAM,OAAO,CAAC,qBAAqB;gBACrC,iFAAiF;gBACjF,OAAO,mBAAmB,IAAI;oEAAC,CAAA,OAAQ,IAAI;;YAC7C,OAAO;gBACL,oDAAoD;gBACpD,OAAO,IAAI;YACb;QACF;uDAAG;QAAC;QAAK;KAAQ;IAEjB,8CAA8C;IAC9C,MAAM,cAAc,CAAC;QACnB,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,wDAAwD;IACxD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,UAAU;YAC3C,OAAO,WAAW,IAAI;sDAAC,CAAA,OAAQ,gBAAgB,UAAU,KAAK,EAAE;;QAClE;6CAAG;QAAC;KAAgB;IAEpB,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,gBAAgB,wIAAA,CAAA,iBAAc,CAAC,aAAa,EAAE;gBAChD,MAAM,oBAAoB;oBAAE,GAAG,cAAc;gBAAC;gBAE9C,wIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,OAAO;yCAAC,CAAA;wBACnC,IAAI,QAAQ,IAAI,KAAK,WAAW,cAAc,cAAc,QAAQ,KAAK,GAAG;4BAC1E,iBAAiB,CAAC,QAAQ,EAAE,CAAC,GAAG;wBAClC;oBACF;;gBAEA,kBAAkB;YACpB;QACA,uDAAuD;QACzD;4BAAG;QAAC;QAAc;QAAU;KAAc;IAE1C,iEAAiE;IACjE,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,UAAU;YACzD,OAAO,wBAAwB,UAAU;QAC3C;2DAAG;QAAC;KAAwB;IAE5B,qBACE,6LAAC;QACC,WAAW,CAAC,6HAA6H,EAAE,gBAAgB,kBAAkB,sCACzK;QACJ,cAAW;;0BAGX,6LAAC,8IAAA,CAAA,UAAgB;gBAAC,WAAU;gBAAa,aAAa;;oBAEnD,oCACC,6LAAC;wBACC,UAAU;wBACV,OAAO;wBACP,MAAM;;;;;;kCAIV,6LAAC;wBACC,WAAU;wBACV,mBAAgB;kCAEf,gBAAgB,wIAAA,CAAA,iBAAc,CAAC,aAAa,EAAE,IAAI,CAAC;4BAClD,gDAAgD;4BAChD,IAAI,QAAQ,IAAI,KAAK,SAAS;gCAC5B,kEAAkE;gCAClE,MAAM,mBAAmB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAA,OAC1C,4BAA4B,cAAc,KAAK,EAAE;gCAGnD,IAAI,CAAC,kBAAkB;oCACrB,OAAO,MAAM,wDAAwD;gCACvE;gCAEA,MAAM,kBAAkB,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI;gCACtD,MAAM,kBAAkB,cAAc,cAAc,QAAQ,KAAK;gCAEjE,qBACE,6LAAC;oCAAqB,WAAU;;sDAE9B,6LAAC;4CACC,SAAS,IAAM,YAAY,QAAQ,EAAE;4CACrC,WAAW,CAAC;;sBAEV,EAAE,kBACE,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,sBAAsB,CAAC,GACzF,4EACH;oBACH,CAAC;4CACD,iBAAe;;8DAEf,6LAAC;oDAAK,WAAU;8DAAyB,QAAQ,KAAK;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DACZ,gCACC,6LAAC,uNAAA,CAAA,cAAW;wDAAC,MAAM;wDAAI,eAAY;;;;;6EAEnC,6LAAC,yNAAA,CAAA,eAAY;wDAAC,MAAM;wDAAI,eAAY;;;;;;;;;;;;;;;;;wCAMzC,iCACC,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAA;gDACjB,MAAM,eAAe,gBAAgB,cAAc,KAAK,EAAE;gDAE1D,iDAAiD;gDACjD,IAAI,CAAC,4BAA4B,cAAc,KAAK,EAAE,GAAG;oDACvD,OAAO,MAAM,wCAAwC;gDACvD;gDAEA,yCAAyC;gDACzC,MAAM,UAAU,GAAG,aAAa,CAAC,EAAE,KAAK,EAAE,EAAE;gDAC5C,MAAM,0BAA0B,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc,KAAK,EAAE;gDACzE,MAAM,sBAAsB,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,KAAK,EAAE;gDAExE,6BAA6B;gDAC7B,MAAM,gBAAgB,CAAC;;0BAErB,EAAE,eACE,CAAC,gCAAgC,EAAE,aAAa,2BAA2B,EAAE,aAAa;yCAC/E,EAAE,aAAa;6DACK,CAAC,GAChC,4EACH;wBACH,CAAC;gDAED,sDAAsD;gDACtD,IAAI,yBAAyB;oDAC3B,qBACE,6LAAC,2MAAA,CAAA,qBAAkB;wDAEjB,WAAW;wDACX,gBAAc,eAAe,SAAS;wDACtC,OAAO,oBAAoB,KAAK;wDAChC,SAAS,oBAAoB,OAAO;wDACpC,MAAM,oBAAoB,IAAI;wDAC9B,UAAS;;4DAER,KAAK,IAAI,kBACR,6LAAC;gEAAI,WAAW,CAAC;kCACf,EAAE,eACE,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,UAAU,CAAC,GAC7E,CAAC,yDAAyD,EAAE,aAAa,mCAAmC,EAAE,aAAa,yCAAyC,CAAC,CACxK;gCACH,CAAC;0EACC,cAAA,6LAAC,KAAK,IAAI;oEACR,MAAM;oEACN,eAAY;;;;;;;;;;;0EAIlB,6LAAC;gEAAK,WAAW,CAAC,8BAA8B,EAAE,eAAe,oBAAoB,IAAI;0EAAG,KAAK,KAAK;;;;;;;uDArBjG,KAAK,EAAE;;;;;gDAwBlB;gDAEA,oDAAoD;gDACpD,qBACE,6LAAC;oDAEC,SAAS,IAAM,yBAAyB,cAAc,KAAK,EAAE;oDAC7D,WAAW;oDACX,gBAAc,eAAe,SAAS;;wDAErC,KAAK,IAAI,kBACR,6LAAC;4DAAI,WAAW,CAAC;gCACf,EAAE,eACE,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,UAAU,CAAC,GAC7E,CAAC,yDAAyD,EAAE,aAAa,mCAAmC,EAAE,aAAa,yCAAyC,CAAC,CACxK;8BACH,CAAC;sEACC,cAAA,6LAAC,KAAK,IAAI;gEACR,MAAM;gEACN,eAAY;;;;;;;;;;;sEAIlB,6LAAC;4DAAK,WAAW,CAAC,8BAA8B,EAAE,eAAe,oBAAoB,IAAI;sEAAG,KAAK,KAAK;;;;;;;mDAlBjG,KAAK,EAAE;;;;;4CAqBlB;;;;;;;mCAxGI,QAAQ,EAAE;;;;;4BA6GxB,OAAO;gCACL,oDAAoD;gCACpD,MAAM,WAAW,gBAAgB,cAAc,QAAQ,EAAE;gCAEzD,iDAAiD;gCACjD,IAAI,CAAC,wBAAwB,cAAc,QAAQ,EAAE,GAAG;oCACtD,OAAO,MAAM,wCAAwC;gCACvD;gCAEA,4CAA4C;gCAC5C,MAAM,aAAa,GAAG,aAAa,CAAC,EAAE,QAAQ,EAAE,EAAE;gCAClD,MAAM,6BAA6B,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc,QAAQ,EAAE;gCAC/E,MAAM,sBAAsB,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,QAAQ,EAAE;gCAE3E,6CAA6C;gCAC7C,MAAM,kBAAkB,CAAC;;gBAEvB,EAAE,WACE,CAAC,gCAAgC,EAAE,aAAa,2BAA2B,EAAE,aAAa;+BAC/E,EAAE,aAAa;mDACK,CAAC,GAChC,4EACH;cACH,CAAC;gCAED,sDAAsD;gCACtD,IAAI,4BAA4B;oCAC9B,qBACE,6LAAC,2MAAA,CAAA,qBAAkB;wCAEjB,WAAW;wCACX,gBAAc,WAAW,SAAS;wCAClC,OAAO,oBAAoB,KAAK;wCAChC,SAAS,oBAAoB,OAAO;wCACpC,MAAM,oBAAoB,IAAI;wCAC9B,UAAS;;4CAER,QAAQ,IAAI,kBACX,6LAAC;gDAAI,WAAW,CAAC;sBACjB,EAAE,WACI,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,UAAU,CAAC,GAC7E,CAAC,yDAAyD,EAAE,aAAa,mCAAmC,EAAE,aAAa,yCAAyC,CAAC,CACxK;oBACL,CAAC;0DACG,cAAA,6LAAC,QAAQ,IAAI;oDACX,MAAM;oDACN,eAAY;;;;;;;;;;;0DAIlB,6LAAC;gDAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW,oBAAoB,IAAI;0DAAG,QAAQ,KAAK;;;;;;;uCArBxF,QAAQ,EAAE;;;;;gCAwBrB;gCAEA,oDAAoD;gCACpD,qBACE,6LAAC;oCAEC,SAAS,IAAM,yBAAyB,cAAc,QAAQ,EAAE;oCAChE,WAAW;oCACX,gBAAc,WAAW,SAAS;;wCAEjC,QAAQ,IAAI,kBACX,6LAAC;4CAAI,WAAW,CAAC;sBACf,EAAE,WACE,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,UAAU,CAAC,GAC7E,CAAC,yDAAyD,EAAE,aAAa,mCAAmC,EAAE,aAAa,yCAAyC,CAAC,CACxK;oBACH,CAAC;sDACC,cAAA,6LAAC,QAAQ,IAAI;gDACX,MAAM;gDACN,eAAY;;;;;;;;;;;sDAIlB,6LAAC;4CAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW,oBAAoB,IAAI;sDAAG,QAAQ,KAAK;;;;;;;mCAlBxF,QAAQ,EAAE;;;;;4BAqBrB;wBACF;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAW,CAAC;;mCAEa,EAAE,aAAa,2BAA2B,EAAE,aAAa;4DAChC,EAAE,aAAa,4BAA4B,EAAE,aAAa;;UAE5G,CAAC;oBACD,cAAW;;sCAEX,6LAAC;4BAAI,WAAW,CAAC;;sBAEL,EAAE,aAAa,mBAAmB,EAAE,aAAa;wBAC/C,EAAE,aAAa,uBAAuB,EAAE,aAAa;;;UAGnE,CAAC;sCACC,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,eAAY;;;;;;;;;;;sCAErC,6LAAC;4BAAK,WAAU;sCAA+C;;;;;;;;;;;;;;;;;;;;;;;AAKzE;GAvVM;;QAQgC,iIAAA,CAAA,iBAAc;QACjC,qIAAA,CAAA,cAAW;;;MATxB;uCAyVS"}}, {"offset": {"line": 4398, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4404, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/dashboard/Sidebar/ClientSidebar.js"], "sourcesContent": ["// src/components/dashboard/Sidebar/ClientSidebar.js\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport { ChevronLeft, Users, Calendar, User, Clock, FileText } from 'lucide-react';\r\nimport CustomScrollArea from '@/components/ui/CustomScrollArea';\r\n\r\n// Client-specific submenu configuration\r\nconst clientModuleSubmenus = {\r\n  people: [\r\n    { id: 'persons', title: '<PERSON><PERSON><PERSON><PERSON>', icon: Users, description: 'Gerenciar pessoas relacionadas' },\r\n  ],\r\n  scheduler: [\r\n    { id: 'calendar', title: 'Calendário', icon: Calendar, description: 'Visualizar agenda' },\r\n    { id: 'appointments', title: 'Meus Agendamentos', icon: Clock, description: 'Visualizar meus agendamentos' },\r\n  ],\r\n  profile: [\r\n    { id: 'profile', title: 'Meu Perfil', icon: User, description: 'Gerenciar meu perfil e dados pessoais' },\r\n  ]\r\n};\r\n\r\nconst ModuleTitle = ({ moduleId, title, icon: Icon }) => {\r\n  return (\r\n    <div className=\"mb-6\">\r\n      {/* Card com gradiente e borda temática */}\r\n      <div className={`\r\n        relative overflow-hidden rounded-xl p-4 mb-4\r\n        bg-gradient-to-r from-white dark:from-gray-800 to-module-${moduleId}-bg/30 dark:to-module-${moduleId}-bg-dark/30\r\n        border-l-4 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark\r\n        shadow-sm dark:shadow-md dark:shadow-black/20\r\n      `}>\r\n        <div className=\"flex items-center\">\r\n          {/* Ícone grande do módulo */}\r\n          <div className={`\r\n            w-12 h-12 rounded-lg mr-3 flex items-center justify-center\r\n            bg-module-${moduleId}-bg/70 dark:bg-module-${moduleId}-bg-dark/70\r\n            text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark\r\n          `}>\r\n            <Icon size={26} />\r\n          </div>\r\n\r\n          {/* Título e subtítulo */}\r\n          <div>\r\n            <h2 className=\"text-xl font-bold text-gray-800 dark:text-white\">{title}</h2>\r\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">Área do Cliente</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ClientSidebar = ({\r\n  activeModule,\r\n  activeModuleTitle,\r\n  isSubmenuActive,\r\n  handleModuleSubmenuClick,\r\n  handleBackToModules,\r\n  isSidebarOpen\r\n}) => {\r\n  // Encontrar o objeto do módulo ativo para acessar seu ícone\r\n  const ModuleIcon = activeModule === 'people' ? Users :\r\n                     activeModule === 'scheduler' ? Calendar :\r\n                     activeModule === 'profile' ? User : User;\r\n\r\n  return (\r\n    <aside\r\n      className={`w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${\r\n        isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'\r\n      }`}\r\n      aria-label=\"Navegação lateral\"\r\n    >\r\n      {/* Conteúdo principal da sidebar */}\r\n      <CustomScrollArea className=\"flex-1 p-5\" moduleColor={activeModule}>\r\n        {/* Título do módulo estilizado */}\r\n        {activeModule && (\r\n          <ModuleTitle\r\n            moduleId={activeModule}\r\n            title={activeModuleTitle}\r\n            icon={ModuleIcon}\r\n          />\r\n        )}\r\n\r\n        <nav\r\n          className=\"space-y-2\"\r\n          aria-labelledby=\"sidebar-heading\"\r\n        >\r\n          {activeModule && clientModuleSubmenus[activeModule]?.map((submenu) => {\r\n            const isActive = isSubmenuActive(activeModule, submenu.id);\r\n\r\n            return (\r\n              <button\r\n                key={submenu.id}\r\n                onClick={() => handleModuleSubmenuClick(activeModule, submenu.id)}\r\n                className={`\r\n                  group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\r\n                  ${isActive\r\n                    ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark\r\n                       bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90\r\n                       shadow-md dark:shadow-black/20`\r\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                  }\r\n                `}\r\n                aria-current={isActive ? 'page' : undefined}\r\n              >\r\n                {submenu.icon && (\r\n                  <div className={`\r\n                    ${isActive\r\n                      ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`\r\n                      : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`\r\n                    }\r\n                  `}>\r\n                    <submenu.icon\r\n                      size={20}\r\n                      aria-hidden=\"true\"\r\n                    />\r\n                  </div>\r\n                )}\r\n                <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>\r\n              </button>\r\n            );\r\n          })}\r\n        </nav>\r\n      </CustomScrollArea>\r\n\r\n      {/* Botão de voltar fixo na parte inferior */}\r\n      <div className=\"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\">\r\n        <button\r\n          onClick={handleBackToModules}\r\n          className={`\r\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\r\n            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark\r\n            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10\r\n            transition-all duration-300\r\n          `}\r\n          aria-label=\"Voltar para o dashboard principal\"\r\n        >\r\n          <div className={`\r\n            flex items-center justify-center w-8 h-8 rounded-full\r\n            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70\r\n            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark\r\n            shadow-sm dark:shadow-md dark:shadow-black/20\r\n            group-hover:scale-110 transition-transform duration-200\r\n          `}>\r\n            <ChevronLeft size={20} aria-hidden=\"true\" />\r\n          </div>\r\n          <span className=\"font-medium text-gray-800 dark:text-gray-200\">Voltar a Tela Inicial</span>\r\n        </button>\r\n      </div>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default ClientSidebar;\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AAGpD;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;AAMA,wCAAwC;AACxC,MAAM,uBAAuB;IAC3B,QAAQ;QACN;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAAiC;KAC/F;IACD,WAAW;QACT;YAAE,IAAI;YAAY,OAAO;YAAc,MAAM,6MAAA,CAAA,WAAQ;YAAE,aAAa;QAAoB;QACxF;YAAE,IAAI;YAAgB,OAAO;YAAqB,MAAM,uMAAA,CAAA,QAAK;YAAE,aAAa;QAA+B;KAC5G;IACD,SAAS;QACP;YAAE,IAAI;YAAW,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;YAAE,aAAa;QAAwC;KACxG;AACH;AAEA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE;IAClD,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAW,CAAC;;iEAE0C,EAAE,SAAS,sBAAsB,EAAE,SAAS;iCAC5E,EAAE,SAAS,2BAA2B,EAAE,SAAS;;MAE5E,CAAC;sBACC,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC;;sBAEL,EAAE,SAAS,sBAAsB,EAAE,SAAS;wBAC1C,EAAE,SAAS,uBAAuB,EAAE,SAAS;UAC3D,CAAC;kCACC,cAAA,6LAAC;4BAAK,MAAM;;;;;;;;;;;kCAId,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE;KA7BM;AA+BN,MAAM,gBAAgB,CAAC,EACrB,YAAY,EACZ,iBAAiB,EACjB,eAAe,EACf,wBAAwB,EACxB,mBAAmB,EACnB,aAAa,EACd;IACC,4DAA4D;IAC5D,MAAM,aAAa,iBAAiB,WAAW,uMAAA,CAAA,QAAK,GACjC,iBAAiB,cAAc,6MAAA,CAAA,WAAQ,GACvC,iBAAiB,YAAY,qMAAA,CAAA,OAAI,GAAG,qMAAA,CAAA,OAAI;IAE3D,qBACE,6LAAC;QACC,WAAW,CAAC,6HAA6H,EACvI,gBAAgB,kBAAkB,sCAClC;QACF,cAAW;;0BAGX,6LAAC,8IAAA,CAAA,UAAgB;gBAAC,WAAU;gBAAa,aAAa;;oBAEnD,8BACC,6LAAC;wBACC,UAAU;wBACV,OAAO;wBACP,MAAM;;;;;;kCAIV,6LAAC;wBACC,WAAU;wBACV,mBAAgB;kCAEf,gBAAgB,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC;4BACxD,MAAM,WAAW,gBAAgB,cAAc,QAAQ,EAAE;4BAEzD,qBACE,6LAAC;gCAEC,SAAS,IAAM,yBAAyB,cAAc,QAAQ,EAAE;gCAChE,WAAW,CAAC;;kBAEV,EAAE,WACE,CAAC,gCAAgC,EAAE,aAAa,2BAA2B,EAAE,aAAa;iCAC/E,EAAE,aAAa;qDACK,CAAC,GAChC,4EACH;gBACH,CAAC;gCACD,gBAAc,WAAW,SAAS;;oCAEjC,QAAQ,IAAI,kBACX,6LAAC;wCAAI,WAAW,CAAC;oBACf,EAAE,WACE,CAAC,YAAY,EAAE,aAAa,uBAAuB,EAAE,aAAa,UAAU,CAAC,GAC7E,CAAC,yDAAyD,EAAE,aAAa,mCAAmC,EAAE,aAAa,yCAAyC,CAAC,CACxK;kBACH,CAAC;kDACC,cAAA,6LAAC,QAAQ,IAAI;4CACX,MAAM;4CACN,eAAY;;;;;;;;;;;kDAIlB,6LAAC;wCAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW,oBAAoB,IAAI;kDAAG,QAAQ,KAAK;;;;;;;+BA1BxF,QAAQ,EAAE;;;;;wBA6BrB;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAW,CAAC;;mCAEa,EAAE,aAAa,2BAA2B,EAAE,aAAa;4DAChC,EAAE,aAAa,4BAA4B,EAAE,aAAa;;UAE5G,CAAC;oBACD,cAAW;;sCAEX,6LAAC;4BAAI,WAAW,CAAC;;sBAEL,EAAE,aAAa,mBAAmB,EAAE,aAAa;wBAC/C,EAAE,aAAa,uBAAuB,EAAE,aAAa;;;UAGnE,CAAC;sCACC,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,eAAY;;;;;;;;;;;sCAErC,6LAAC;4BAAK,WAAU;sCAA+C;;;;;;;;;;;;;;;;;;;;;;;AAKzE;MAnGM;uCAqGS"}}, {"offset": {"line": 4672, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4678, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialHighlight.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\n\r\n/**\r\n * Componente que cria um destaque visual em torno do elemento alvo para o tutorial\r\n *\r\n * @param {Object} props - Propriedades do componente\r\n * @param {string} props.selector - Seletor CSS do elemento a ser destacado\r\n * @param {string} props.shape - Forma do destaque ('circle', 'rectangle', 'auto')\r\n * @param {number} props.padding - Espaço adicional ao redor do elemento em pixels\r\n * @param {boolean} props.pulsate - Se o destaque deve ter animação pulsante\r\n */\r\nconst TutorialHighlight = ({\r\n  selector,\r\n  shape = 'auto',\r\n  padding = 10,\r\n  pulsate = true,\r\n  children\r\n}) => {\r\n  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const highlightRef = useRef(null);\r\n  const targetRef = useRef(null);\r\n\r\n  // Calcula a posição do elemento alvo\r\n  useEffect(() => {\r\n    if (!selector) return;\r\n\r\n    const calculatePosition = () => {\r\n      // Tenta encontrar todos os elementos que correspondem ao seletor\r\n      const elements = document.querySelectorAll(selector);\r\n\r\n      if (!elements || elements.length === 0) {\r\n        console.warn(`TutorialHighlight: Elemento com seletor \"${selector}\" não encontrado.`);\r\n        setIsVisible(false);\r\n        return;\r\n      }\r\n\r\n      console.log(`TutorialHighlight: Encontrados ${elements.length} elementos com seletor \"${selector}\"`);\r\n\r\n      // Encontra o primeiro elemento visível\r\n      let targetElement = null;\r\n      for (let i = 0; i < elements.length; i++) {\r\n        const element = elements[i];\r\n        const rect = element.getBoundingClientRect();\r\n\r\n        // Verifica se o elemento está visível na tela\r\n        if (rect.width > 0 && rect.height > 0 &&\r\n            rect.top < window.innerHeight &&\r\n            rect.left < window.innerWidth &&\r\n            rect.bottom > 0 &&\r\n            rect.right > 0) {\r\n\r\n          // Verifica se o elemento não está oculto por CSS\r\n          const style = window.getComputedStyle(element);\r\n          if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {\r\n            targetElement = element;\r\n            console.log(`TutorialHighlight: Elemento visível encontrado na posição ${i+1}`, rect);\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Se não encontrou nenhum elemento visível, usa o primeiro\r\n      if (!targetElement && elements.length > 0) {\r\n        targetElement = elements[0];\r\n        console.log(`TutorialHighlight: Nenhum elemento visível encontrado, usando o primeiro elemento`);\r\n      }\r\n\r\n      if (!targetElement) {\r\n        console.warn(`TutorialHighlight: Nenhum elemento visível encontrado com seletor \"${selector}\".`);\r\n        setIsVisible(false);\r\n        return;\r\n      }\r\n\r\n      // Armazenamos referência ao elemento para uso futuro\r\n      targetRef.current = targetElement;\r\n\r\n      const rect = targetElement.getBoundingClientRect();\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\r\n\r\n      // Determinar forma automática com base nas dimensões\r\n      let finalShape = shape;\r\n      if (shape === 'auto') {\r\n        // Se o elemento for aproximadamente quadrado e pequeno, usar círculo\r\n        const isSquarish = Math.abs(rect.width - rect.height) < Math.min(rect.width, rect.height) * 0.2;\r\n        const isSmall = Math.max(rect.width, rect.height) < 100;\r\n        finalShape = isSquarish && isSmall ? 'circle' : 'rectangle';\r\n      }\r\n\r\n      // Adicionar padding à posição\r\n      let newPosition = {\r\n        top: rect.top + scrollTop - padding,\r\n        left: rect.left + scrollLeft - padding,\r\n        width: rect.width + padding * 2,\r\n        height: rect.height + padding * 2,\r\n        shape: finalShape\r\n      };\r\n\r\n      setPosition(newPosition);\r\n      setIsVisible(true);\r\n    };\r\n\r\n    // Calcular posição inicial\r\n    calculatePosition();\r\n\r\n    // Recalcular durante a rolagem da página\r\n    const handleScroll = () => {\r\n      requestAnimationFrame(calculatePosition);\r\n    };\r\n\r\n    // Recalcular em caso de redimensionamento\r\n    window.addEventListener('resize', calculatePosition);\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', calculatePosition);\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n  }, [selector, shape, padding]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  const borderRadius = position.shape === 'circle'\r\n    ? '50%'\r\n    : '8px';\r\n\r\n  const highlightStyles = {\r\n    position: 'absolute',\r\n    top: `${position.top}px`,\r\n    left: `${position.left}px`,\r\n    width: `${position.width}px`,\r\n    height: `${position.height}px`,\r\n    borderRadius,\r\n    boxShadow: '0 0 0 5000px rgba(0, 0, 0, 0.75)',\r\n    zIndex: 9998,\r\n    pointerEvents: 'none',\r\n    animation: pulsate ? 'tutorial-highlight-pulse 2s infinite' : 'none',\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Estilo para animação */}\r\n      <style jsx global>{`\r\n        @keyframes tutorial-highlight-pulse {\r\n          0% {\r\n            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 4px rgba(255, 153, 51, 0.6);\r\n          }\r\n          50% {\r\n            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 8px rgba(255, 153, 51, 0.8);\r\n          }\r\n          100% {\r\n            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 4px rgba(255, 153, 51, 0.6);\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      {/* Elemento de destaque */}\r\n      <div\r\n        ref={highlightRef}\r\n        className=\"tutorial-highlight\"\r\n        style={highlightStyles}\r\n      />\r\n\r\n      {/* Conteúdo filho (caixa de diálogo, etc.) */}\r\n      {children}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TutorialHighlight;"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AAIA;;;;;;;;CAQC,GACD,MAAM,oBAAoB,CAAC,EACzB,QAAQ,EACR,QAAQ,MAAM,EACd,UAAU,EAAE,EACZ,UAAU,IAAI,EACd,QAAQ,EACT;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,MAAM;QAAG,OAAO;QAAG,QAAQ;IAAE;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU;YAEf,MAAM;iEAAoB;oBACxB,iEAAiE;oBACjE,MAAM,WAAW,SAAS,gBAAgB,CAAC;oBAE3C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;wBACtC,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,SAAS,iBAAiB,CAAC;wBACpF,aAAa;wBACb;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;oBAEnG,uCAAuC;oBACvC,IAAI,gBAAgB;oBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;wBACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;wBAC3B,MAAM,OAAO,QAAQ,qBAAqB;wBAE1C,8CAA8C;wBAC9C,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAChC,KAAK,GAAG,GAAG,OAAO,WAAW,IAC7B,KAAK,IAAI,GAAG,OAAO,UAAU,IAC7B,KAAK,MAAM,GAAG,KACd,KAAK,KAAK,GAAG,GAAG;4BAElB,iDAAiD;4BACjD,MAAM,QAAQ,OAAO,gBAAgB,CAAC;4BACtC,IAAI,MAAM,OAAO,KAAK,UAAU,MAAM,UAAU,KAAK,YAAY,MAAM,OAAO,KAAK,KAAK;gCACtF,gBAAgB;gCAChB,QAAQ,GAAG,CAAC,CAAC,0DAA0D,EAAE,IAAE,GAAG,EAAE;gCAChF;4BACF;wBACF;oBACF;oBAEA,2DAA2D;oBAC3D,IAAI,CAAC,iBAAiB,SAAS,MAAM,GAAG,GAAG;wBACzC,gBAAgB,QAAQ,CAAC,EAAE;wBAC3B,QAAQ,GAAG,CAAC,CAAC,iFAAiF,CAAC;oBACjG;oBAEA,IAAI,CAAC,eAAe;wBAClB,QAAQ,IAAI,CAAC,CAAC,mEAAmE,EAAE,SAAS,EAAE,CAAC;wBAC/F,aAAa;wBACb;oBACF;oBAEA,qDAAqD;oBACrD,UAAU,OAAO,GAAG;oBAEpB,MAAM,OAAO,cAAc,qBAAqB;oBAChD,MAAM,YAAY,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,SAAS;oBAC1E,MAAM,aAAa,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,UAAU;oBAE5E,qDAAqD;oBACrD,IAAI,aAAa;oBACjB,IAAI,UAAU,QAAQ;wBACpB,qEAAqE;wBACrE,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,IAAI;wBAC5F,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,IAAI;wBACpD,aAAa,cAAc,UAAU,WAAW;oBAClD;oBAEA,8BAA8B;oBAC9B,IAAI,cAAc;wBAChB,KAAK,KAAK,GAAG,GAAG,YAAY;wBAC5B,MAAM,KAAK,IAAI,GAAG,aAAa;wBAC/B,OAAO,KAAK,KAAK,GAAG,UAAU;wBAC9B,QAAQ,KAAK,MAAM,GAAG,UAAU;wBAChC,OAAO;oBACT;oBAEA,YAAY;oBACZ,aAAa;gBACf;;YAEA,2BAA2B;YAC3B;YAEA,yCAAyC;YACzC,MAAM;4DAAe;oBACnB,sBAAsB;gBACxB;;YAEA,0CAA0C;YAC1C,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,UAAU;YAElC;+CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;sCAAG;QAAC;QAAU;QAAO;KAAQ;IAE7B,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,eAAe,SAAS,KAAK,KAAK,WACpC,QACA;IAEJ,MAAM,kBAAkB;QACtB,UAAU;QACV,KAAK,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC;QAC1B,OAAO,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC;QAC5B,QAAQ,GAAG,SAAS,MAAM,CAAC,EAAE,CAAC;QAC9B;QACA,WAAW;QACX,QAAQ;QACR,eAAe;QACf,WAAW,UAAU,yCAAyC;IAChE;IAEA,qBACE;;;;;;0BAiBE,6LAAC;gBACC,KAAK;gBAEL,OAAO;yDADG;;;;;;YAKX;;;AAGP;GA7JM;KAAA;uCA+JS"}}, {"offset": {"line": 4837, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4843, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialDialog.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef, useEffect, useState } from 'react';\r\nimport { ArrowLeft, ArrowRight, X } from 'lucide-react';\r\n\r\n/**\r\n * Caixa de diálogo para exibir instruções do tutorial\r\n *\r\n * @param {Object} props - Propriedades do componente\r\n * @param {string} props.title - <PERSON><PERSON><PERSON><PERSON> da etapa\r\n * @param {string|React.ReactNode} props.content - Conteúdo/descrição da etapa\r\n * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')\r\n * @param {string} props.targetSelector - Seletor do elemento alvo para posicionamento\r\n * @param {number} props.offsetX - Deslocamento horizontal da posição padrão\r\n * @param {number} props.offsetY - Deslocamento vertical da posição padrão\r\n * @param {number} props.currentStep - Número da etapa atual\r\n * @param {number} props.totalSteps - Número total de etapas\r\n * @param {function} props.onNext - Função chamada ao clicar em \"Próximo\"\r\n * @param {function} props.onPrev - Função chamada ao clicar em \"Anterior\"\r\n * @param {function} props.onClose - Função chamada ao clicar em \"Fechar\"\r\n * @param {boolean} props.isFirstStep - Se é a primeira etapa\r\n * @param {boolean} props.isLastStep - Se é a última etapa\r\n */\r\nconst TutorialDialog = ({\r\n  title,\r\n  content,\r\n  position = 'auto',\r\n  targetSelector,\r\n  offsetX = 20,\r\n  offsetY = 20,\r\n  currentStep,\r\n  totalSteps,\r\n  onNext,\r\n  onPrev,\r\n  onClose,\r\n  isFirstStep,\r\n  isLastStep\r\n}) => {\r\n  const dialogRef = useRef(null);\r\n  const [dialogPosition, setDialogPosition] = useState({ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' });\r\n  const [calculatedPosition, setCalculatedPosition] = useState(position);\r\n\r\n  // Calcula a melhor posição para o diálogo com base no elemento alvo\r\n  useEffect(() => {\r\n    if (!targetSelector || !dialogRef.current) return;\r\n\r\n    const calculatePosition = () => {\r\n      // Tenta encontrar todos os elementos que correspondem ao seletor\r\n      const elements = document.querySelectorAll(targetSelector);\r\n\r\n      if (!elements || elements.length === 0) {\r\n        console.warn(`TutorialDialog: Elemento com seletor \"${targetSelector}\" não encontrado para posicionar o diálogo.`);\r\n        return;\r\n      }\r\n\r\n      console.log(`TutorialDialog: Encontrados ${elements.length} elementos com seletor \"${targetSelector}\"`);\r\n\r\n      // Encontra o primeiro elemento visível\r\n      let targetElement = null;\r\n      for (let i = 0; i < elements.length; i++) {\r\n        const element = elements[i];\r\n        const rect = element.getBoundingClientRect();\r\n\r\n        // Verifica se o elemento está visível na tela\r\n        if (rect.width > 0 && rect.height > 0 &&\r\n            rect.top < window.innerHeight &&\r\n            rect.left < window.innerWidth &&\r\n            rect.bottom > 0 &&\r\n            rect.right > 0) {\r\n\r\n          // Verifica se o elemento não está oculto por CSS\r\n          const style = window.getComputedStyle(element);\r\n          if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {\r\n            targetElement = element;\r\n            console.log(`TutorialDialog: Elemento visível encontrado na posição ${i+1}`, rect);\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Se não encontrou nenhum elemento visível, usa o primeiro\r\n      if (!targetElement && elements.length > 0) {\r\n        targetElement = elements[0];\r\n        console.log(`TutorialDialog: Nenhum elemento visível encontrado, usando o primeiro elemento`);\r\n      }\r\n\r\n      if (!targetElement) {\r\n        console.warn(`TutorialDialog: Nenhum elemento visível encontrado com seletor \"${targetSelector}\" para posicionar o diálogo.`);\r\n        return;\r\n      }\r\n\r\n      const targetRect = targetElement.getBoundingClientRect();\r\n      const dialogRect = dialogRef.current.getBoundingClientRect();\r\n\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\r\n\r\n      const viewportWidth = window.innerWidth;\r\n      const viewportHeight = window.innerHeight;\r\n\r\n      // Auto-calcular a melhor posição se for 'auto'\r\n      let bestPosition = position;\r\n      if (position === 'auto') {\r\n        // Calcular espaço disponível em cada direção\r\n        const spaceAbove = targetRect.top;\r\n        const spaceBelow = viewportHeight - targetRect.bottom;\r\n        const spaceLeft = targetRect.left;\r\n        const spaceRight = viewportWidth - targetRect.right;\r\n\r\n        // Determinar a direção com mais espaço\r\n        const maxSpace = Math.max(spaceAbove, spaceBelow, spaceLeft, spaceRight);\r\n\r\n        if (maxSpace === spaceBelow) bestPosition = 'bottom';\r\n        else if (maxSpace === spaceAbove) bestPosition = 'top';\r\n        else if (maxSpace === spaceRight) bestPosition = 'right';\r\n        else bestPosition = 'left';\r\n      }\r\n\r\n      setCalculatedPosition(bestPosition);\r\n\r\n      let newPosition = {};\r\n\r\n      // Calcular posição com base na direção escolhida\r\n      switch (bestPosition) {\r\n        case 'top':\r\n          newPosition = {\r\n            top: targetRect.top + scrollTop - dialogRect.height - offsetY,\r\n            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),\r\n            transform: 'none'\r\n          };\r\n          break;\r\n        case 'right':\r\n          newPosition = {\r\n            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),\r\n            left: targetRect.right + scrollLeft + offsetX,\r\n            transform: 'none'\r\n          };\r\n          break;\r\n        case 'bottom':\r\n          newPosition = {\r\n            top: targetRect.bottom + scrollTop + offsetY,\r\n            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),\r\n            transform: 'none'\r\n          };\r\n          break;\r\n        case 'left':\r\n          newPosition = {\r\n            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),\r\n            left: targetRect.left + scrollLeft - dialogRect.width - offsetX,\r\n            transform: 'none'\r\n          };\r\n          break;\r\n        default:\r\n          // Posição centralizada como fallback\r\n          newPosition = {\r\n            top: '50%',\r\n            left: '50%',\r\n            transform: 'translate(-50%, -50%)'\r\n          };\r\n      }\r\n\r\n      // Ajuste para garantir que o diálogo fique dentro da viewport\r\n      if (newPosition.left < 20) newPosition.left = 20;\r\n      if (newPosition.top < 20) newPosition.top = 20;\r\n      if (newPosition.left + dialogRect.width > viewportWidth - 20) {\r\n        newPosition.left = viewportWidth - dialogRect.width - 20;\r\n      }\r\n      if (newPosition.top + dialogRect.height > viewportHeight - 20) {\r\n        newPosition.top = viewportHeight - dialogRect.height - 20;\r\n      }\r\n\r\n      setDialogPosition(newPosition);\r\n    };\r\n\r\n    // Pequeno atraso para garantir que o diálogo tenha sido renderizado com dimensões corretas\r\n    const timer = setTimeout(calculatePosition, 300);\r\n\r\n    // Recalcular em caso de redimensionamento da janela\r\n    window.addEventListener('resize', calculatePosition);\r\n\r\n    // Recalcular periodicamente para garantir que o diálogo esteja sempre posicionado corretamente\r\n    const intervalTimer = setInterval(() => {\r\n      calculatePosition();\r\n    }, 1000);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n      clearInterval(intervalTimer);\r\n      window.removeEventListener('resize', calculatePosition);\r\n    };\r\n  }, [targetSelector, position, offsetX, offsetY, dialogRef.current]);\r\n\r\n  // Classe para adicionar seta direcional\r\n  const getPositionClass = () => {\r\n    switch (calculatedPosition) {\r\n      case 'top': return 'tutorial-dialog-arrow-bottom';\r\n      case 'right': return 'tutorial-dialog-arrow-left';\r\n      case 'bottom': return 'tutorial-dialog-arrow-top';\r\n      case 'left': return 'tutorial-dialog-arrow-right';\r\n      default: return '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Estilos para setas direcionais */}\r\n      <style jsx global>{`\r\n        .tutorial-dialog-arrow-top:after {\r\n          content: '';\r\n          position: absolute;\r\n          top: -10px;\r\n          left: 50%;\r\n          transform: translateX(-50%);\r\n          border-width: 0 10px 10px 10px;\r\n          border-style: solid;\r\n          border-color: transparent transparent #ffffff transparent;\r\n          filter: drop-shadow(0 -2px 2px rgba(0,0,0,0.1));\r\n        }\r\n\r\n        .tutorial-dialog-arrow-right:after {\r\n          content: '';\r\n          position: absolute;\r\n          right: -10px;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n          border-width: 10px 0 10px 10px;\r\n          border-style: solid;\r\n          border-color: transparent transparent transparent #ffffff;\r\n          filter: drop-shadow(2px 0 2px rgba(0,0,0,0.1));\r\n        }\r\n\r\n        .tutorial-dialog-arrow-bottom:after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: -10px;\r\n          left: 50%;\r\n          transform: translateX(-50%);\r\n          border-width: 10px 10px 0 10px;\r\n          border-style: solid;\r\n          border-color: #ffffff transparent transparent transparent;\r\n          filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));\r\n        }\r\n\r\n        .tutorial-dialog-arrow-left:after {\r\n          content: '';\r\n          position: absolute;\r\n          left: -10px;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n          border-width: 10px 10px 10px 0;\r\n          border-style: solid;\r\n          border-color: transparent #ffffff transparent transparent;\r\n          filter: drop-shadow(-2px 0 2px rgba(0,0,0,0.1));\r\n        }\r\n\r\n        .dark .tutorial-dialog-arrow-top:after,\r\n        .dark .tutorial-dialog-arrow-right:after,\r\n        .dark .tutorial-dialog-arrow-bottom:after,\r\n        .dark .tutorial-dialog-arrow-left:after {\r\n          border-color: transparent;\r\n        }\r\n\r\n        .dark .tutorial-dialog-arrow-top:after {\r\n          border-bottom-color: #1f2937;\r\n        }\r\n\r\n        .dark .tutorial-dialog-arrow-right:after {\r\n          border-left-color: #1f2937;\r\n        }\r\n\r\n        .dark .tutorial-dialog-arrow-bottom:after {\r\n          border-top-color: #1f2937;\r\n        }\r\n\r\n        .dark .tutorial-dialog-arrow-left:after {\r\n          border-right-color: #1f2937;\r\n        }\r\n      `}</style>\r\n\r\n      {/* Diálogo do tutorial */}\r\n      <div\r\n        ref={dialogRef}\r\n        className={`fixed z-[9999] w-80 p-5 rounded-lg bg-white dark:bg-gray-800 shadow-xl dark:shadow-hard-dark ${getPositionClass()}`}\r\n        style={{\r\n          top: dialogPosition.top,\r\n          left: dialogPosition.left,\r\n          transform: dialogPosition.transform,\r\n        }}\r\n      >\r\n        {/* Cabeçalho */}\r\n        <div className=\"flex justify-between items-start mb-3\">\r\n          <h3 className=\"text-lg font-bold text-primary-600 dark:text-primary-400\">\r\n            {title}\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full transition-colors\"\r\n            aria-label=\"Fechar tutorial\"\r\n          >\r\n            <X size={18} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Conteúdo */}\r\n        <div className=\"mb-4 text-gray-700 dark:text-gray-300\">\r\n          {content}\r\n        </div>\r\n\r\n        {/* Progresso */}\r\n        <div className=\"w-full bg-gray-200 dark:bg-gray-700 h-1 rounded mb-3\">\r\n          <div\r\n            className=\"bg-primary-500 dark:bg-primary-600 h-1 rounded\"\r\n            style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}\r\n          ></div>\r\n        </div>\r\n\r\n        {/* Rodapé com navegação */}\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            Passo {currentStep + 1} de {totalSteps}\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            {!isFirstStep && (\r\n              <button\r\n                onClick={onPrev}\r\n                className=\"p-1.5 rounded-md border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors\"\r\n                aria-label=\"Anterior\"\r\n              >\r\n                <ArrowLeft size={16} />\r\n              </button>\r\n            )}\r\n            <button\r\n              onClick={onNext}\r\n              className=\"px-3 py-1.5 rounded-md bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white flex items-center gap-1.5 transition-colors\"\r\n              aria-label={isLastStep ? \"Concluir\" : \"Próximo\"}\r\n            >\r\n              {isLastStep ? \"Concluir\" : \"Próximo\"}\r\n              {!isLastStep && <ArrowRight size={16} />}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TutorialDialog;"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;;AAKA;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,iBAAiB,CAAC,EACtB,KAAK,EACL,OAAO,EACP,WAAW,MAAM,EACjB,cAAc,EACd,UAAU,EAAE,EACZ,UAAU,EAAE,EACZ,WAAW,EACX,UAAU,EACV,MAAM,EACN,MAAM,EACN,OAAO,EACP,WAAW,EACX,UAAU,EACX;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAO,MAAM;QAAO,WAAW;IAAwB;IACnH,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,kBAAkB,CAAC,UAAU,OAAO,EAAE;YAE3C,MAAM;8DAAoB;oBACxB,iEAAiE;oBACjE,MAAM,WAAW,SAAS,gBAAgB,CAAC;oBAE3C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;wBACtC,QAAQ,IAAI,CAAC,CAAC,sCAAsC,EAAE,eAAe,2CAA2C,CAAC;wBACjH;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS,MAAM,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;oBAEtG,uCAAuC;oBACvC,IAAI,gBAAgB;oBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;wBACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;wBAC3B,MAAM,OAAO,QAAQ,qBAAqB;wBAE1C,8CAA8C;wBAC9C,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAChC,KAAK,GAAG,GAAG,OAAO,WAAW,IAC7B,KAAK,IAAI,GAAG,OAAO,UAAU,IAC7B,KAAK,MAAM,GAAG,KACd,KAAK,KAAK,GAAG,GAAG;4BAElB,iDAAiD;4BACjD,MAAM,QAAQ,OAAO,gBAAgB,CAAC;4BACtC,IAAI,MAAM,OAAO,KAAK,UAAU,MAAM,UAAU,KAAK,YAAY,MAAM,OAAO,KAAK,KAAK;gCACtF,gBAAgB;gCAChB,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,IAAE,GAAG,EAAE;gCAC7E;4BACF;wBACF;oBACF;oBAEA,2DAA2D;oBAC3D,IAAI,CAAC,iBAAiB,SAAS,MAAM,GAAG,GAAG;wBACzC,gBAAgB,QAAQ,CAAC,EAAE;wBAC3B,QAAQ,GAAG,CAAC,CAAC,8EAA8E,CAAC;oBAC9F;oBAEA,IAAI,CAAC,eAAe;wBAClB,QAAQ,IAAI,CAAC,CAAC,gEAAgE,EAAE,eAAe,4BAA4B,CAAC;wBAC5H;oBACF;oBAEA,MAAM,aAAa,cAAc,qBAAqB;oBACtD,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;oBAE1D,MAAM,YAAY,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,SAAS;oBAC1E,MAAM,aAAa,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,UAAU;oBAE5E,MAAM,gBAAgB,OAAO,UAAU;oBACvC,MAAM,iBAAiB,OAAO,WAAW;oBAEzC,+CAA+C;oBAC/C,IAAI,eAAe;oBACnB,IAAI,aAAa,QAAQ;wBACvB,6CAA6C;wBAC7C,MAAM,aAAa,WAAW,GAAG;wBACjC,MAAM,aAAa,iBAAiB,WAAW,MAAM;wBACrD,MAAM,YAAY,WAAW,IAAI;wBACjC,MAAM,aAAa,gBAAgB,WAAW,KAAK;wBAEnD,uCAAuC;wBACvC,MAAM,WAAW,KAAK,GAAG,CAAC,YAAY,YAAY,WAAW;wBAE7D,IAAI,aAAa,YAAY,eAAe;6BACvC,IAAI,aAAa,YAAY,eAAe;6BAC5C,IAAI,aAAa,YAAY,eAAe;6BAC5C,eAAe;oBACtB;oBAEA,sBAAsB;oBAEtB,IAAI,cAAc,CAAC;oBAEnB,iDAAiD;oBACjD,OAAQ;wBACN,KAAK;4BACH,cAAc;gCACZ,KAAK,WAAW,GAAG,GAAG,YAAY,WAAW,MAAM,GAAG;gCACtD,MAAM,WAAW,IAAI,GAAG,aAAc,WAAW,KAAK,GAAG,IAAM,WAAW,KAAK,GAAG;gCAClF,WAAW;4BACb;4BACA;wBACF,KAAK;4BACH,cAAc;gCACZ,KAAK,WAAW,GAAG,GAAG,YAAa,WAAW,MAAM,GAAG,IAAM,WAAW,MAAM,GAAG;gCACjF,MAAM,WAAW,KAAK,GAAG,aAAa;gCACtC,WAAW;4BACb;4BACA;wBACF,KAAK;4BACH,cAAc;gCACZ,KAAK,WAAW,MAAM,GAAG,YAAY;gCACrC,MAAM,WAAW,IAAI,GAAG,aAAc,WAAW,KAAK,GAAG,IAAM,WAAW,KAAK,GAAG;gCAClF,WAAW;4BACb;4BACA;wBACF,KAAK;4BACH,cAAc;gCACZ,KAAK,WAAW,GAAG,GAAG,YAAa,WAAW,MAAM,GAAG,IAAM,WAAW,MAAM,GAAG;gCACjF,MAAM,WAAW,IAAI,GAAG,aAAa,WAAW,KAAK,GAAG;gCACxD,WAAW;4BACb;4BACA;wBACF;4BACE,qCAAqC;4BACrC,cAAc;gCACZ,KAAK;gCACL,MAAM;gCACN,WAAW;4BACb;oBACJ;oBAEA,8DAA8D;oBAC9D,IAAI,YAAY,IAAI,GAAG,IAAI,YAAY,IAAI,GAAG;oBAC9C,IAAI,YAAY,GAAG,GAAG,IAAI,YAAY,GAAG,GAAG;oBAC5C,IAAI,YAAY,IAAI,GAAG,WAAW,KAAK,GAAG,gBAAgB,IAAI;wBAC5D,YAAY,IAAI,GAAG,gBAAgB,WAAW,KAAK,GAAG;oBACxD;oBACA,IAAI,YAAY,GAAG,GAAG,WAAW,MAAM,GAAG,iBAAiB,IAAI;wBAC7D,YAAY,GAAG,GAAG,iBAAiB,WAAW,MAAM,GAAG;oBACzD;oBAEA,kBAAkB;gBACpB;;YAEA,2FAA2F;YAC3F,MAAM,QAAQ,WAAW,mBAAmB;YAE5C,oDAAoD;YACpD,OAAO,gBAAgB,CAAC,UAAU;YAElC,+FAA+F;YAC/F,MAAM,gBAAgB;0DAAY;oBAChC;gBACF;yDAAG;YAEH;4CAAO;oBACL,aAAa;oBACb,cAAc;oBACd,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;mCAAG;QAAC;QAAgB;QAAU;QAAS;QAAS,UAAU,OAAO;KAAC;IAElE,wCAAwC;IACxC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE;;;;;;0BA4EE,6LAAC;gBACC,KAAK;gBAEL,OAAO;oBACL,KAAK,eAAe,GAAG;oBACvB,MAAM,eAAe,IAAI;oBACzB,WAAW,eAAe,SAAS;gBACrC;0DALW,CAAC,6FAA6F,EAAE,oBAAoB;;kCAQ/H,6LAAC;kEAAc;;0CACb,6LAAC;0EAAa;0CACX;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCAET,cAAW;0EADD;0CAGV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,6LAAC;kEAAc;kCACZ;;;;;;kCAIH,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BAEC,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,cAAc,CAAC,IAAI,aAAc,IAAI,CAAC,CAAC;4BAAC;sEADnD;;;;;;;;;;;kCAMd,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;oCAA2C;oCACjD,cAAc;oCAAE;oCAAK;;;;;;;0CAE9B,6LAAC;0EAAc;;oCACZ,CAAC,6BACA,6LAAC;wCACC,SAAS;wCAET,cAAW;kFADD;kDAGV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;;;;;;kDAGrB,6LAAC;wCACC,SAAS;wCAET,cAAY,aAAa,aAAa;kFAD5B;;4CAGT,aAAa,aAAa;4CAC1B,CAAC,4BAAc,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;GAjUM;KAAA;uCAmUS"}}, {"offset": {"line": 5191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5197, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialStep.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from 'react';\r\nimport TutorialHighlight from './TutorialHighlight';\r\nimport TutorialDialog from './TutorialDialog';\r\nimport { useTutorial } from '@/contexts/TutorialContext';\r\n\r\n/**\r\n * Componente que combina o destaque com o diálogo para uma etapa do tutorial\r\n * Com scroll automático ao navegar entre etapas\r\n */\r\nconst TutorialStep = () => {\r\n  const {\r\n    isActive,\r\n    currentStep,\r\n    currentStepIndex,\r\n    steps,\r\n    isFirstStep,\r\n    isLastStep,\r\n    nextStep,\r\n    prevStep,\r\n    endTutorial\r\n  } = useTutorial();\r\n\r\n  // Efeito para rolar para o elemento destacado quando a etapa mudar\r\n  useEffect(() => {\r\n    if (isActive && currentStep && currentStep.selector) {\r\n      const scrollToElement = () => {\r\n        // Tenta encontrar todos os elementos que correspondem ao seletor\r\n        const elements = document.querySelectorAll(currentStep.selector);\r\n\r\n        if (!elements || elements.length === 0) {\r\n          console.warn(`TutorialStep: Elemento com seletor \"${currentStep.selector}\" não encontrado.`);\r\n          return;\r\n        }\r\n\r\n        console.log(`TutorialStep: Encontrados ${elements.length} elementos com seletor \"${currentStep.selector}\"`);\r\n\r\n        // Encontra o primeiro elemento visível\r\n        let targetElement = null;\r\n        for (let i = 0; i < elements.length; i++) {\r\n          const element = elements[i];\r\n          const rect = element.getBoundingClientRect();\r\n\r\n          // Verifica se o elemento está visível na tela\r\n          if (rect.width > 0 && rect.height > 0 &&\r\n              rect.top < window.innerHeight &&\r\n              rect.left < window.innerWidth &&\r\n              rect.bottom > 0 &&\r\n              rect.right > 0) {\r\n\r\n            // Verifica se o elemento não está oculto por CSS\r\n            const style = window.getComputedStyle(element);\r\n            if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {\r\n              targetElement = element;\r\n              console.log(`TutorialStep: Elemento visível encontrado na posição ${i+1}`, rect);\r\n              break;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Se não encontrou nenhum elemento visível, usa o primeiro\r\n        if (!targetElement && elements.length > 0) {\r\n          targetElement = elements[0];\r\n          console.log(`TutorialStep: Nenhum elemento visível encontrado, usando o primeiro elemento`);\r\n        }\r\n\r\n        if (targetElement) {\r\n          // Pequeno atraso para garantir que a UI esteja pronta\r\n          setTimeout(() => {\r\n            targetElement.scrollIntoView({\r\n              behavior: 'smooth',\r\n              block: 'center',\r\n              inline: 'center'\r\n            });\r\n            console.log(`TutorialStep: Rolando para o elemento no passo ${currentStepIndex + 1}`);\r\n          }, 100);\r\n        }\r\n      };\r\n\r\n      // Pequeno atraso para garantir que a página esteja completamente carregada\r\n      setTimeout(scrollToElement, 300);\r\n    }\r\n  }, [isActive, currentStep, currentStepIndex]);\r\n\r\n  if (!isActive || !currentStep) return null;\r\n\r\n  const {\r\n    title,\r\n    content,\r\n    selector,\r\n    position = 'auto',\r\n    shape = 'auto',\r\n    highlightPadding = 10,\r\n    pulsate = true,\r\n    dialogOffsetX = 20,\r\n    dialogOffsetY = 20\r\n  } = currentStep;\r\n\r\n  return (\r\n    <TutorialHighlight\r\n      selector={selector}\r\n      shape={shape}\r\n      padding={highlightPadding}\r\n      pulsate={pulsate}\r\n    >\r\n      <TutorialDialog\r\n        title={title}\r\n        content={content}\r\n        position={position}\r\n        targetSelector={selector}\r\n        offsetX={dialogOffsetX}\r\n        offsetY={dialogOffsetY}\r\n        currentStep={currentStepIndex}\r\n        totalSteps={steps.length}\r\n        onNext={nextStep}\r\n        onPrev={prevStep}\r\n        onClose={endTutorial}\r\n        isFirstStep={isFirstStep}\r\n        isLastStep={isLastStep}\r\n      />\r\n    </TutorialHighlight>\r\n  );\r\n};\r\n\r\nexport default TutorialStep;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA;;;CAGC,GACD,MAAM,eAAe;;IACnB,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,KAAK,EACL,WAAW,EACX,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,WAAW,EACZ,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAEd,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,eAAe,YAAY,QAAQ,EAAE;gBACnD,MAAM;8DAAkB;wBACtB,iEAAiE;wBACjE,MAAM,WAAW,SAAS,gBAAgB,CAAC,YAAY,QAAQ;wBAE/D,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;4BACtC,QAAQ,IAAI,CAAC,CAAC,oCAAoC,EAAE,YAAY,QAAQ,CAAC,iBAAiB,CAAC;4BAC3F;wBACF;wBAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,SAAS,MAAM,CAAC,wBAAwB,EAAE,YAAY,QAAQ,CAAC,CAAC,CAAC;wBAE1G,uCAAuC;wBACvC,IAAI,gBAAgB;wBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;4BACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;4BAC3B,MAAM,OAAO,QAAQ,qBAAqB;4BAE1C,8CAA8C;4BAC9C,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAChC,KAAK,GAAG,GAAG,OAAO,WAAW,IAC7B,KAAK,IAAI,GAAG,OAAO,UAAU,IAC7B,KAAK,MAAM,GAAG,KACd,KAAK,KAAK,GAAG,GAAG;gCAElB,iDAAiD;gCACjD,MAAM,QAAQ,OAAO,gBAAgB,CAAC;gCACtC,IAAI,MAAM,OAAO,KAAK,UAAU,MAAM,UAAU,KAAK,YAAY,MAAM,OAAO,KAAK,KAAK;oCACtF,gBAAgB;oCAChB,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,IAAE,GAAG,EAAE;oCAC3E;gCACF;4BACF;wBACF;wBAEA,2DAA2D;wBAC3D,IAAI,CAAC,iBAAiB,SAAS,MAAM,GAAG,GAAG;4BACzC,gBAAgB,QAAQ,CAAC,EAAE;4BAC3B,QAAQ,GAAG,CAAC,CAAC,4EAA4E,CAAC;wBAC5F;wBAEA,IAAI,eAAe;4BACjB,sDAAsD;4BACtD;0EAAW;oCACT,cAAc,cAAc,CAAC;wCAC3B,UAAU;wCACV,OAAO;wCACP,QAAQ;oCACV;oCACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,mBAAmB,GAAG;gCACtF;yEAAG;wBACL;oBACF;;gBAEA,2EAA2E;gBAC3E,WAAW,iBAAiB;YAC9B;QACF;iCAAG;QAAC;QAAU;QAAa;KAAiB;IAE5C,IAAI,CAAC,YAAY,CAAC,aAAa,OAAO;IAEtC,MAAM,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,WAAW,MAAM,EACjB,QAAQ,MAAM,EACd,mBAAmB,EAAE,EACrB,UAAU,IAAI,EACd,gBAAgB,EAAE,EAClB,gBAAgB,EAAE,EACnB,GAAG;IAEJ,qBACE,6LAAC,qJAAA,CAAA,UAAiB;QAChB,UAAU;QACV,OAAO;QACP,SAAS;QACT,SAAS;kBAET,cAAA,6LAAC,kJAAA,CAAA,UAAc;YACb,OAAO;YACP,SAAS;YACT,UAAU;YACV,gBAAgB;YAChB,SAAS;YACT,SAAS;YACT,aAAa;YACb,YAAY,MAAM,MAAM;YACxB,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,aAAa;YACb,YAAY;;;;;;;;;;;AAIpB;GAhHM;;QAWA,qIAAA,CAAA,cAAW;;;KAXX;uCAkHS"}}, {"offset": {"line": 5320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialManager.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport TutorialStep from './TutorialStep';\r\n\r\n/**\r\n * Componente que gerencia a exibição de tutoriais em uma página\r\n * Deve ser incluído uma vez em cada página que usa tutoriais\r\n */\r\nconst TutorialManager = () => {\r\n  return <TutorialStep />;\r\n};\r\n\r\nexport default TutorialManager;"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA;;;CAGC,GACD,MAAM,kBAAkB;IACtB,qBAAO,6LAAC,gJAAA,CAAA,UAAY;;;;;AACtB;KAFM;uCAIS"}}, {"offset": {"line": 5353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5359, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/tutorials/tutorialMapping.js"], "sourcesContent": ["/**\r\n * Este arquivo mapeia tutoriais específicos para diferentes rotas da aplicação\r\n * Cada rota principal tem seu próprio tutorial com passos específicos\r\n */\r\n\r\n// Tutorial para o Dashboard principal\r\nconst dashboardTutorial = [\r\n    {\r\n      title: \"Bem-vindo ao Dashboard\",\r\n      content: \"Este é o seu painel central, onde você pode visualizar informações importantes e acessar todos os módulos do sistema.\",\r\n      selector: \"#Main-Container-Dashboard\", // Use o ID principal do dashboard\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Cartão de Boas-vindas\",\r\n      content: \"Aqui você visualiza informações personalizadas conforme o horário do dia e seu perfil de usuário.\",\r\n      selector: \".bg-gradient-to-r.from-white.to-gray-50\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Módulos Disponíveis\",\r\n      content: \"Cada card representa um módulo do sistema. Clique em um deles para acessar suas funcionalidades específicas.\",\r\n      selector: \".grid.grid-cols-1.md\\\\:grid-cols-2.lg\\\\:grid-cols-3.xl\\\\:grid-cols-5 > div:first-child\",\r\n      position: \"right\"\r\n    },\r\n    {\r\n      title: \"Próximos Agendamentos\",\r\n      content: \"Visualize seus próximos agendamentos com informações detalhadas sobre pacientes, profissionais e horários.\",\r\n      selector: \".bg-white.dark\\\\:bg-gray-800.rounded-xl h3:has(.calendar)\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Ações Rápidas\",\r\n      content: \"Acesse diretamente as funcionalidades mais utilizadas sem precisar navegar pelos diferentes módulos.\",\r\n      selector: \"div:has(> h3:has(.activity))\",\r\n      position: \"left\",\r\n      highlightPadding: 15\r\n    },\r\n    // {\r\n    //   title: \"Funcionalidades Principais\",\r\n    //   content: \"Você pode agendar consultas, cadastrar pacientes ou visualizar sua agenda completa através destes atalhos.\",\r\n    //   selector: \".grid.grid-cols-1 button:first-child\",\r\n    //   position: \"right\"\r\n    // }\r\n  ];\r\n\r\n//----------------------------------------------AGENDAMENTO--------------------------------------------------------------------------------------\r\n  // Tutorial para Calendário de Agendamentos\r\n  const calendarTutorial = [\r\n    {\r\n      title: \"Calendário de Agendamentos\",\r\n      content: \"Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.\",\r\n      selector: \".fc-view-harness\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Filtros\",\r\n      content: \"Use estes filtros para encontrar agendamentos específicos por profissional, paciente, tipo de serviço ou local.\",\r\n      selector: \".filter-section\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Criar Agendamento\",\r\n      content: \"Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário.\",\r\n      selector: \".fc-daygrid-day:not(.fc-day-other):nth-child(3)\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Visualizações\",\r\n      content: \"Alterne entre visualizações de mês, semana ou dia para ver seus agendamentos com diferentes níveis de detalhe.\",\r\n      selector: \".fc-toolbar-chunk:last-child\",\r\n      position: \"bottom\"\r\n    }\r\n  ];\r\n\r\n  // Tutorial para página de horários de trabalho\r\n  const workingHoursTutorial = [\r\n    {\r\n      title: \"Horários de Trabalho\",\r\n      content: \"Gerencie os Horários de Trabalho dos funcionários cadastrados no sistema.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Exportar Dados\",\r\n      content: \"Exporte os horários de trabalho em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n      selector: \".export-button\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Horários de Trabalho pelo dia da Semana\",\r\n      content: \"Clique aqui para mudar os dias a serem preenchidos.\",\r\n      selector: \"#bordaDiaDaSemana\",\r\n      position: \"left\"\r\n    },\r\n    {\r\n      title: \"Horários selecionáveis\",\r\n      content: \"Clique aqui para selecionar os horários daquele dia da semana que o profissional está disponível.\",\r\n      selector: \"#bordaSelecaoHorario\",\r\n      position: \"left\"\r\n    },\r\n    {\r\n      title: \"Horários por Funcionário\",\r\n      content: \"Clique aqui para selecionar os horários da semana completa de um único funcionário.\",\r\n      selector: \"#bordaNomeFuncionario\",\r\n      position: \"left\"\r\n    },\r\n    {\r\n      title: \"Salve seus Horários e Utilize os Filtros\",\r\n      content: \"Clique aqui para salvar os horários, e para visualizar utilize os filtros.\",\r\n      selector: \"#bordaSalvarHorarioTrabalho\",\r\n      position: \"right\"\r\n    }\r\n  ];\r\n\r\n  // Tutorial para página de localizações\r\n  const locationsTutorial = [\r\n    {\r\n      title: \"Localizações\",\r\n      content: \"Gerencie os locais de atendimento disponíveis no sistema.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Exportar Dados\",\r\n      content: \"Exporte a lista de localizações em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n      selector: \".export-button\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Adicionar Localização\",\r\n      content: \"Clique aqui para cadastrar um novo local de atendimento.\",\r\n      selector: \"button:has(.lucide-plus)\",\r\n      position: \"left\"\r\n    }\r\n  ];\r\n\r\n    // Tutorial para página de ocupações\r\n    const ocuppancyTutorial = [\r\n      {\r\n        title: \"Análise de Ocupação\",\r\n        content: \"Visualize a taxa de Ocupação de locais, profissionais e até pelo tipo do serviço caso deseje limitar.\",\r\n        selector: \"#bordaTaxaOcupacao\",\r\n        position: \"top\"\r\n      },\r\n      {\r\n        title: \"Exportar Dados\",\r\n        content: \"Exporte os dados de ocupação em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n        selector: \".export-button\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Use os filtros!\",\r\n        content: \"Clique para selecionar o periodo do tempo, a data referência, os profissionais, serviços ou até os locais.\",\r\n        selector: \"#bordaFiltroOcupacao\",\r\n        position: \"left\"\r\n      }\r\n    ];\r\n\r\n  // Tutorial para tipos de serviço\r\n  const serviceTypesTutorial = [\r\n    {\r\n      title: \"Tipos de Serviço\",\r\n      content: \"Gerencie os serviços oferecidos pela sua clínica ou consultório.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Exportar Dados\",\r\n      content: \"Exporte a lista de tipos de serviço em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n      selector: \".export-button\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Adicionar Tipo de Serviço\",\r\n      content: \"Clique aqui para cadastrar um novo tipo de serviço.\",\r\n      selector: \"button:has(.lucide-plus)\",\r\n      position: \"left\"\r\n    }\r\n  ];\r\n\r\n//----------------------------------------------PESSOAS--------------------------------------------------------------------------------------\r\n  // Tutorial para página de Clientes\r\n  const clientsListTutorial = [\r\n    {\r\n      title: \"Lista de Clientes\",\r\n      content: \"Aqui você pode visualizar, filtrar e gerenciar todos os clientes cadastrados no sistema.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Pesquisa e Filtros\",\r\n      content: \"Use estes campos para buscar clientes por nome ou email, e filtrar por status.\",\r\n      selector: \"form\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Adicionar Cliente\",\r\n      content: \"Clique aqui para cadastrar um novo cliente no sistema.\",\r\n      selector: \"button:has(.lucide-plus)\",\r\n      position: \"left\"\r\n    }\r\n  ];\r\n\r\n  // Tutorial para página de Pacientes\r\n  const patientsListTutorial = [\r\n    {\r\n      title: \"Lista de Pacientes\",\r\n      content: \"Aqui você pode visualizar, filtrar e gerenciar todos os pacientes cadastrados no sistema.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Pesquisa e Filtros\",\r\n      content: \"Use estes campos para buscar pacientes por nome, email ou CPF, e filtrar por status.\",\r\n      selector: \"form\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Adicionar Paciente\",\r\n      content: \"Clique aqui para cadastrar um novo paciente no sistema.\",\r\n      selector: \"button:has(.lucide-plus)\",\r\n      position: \"left\"\r\n    },\r\n    {\r\n      title: \"Ações por Paciente\",\r\n      content: \"Nesta coluna você pode visualizar detalhes, editar, ativar/desativar ou excluir um paciente.\",\r\n      selector: \"td:last-child\",\r\n      position: \"left\"\r\n    }\r\n  ];\r\n\r\n  // Tutorial para página de detalhes do paciente\r\n  const patientDetailsTutorial = [\r\n    {\r\n      title: \"Detalhes do Paciente\",\r\n      content: \"Esta página mostra informações detalhadas sobre o paciente selecionado.\",\r\n      selector: \"h1\",\r\n      position: \"bottom\"\r\n    },\r\n    {\r\n      title: \"Informações Pessoais\",\r\n      content: \"Aqui você encontra os dados pessoais e de contato do paciente.\",\r\n      selector: \"h2\",\r\n      position: \"right\"\r\n    },\r\n    {\r\n      title: \"Documentos\",\r\n      content: \"Nesta seção você pode visualizar e gerenciar documentos relacionados ao paciente.\",\r\n      selector: \"h3\",\r\n      position: \"bottom\"\r\n    }\r\n  ];\r\n\r\n  // Tutorial para página de convênios\r\n  const insurancesTutorial = [\r\n    {\r\n      title: \"Convênios\",\r\n      content: \"Gerencie os convênios disponíveis para seus pacientes nesta página.\",\r\n      selector: \"table\",\r\n      position: \"top\"\r\n    },\r\n    {\r\n      title: \"Adicionar Convênio\",\r\n      content: \"Clique aqui para cadastrar um novo convênio no sistema.\",\r\n      selector: \"button:has(.lucide-plus)\",\r\n      position: \"left\"\r\n    }\r\n  ];\r\n\r\n    // Tutorial para página de Usuários\r\n    const admUsersTutorial = [\r\n      {\r\n        title: \"Gerenciamento de Usuários\",\r\n        content: \"Esta página permite gerenciar todos os usuários do sistema. Aqui você pode criar, editar, ativar/desativar e excluir usuários, além de gerenciar suas permissões e acesso aos módulos.\",\r\n        selector: \".ModuleHeader h1\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Exportar Dados\",\r\n        content: \"Exporte a lista de usuários em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n        selector: \".export-button\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Adicionar Novo Usuário\",\r\n        content: \"Clique aqui para criar um novo usuário no sistema. Você poderá definir informações pessoais, credenciais de acesso, profissão, função e permissões.\",\r\n        selector: \"button:has(.lucide-plus)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15,\r\n        highlightPadding: 5\r\n      },\r\n      {\r\n        title: \"Pesquisa e Filtros\",\r\n        content: \"Use estes campos para buscar usuários por nome, email ou profissão. Você também pode filtrar por status (ativo/inativo) e por função (administrador, funcionário, etc).\",\r\n        selector: \"#filtroUsuario\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Tabela de Usuários\",\r\n        content: \"Esta tabela exibe todos os usuários do sistema com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.\",\r\n        selector: \"#admin-users-table\",\r\n        position: \"top\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Ações de Usuário\",\r\n        content: \"Aqui você encontra botões para editar informações, gerenciar módulos, permissões, função, ativar/desativar ou excluir usuários.\",\r\n        selector: \"td:last-child .flex.justify-end\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Editar Usuário\",\r\n        content: \"Clique neste botão para modificar as informações pessoais e credenciais do usuário selecionado.\",\r\n        selector: \"#edicaoUsuario\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Gerenciar Módulos\",\r\n        content: \"Este botão permite definir a quais módulos do sistema o usuário terá acesso. Administradores de sistema e de empresa têm acesso automático a todos os módulos.\",\r\n        selector: \"#gerenciarModulo\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Gerenciar Permissões\",\r\n        content: \"Controle detalhado das permissões do usuário dentro de cada módulo. Você pode definir o que o usuário pode visualizar, criar, editar ou excluir.\",\r\n        selector: \"#gerenciarPermissoes\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Fluxo de Criação de Usuário\",\r\n        content: \"O fluxo recomendado para criar um usuário é: 1) Informações pessoais, 2) Documentos, 3) Definir função, 4) Atribuir módulos (para funcionários), 5) Configurar permissões específicas.\",\r\n        selector: \"#admin-users-table\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      }\r\n    ];\r\n\r\n//----------------------------------------------ADMINISTRAÇÃO--------------------------------------------------------------------------------------\r\n    // Tutorial para página de Profissões/Grupos\r\n    const professionsGroupsTutorial = [\r\n      {\r\n        title: \"Gerenciamento de Profissões e Grupos\",\r\n        content: \"Esta página permite gerenciar as profissões e grupos de profissões disponíveis no sistema. Estas informações são essenciais para a criação de usuários e definição de permissões.\",\r\n        selector: \".text-2xl.font-bold\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Abas de Navegação\",\r\n        content: \"Alterne entre as abas 'Profissões' e 'Grupos de Profissões' para gerenciar cada tipo de cadastro separadamente.\",\r\n        selector: \"button[role='tab']\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 15\r\n      },\r\n      {\r\n        title: \"Exportar Dados\",\r\n        content: \"Exporte a lista de profissões ou grupos em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n        selector: \".export-button\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Adicionar Nova Profissão/Grupo\",\r\n        content: \"Clique neste botão para criar uma nova profissão ou grupo no sistema. Você poderá definir nome, descrição, associações e status.\",\r\n        selector: \"button:has(.lucide-plus)\",\r\n        position: \"left\",\r\n        highlightPadding: 5,\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Filtros de Busca\",\r\n        content: \"Use estes campos para buscar profissões ou grupos por nome, filtrar por status ou outras propriedades.\",\r\n        selector: \"input[type='text']\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Tabela de Profissões/Grupos\",\r\n        content: \"Esta tabela exibe todas as profissões ou grupos cadastrados com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.\",\r\n        selector: \"table\",\r\n        position: \"top\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Ações Disponíveis\",\r\n        content: \"Aqui você encontra botões para ver usuários associados, editar ou excluir profissões e grupos.\",\r\n        selector: \"td:last-child .flex.justify-end\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Visibilidade de Empresa\",\r\n        content: \"Administradores de sistema podem ver a qual empresa cada profissão ou grupo pertence. Administradores de empresa só veem registros da própria empresa.\",\r\n        selector: \"th:nth-child(3)\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Dica Importante\",\r\n        content: \"Configure primeiro os grupos de profissões antes de criar as profissões individuais. Isso facilitará a organização e atribuição de permissões posteriormente.\",\r\n        selector: \"table\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      }\r\n    ];\r\n\r\n    // Tutorial para página de Configurações\r\n    const settingsTutorial = [\r\n      {\r\n        title: \"Configurações do Sistema\",\r\n        content: \"Esta página permite personalizar diversos aspectos do sistema de acordo com as necessidades da sua empresa ou organização.\",\r\n        selector: \"h1, .text-2xl.font-bold\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Navegação por Abas\",\r\n        content: \"Use estas abas para navegar entre diferentes categorias de configurações. As opções disponíveis dependem do seu nível de acesso no sistema.\",\r\n        selector: \"button.flex.items-center.gap-2\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 15\r\n      },\r\n      {\r\n        title: \"Configurações Gerais\",\r\n        content: \"Defina informações básicas como nome do site, URL, email administrativo, formato de data e hora, e outras configurações globais.\",\r\n        selector: \"button:has(.lucide-cog), button:has(.lucide-settings)\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Configurações de Empresa\",\r\n        content: \"Gerencie informações da empresa como nome, CNPJ, endereço, contatos e configurações específicas. Administradores de sistema podem gerenciar múltiplas empresas.\",\r\n        selector: \"button:has(.lucide-building)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Configurações de Unidades\",\r\n        content: \"Administre as unidades ou filiais da empresa, incluindo endereços, horários de funcionamento e configurações específicas de cada local.\",\r\n        selector: \"button:has(.lucide-map-pin)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Configurações de Email\",\r\n        content: \"Configure os servidores de email, modelos de mensagens e notificações automáticas enviadas pelo sistema.\",\r\n        selector: \"button:has(.lucide-mail)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Configurações de Backup\",\r\n        content: \"Configure as opções de backup automático do sistema, incluindo frequência, horário e tipos de arquivos a serem salvos.\",\r\n        selector: \"button:has(.lucide-database), button:has(.lucide-hard-drive)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Configurações de Segurança\",\r\n        content: \"Defina políticas de senha, autenticação de dois fatores e outras configurações de segurança para proteger o sistema.\",\r\n        selector: \"button:has(.lucide-shield), button:has(.lucide-lock)\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      },\r\n      {\r\n        title: \"Formulários de Configuração\",\r\n        content: \"Preencha os formulários com as informações necessárias para configurar o sistema de acordo com as necessidades da sua empresa.\",\r\n        selector: \"input[type='text'], select\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Opções de Localização\",\r\n        content: \"Configure o fuso horário, formato de data e hora para todo o sistema de acordo com as necessidades da sua região.\",\r\n        selector: \"select\",\r\n        position: \"bottom\",\r\n        dialogOffsetY: 10\r\n      },\r\n      {\r\n        title: \"Salvar Alterações\",\r\n        content: \"Lembre-se de clicar em 'Salvar' após fazer modificações em cada seção para que as alterações sejam aplicadas.\",\r\n        selector: \"button.bg-primary-500, button.bg-orange-500, button[type='submit']\",\r\n        position: \"left\",\r\n        dialogOffsetX: 15\r\n      }\r\n    ];\r\n\r\n//----------------------------------------------DASHBOARDS NO GERAL--------------------------------------------------------------------------------------\r\n    // Tutorial para o Dashboard de Administração\r\n    const adminDashboardTutorial = [\r\n      {\r\n        title: \"Dashboard de Administração\",\r\n        content: \"Este painel oferece uma visão geral do sistema com métricas importantes, estatísticas de uso e informações sobre usuários e atividades recentes.\",\r\n        selector: \"h1\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Exportar Dados\",\r\n        content: \"Exporte os dados do dashboard em diferentes formatos (Excel ou PDF) usando este botão.\",\r\n        selector: \".export-button\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Seletor de Empresa\",\r\n        content: \"Administradores de sistema podem selecionar diferentes empresas para visualizar dados específicos de cada uma. Administradores de empresa veem apenas dados da própria empresa.\",\r\n        selector: \"select\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Cartões de Estatísticas\",\r\n        content: \"Estes cartões mostram métricas importantes como total de usuários, usuários ativos, clientes e agendamentos, com indicadores de crescimento.\",\r\n        selector: \".grid\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Gráficos e Visualizações\",\r\n        content: \"Visualize dados importantes do sistema através de gráficos interativos que mostram tendências e distribuições.\",\r\n        selector: \".lg\\\\:col-span-2\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Seletor de Período\",\r\n        content: \"Filtre os dados do gráfico por diferentes períodos de tempo para análises mais específicas.\",\r\n        selector: \"select:nth-of-type(2)\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Distribuição de Usuários\",\r\n        content: \"Este gráfico mostra a distribuição de usuários por módulo, ajudando a identificar quais áreas do sistema são mais utilizadas.\",\r\n        selector: \".h-80\",\r\n        position: \"left\"\r\n      },\r\n      {\r\n        title: \"Tabelas de Informações\",\r\n        content: \"Visualize informações detalhadas sobre usuários ativos e atividades recentes no sistema.\",\r\n        selector: \"table\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Informações do Sistema\",\r\n        content: \"Visualize detalhes técnicos como versão do sistema, status do servidor, uso de recursos e outras informações relevantes para administradores.\",\r\n        selector: \".grid:last-child\",\r\n        position: \"bottom\"\r\n      }\r\n    ];\r\n\r\n    // Tutorial para todos os tipos de Dashboards\r\n    const dashboardsTutorial = [\r\n      {\r\n        title: \"Dashboard\",\r\n        content: \"Use filtros no seu Dashboard ou busque para visualizar as informações que deseja\",\r\n        selector: \"#bordadashboards\",\r\n        position: \"bottom\"\r\n      },\r\n      {\r\n        title: \"Tipo de Dashboard\",\r\n        content: \"Cada Dashboard tem suas informações retiradas dependendo de seu módulo.\",\r\n        selector: \"#gradedashboards\",\r\n        position: \"top\"\r\n      }\r\n    ];\r\n\r\n  // Mapa de rotas para tutoriais\r\n  // Cada chave é um padrão de URL, e o valor é o tutorial correspondente\r\n  const tutorialMap = {\r\n    '/dashboard': dashboardTutorial,\r\n    '/dashboard/admin/users': admUsersTutorial,\r\n    '/dashboard/admin/dashboard': adminDashboardTutorial,\r\n    '/dashboard/admin/professions': professionsGroupsTutorial,\r\n    '/dashboard/admin/settings': settingsTutorial,\r\n    '/dashboard/scheduler/appointments-dashboard': dashboardsTutorial,\r\n    '/dashboard/scheduler/working-hours': workingHoursTutorial,\r\n    '/dashboard/scheduler/occupancy': ocuppancyTutorial,\r\n    '/dashboard/scheduler/calendar': calendarTutorial,\r\n    '/dashboard/people/persons': patientsListTutorial,\r\n    '/dashboard/people/clients': clientsListTutorial,\r\n    '/dashboard/people/persons/[id]': patientDetailsTutorial,\r\n    '/dashboard/people/insurances': insurancesTutorial,\r\n    '/dashboard/scheduler/locations': locationsTutorial,\r\n    '/dashboard/scheduler/service-types': serviceTypesTutorial,\r\n  };\r\n\r\n  /**\r\n   * Função para obter o tutorial apropriado com base na rota atual\r\n   * @param {string} pathname - Caminho da URL atual\r\n   * @returns {Object} Tutorial correspondente ou null se não houver correspondência\r\n   */\r\n  export const getTutorialForRoute = (pathname) => {\r\n    // Tenta encontrar uma correspondência exata primeiro\r\n    if (tutorialMap[pathname]) {\r\n      return {\r\n        steps: tutorialMap[pathname],\r\n        name: pathname.replace(/\\//g, '-').replace(/^-/, '') // Converte '/dashboard' para 'dashboard'\r\n      };\r\n    }\r\n\r\n    // Verifica rotas dinâmicas com parâmetros (ex: /dashboard/people/persons/123)\r\n    for (const route in tutorialMap) {\r\n      if (route.includes('[id]') && pathname.match(new RegExp(route.replace('[id]', '\\\\d+')))) {\r\n        return {\r\n          steps: tutorialMap[route],\r\n          name: route.replace(/\\//g, '-').replace(/^-/, '').replace('[id]', 'details')\r\n        };\r\n      }\r\n    }\r\n\r\n    // Se não encontrou nenhuma correspondência\r\n    return null;\r\n  };\r\n\r\n  export default tutorialMap;"], "names": [], "mappings": "AAAA;;;CAGC,GAED,sCAAsC;;;;;AACtC,MAAM,oBAAoB;IACtB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,kBAAkB;IACpB;CAOD;AAEH,iJAAiJ;AAC/I,2CAA2C;AAC3C,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,+CAA+C;AAC/C,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,uCAAuC;AACvC,MAAM,oBAAoB;IACxB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEC,oCAAoC;AACpC,MAAM,oBAAoB;IACxB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEH,iCAAiC;AACjC,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEH,6IAA6I;AAC3I,mCAAmC;AACnC,MAAM,sBAAsB;IAC1B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,oCAAoC;AACpC,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,+CAA+C;AAC/C,MAAM,yBAAyB;IAC7B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,oCAAoC;AACpC,MAAM,qBAAqB;IACzB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEC,mCAAmC;AACnC,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;QACf,kBAAkB;IACpB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;CACD;AAEL,mJAAmJ;AAC/I,4CAA4C;AAC5C,MAAM,4BAA4B;IAChC;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,kBAAkB;QAClB,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;CACD;AAED,wCAAwC;AACxC,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;IACjB;CACD;AAEL,yJAAyJ;AACrJ,6CAA6C;AAC7C,MAAM,yBAAyB;IAC7B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,6CAA6C;AAC7C,MAAM,qBAAqB;IACzB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEH,+BAA+B;AAC/B,uEAAuE;AACvE,MAAM,cAAc;IAClB,cAAc;IACd,0BAA0B;IAC1B,8BAA8B;IAC9B,gCAAgC;IAChC,6BAA6B;IAC7B,+CAA+C;IAC/C,sCAAsC;IACtC,kCAAkC;IAClC,iCAAiC;IACjC,6BAA6B;IAC7B,6BAA6B;IAC7B,kCAAkC;IAClC,gCAAgC;IAChC,kCAAkC;IAClC,sCAAsC;AACxC;AAOO,MAAM,sBAAsB,CAAC;IAClC,qDAAqD;IACrD,IAAI,WAAW,CAAC,SAAS,EAAE;QACzB,OAAO;YACL,OAAO,WAAW,CAAC,SAAS;YAC5B,MAAM,SAAS,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,yCAAyC;QAChG;IACF;IAEA,8EAA8E;IAC9E,IAAK,MAAM,SAAS,YAAa;QAC/B,IAAI,MAAM,QAAQ,CAAC,WAAW,SAAS,KAAK,CAAC,IAAI,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW;YACvF,OAAO;gBACL,OAAO,WAAW,CAAC,MAAM;gBACzB,MAAM,MAAM,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ;YACpE;QACF;IACF;IAEA,2CAA2C;IAC3C,OAAO;AACT;uCAEe"}}, {"offset": {"line": 5951, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5957, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/ContextualHelpButton.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { HelpCircle } from 'lucide-react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useTutorial } from '@/contexts/TutorialContext';\r\nimport { getTutorialForRoute } from '@/tutorials/tutorialMapping';\r\n\r\n/**\r\n * Botão de ajuda contextual que mostra tutoriais específicos com base na rota atual\r\n */\r\nconst ContextualHelpButton = () => {\r\n  const pathname = usePathname();\r\n  const { startTutorial, isActive } = useTutorial();\r\n  const [currentTutorial, setCurrentTutorial] = useState(null);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n\r\n  // Busca o tutorial apropriado quando a rota muda\r\n  useEffect(() => {\r\n    const tutorialData = getTutorialForRoute(pathname);\r\n    setCurrentTutorial(tutorialData);\r\n  }, [pathname]);\r\n\r\n  // Função para iniciar o tutorial contextual\r\n  const handleStartTutorial = () => {\r\n    if (currentTutorial && currentTutorial.steps && currentTutorial.steps.length > 0) {\r\n      startTutorial(currentTutorial.steps, currentTutorial.name);\r\n    } else {\r\n      // Se não temos um tutorial para esta página, podemos mostrar uma mensagem\r\n      console.log('Nenhum tutorial disponível para esta página');\r\n      // Você pode adicionar aqui uma notificação para o usuário\r\n    }\r\n  };\r\n\r\n  // Se o tutorial já estiver ativo, não mostramos o botão\r\n  if (isActive) return null;\r\n\r\n  return (\r\n    <div className=\"fixed bottom-6 right-6 z-50\">\r\n      {/* Tooltip que aparece ao passar o mouse */}\r\n      {isHovering && currentTutorial && (\r\n        <div className=\"absolute bottom-16 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 mb-2 w-48 text-sm text-gray-700 dark:text-gray-300 animate-fade-in\">\r\n          Clique para ver o tutorial desta página\r\n          <div className=\"absolute bottom-0 right-5 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800\"></div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Botão principal */}\r\n      <button\r\n        onClick={handleStartTutorial}\r\n        onMouseEnter={() => setIsHovering(true)}\r\n        onMouseLeave={() => setIsHovering(false)}\r\n        className={`\r\n          w-12 h-12 rounded-full flex items-center justify-center shadow-lg \r\n          transition-all duration-300 hover:shadow-xl\r\n          ${currentTutorial \r\n            ? 'bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700' \r\n            : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'}\r\n        `}\r\n        aria-label=\"Mostrar tutorial da página\"\r\n        disabled={!currentTutorial}\r\n      >\r\n        <HelpCircle size={currentTutorial ? 28 : 24} />\r\n        \r\n        {/* Indicador de \"novo\" para páginas com tutorial */}\r\n        {currentTutorial && (\r\n          <span className=\"absolute -top-1 -right-1 h-3 w-3 rounded-full bg-white border-2 border-primary-500 dark:border-primary-600\"></span>\r\n        )}\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContextualHelpButton;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;;;AAHA;;;;;;AAQA;;CAEC,GACD,MAAM,uBAAuB;;IAC3B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC9C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,mBAAmB;QACrB;yCAAG;QAAC;KAAS;IAEb,4CAA4C;IAC5C,MAAM,sBAAsB;QAC1B,IAAI,mBAAmB,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,CAAC,MAAM,GAAG,GAAG;YAChF,cAAc,gBAAgB,KAAK,EAAE,gBAAgB,IAAI;QAC3D,OAAO;YACL,0EAA0E;YAC1E,QAAQ,GAAG,CAAC;QACZ,0DAA0D;QAC5D;IACF;IAEA,wDAAwD;IACxD,IAAI,UAAU,OAAO;IAErB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,cAAc,iCACb,6LAAC;gBAAI,WAAU;;oBAAmJ;kCAEhK,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAKnB,6LAAC;gBACC,SAAS;gBACT,cAAc,IAAM,cAAc;gBAClC,cAAc,IAAM,cAAc;gBAClC,WAAW,CAAC;;;UAGV,EAAE,kBACE,iGACA,gHAAgH;QACtH,CAAC;gBACD,cAAW;gBACX,UAAU,CAAC;;kCAEX,6LAAC,qNAAA,CAAA,aAAU;wBAAC,MAAM,kBAAkB,KAAK;;;;;;oBAGxC,iCACC,6LAAC;wBAAK,WAAU;;;;;;;;;;;;;;;;;;AAK1B;GA5DM;;QACa,qIAAA,CAAA,cAAW;QACQ,qIAAA,CAAA,cAAW;;;KAF3C;uCA8DS"}}, {"offset": {"line": 6075, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6081, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/dashboard/layout.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { PrivateRoute } from '@/components/PrivateRoute';\r\nimport { Header, modules } from './components';\r\nimport ClientHeader from './ClientHeader';\r\nimport { useRouter } from 'next/navigation';\r\nimport Sidebar from '@/components/dashboard/Sidebar';\r\nimport ClientSidebar from '@/components/dashboard/Sidebar/ClientSidebar';\r\nimport { usePermissions } from '@/hooks/usePermissions';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\n// Componentes de tutorial\r\nimport TutorialManager from '@/components/tutorial/TutorialManager';\r\nimport ContextualHelpButton from '@/components/tutorial/ContextualHelpButton';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isClient } = usePermissions();\r\n  const { user } = useAuth();\r\n\r\n  // Verificar se o usuário é um cliente\r\n  const isClientUser = () => {\r\n    return user?.isClient || user?.role === 'CLIENT';\r\n  };\r\n  const [activeModule, setActiveModule] = useState(null);\r\n  const [activeSubmenu, setActiveSubmenu] = useState(null);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(true);\r\n\r\n  // Determina o módulo ativo e submenu baseado na URL\r\n  useEffect(() => {\r\n    if (pathname) {\r\n      // Exemplo: /dashboard/people/persons/ID => people, persons\r\n      const path = pathname.split('/');\r\n      if (path.length >= 3) {\r\n        const moduleFromUrl = path[2]; // O módulo é o terceiro segmento da URL\r\n        setActiveModule(moduleFromUrl);\r\n\r\n        if (path.length >= 4) {\r\n          const submenuFromUrl = path[3]; // O submenu é o quarto segmento da URL\r\n          setActiveSubmenu(submenuFromUrl);\r\n        }\r\n      } else {\r\n        setActiveModule(null);\r\n        setActiveSubmenu(null);\r\n      }\r\n    }\r\n  }, [pathname]);\r\n\r\n  // Memoize handlers para evitar recriações em cada render\r\n  const handleBackToModules = useCallback(() => {\r\n    router.push('/dashboard');\r\n  }, [router]);\r\n\r\n  const handleModuleSubmenuClick = useCallback((moduleId, submenuId) => {\r\n    router.push(`/dashboard/${moduleId}/${submenuId}`);\r\n  }, [router]);\r\n\r\n  // Verifica se um submenu está ativo (mesmo quando estamos em uma página de detalhes)\r\n  const isSubmenuActive = useCallback((moduleId, submenuId) => {\r\n    return pathname.startsWith(`/dashboard/${moduleId}/${submenuId}`);\r\n  }, [pathname]);\r\n\r\n  const toggleSidebar = useCallback(() => {\r\n    setIsSidebarOpen(prev => !prev);\r\n  }, []);\r\n\r\n  // Obter o título do módulo ativo\r\n  const activeModuleTitle = modules.find(m => m.id === activeModule)?.title || '';\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors\">\r\n      <PrivateRoute>\r\n        <div className=\"flex\">\r\n          {/* Sidebar - Visível em todas as páginas exceto a página principal do dashboard e a página de perfil */}\r\n          {pathname !== '/dashboard' && pathname !== '/dashboard/profile' && (\r\n            isClientUser() ? (\r\n              <ClientSidebar\r\n                activeModule={activeModule}\r\n                activeModuleTitle={activeModuleTitle}\r\n                isSubmenuActive={isSubmenuActive}\r\n                handleModuleSubmenuClick={handleModuleSubmenuClick}\r\n                handleBackToModules={handleBackToModules}\r\n                isSidebarOpen={isSidebarOpen}\r\n              />\r\n            ) : (\r\n              <Sidebar\r\n                activeModule={activeModule}\r\n                activeModuleTitle={activeModuleTitle}\r\n                isSubmenuActive={isSubmenuActive}\r\n                handleModuleSubmenuClick={handleModuleSubmenuClick}\r\n                handleBackToModules={handleBackToModules}\r\n                isSidebarOpen={isSidebarOpen}\r\n              />\r\n            )\r\n          )}\r\n\r\n          {/* Main Content */}\r\n          <div className=\"flex-1\">\r\n            {isClientUser() ? (\r\n              <ClientHeader\r\n                toggleSidebar={toggleSidebar}\r\n                isSidebarOpen={isSidebarOpen}\r\n              />\r\n            ) : (\r\n              <Header\r\n                toggleSidebar={toggleSidebar}\r\n                isSidebarOpen={isSidebarOpen}\r\n              />\r\n            )}\r\n\r\n            <main\r\n              className=\"p-6\"\r\n              id=\"main-content\"\r\n            >\r\n              <div className=\"max-w-7xl mx-auto\">\r\n                {children}\r\n              </div>\r\n            </main>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Componentes de tutorial */}\r\n        <TutorialManager />\r\n        <ContextualHelpButton />\r\n      </PrivateRoute>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA,0BAA0B;AAC1B;AACA;;;AAfA;;;;;;;;;;;;;AAiBe,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEvB,sCAAsC;IACtC,MAAM,eAAe;QACnB,OAAO,MAAM,YAAY,MAAM,SAAS;IAC1C;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU;gBACZ,2DAA2D;gBAC3D,MAAM,OAAO,SAAS,KAAK,CAAC;gBAC5B,IAAI,KAAK,MAAM,IAAI,GAAG;oBACpB,MAAM,gBAAgB,IAAI,CAAC,EAAE,EAAE,wCAAwC;oBACvE,gBAAgB;oBAEhB,IAAI,KAAK,MAAM,IAAI,GAAG;wBACpB,MAAM,iBAAiB,IAAI,CAAC,EAAE,EAAE,uCAAuC;wBACvE,iBAAiB;oBACnB;gBACF,OAAO;oBACL,gBAAgB;oBAChB,iBAAiB;gBACnB;YACF;QACF;oCAAG;QAAC;KAAS;IAEb,yDAAyD;IACzD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACtC,OAAO,IAAI,CAAC;QACd;2DAAG;QAAC;KAAO;IAEX,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC,UAAU;YACtD,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,WAAW;QACnD;gEAAG;QAAC;KAAO;IAEX,qFAAqF;IACrF,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,UAAU;YAC7C,OAAO,SAAS,UAAU,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,WAAW;QAClE;uDAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC;8DAAiB,CAAA,OAAQ,CAAC;;QAC5B;qDAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,oBAAoB,wIAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,SAAS;IAE7E,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,eAAY;;8BACX,6LAAC;oBAAI,WAAU;;wBAEZ,aAAa,gBAAgB,aAAa,wBAAwB,CACjE,+BACE,6LAAC,6JAAA,CAAA,UAAa;4BACZ,cAAc;4BACd,mBAAmB;4BACnB,iBAAiB;4BACjB,0BAA0B;4BAC1B,qBAAqB;4BACrB,eAAe;;;;;iDAGjB,6LAAC,qJAAA,CAAA,UAAO;4BACN,cAAc;4BACd,mBAAmB;4BACnB,iBAAiB;4BACjB,0BAA0B;4BAC1B,qBAAqB;4BACrB,eAAe;;;;;gCAGrB;sCAGA,6LAAC;4BAAI,WAAU;;gCACZ,+BACC,6LAAC,0IAAA,CAAA,UAAY;oCACX,eAAe;oCACf,eAAe;;;;;yDAGjB,6LAAC,wIAAA,CAAA,SAAM;oCACL,eAAe;oCACf,eAAe;;;;;;8CAInB,6LAAC;oCACC,WAAU;oCACV,IAAG;8CAEH,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;8BAOT,6LAAC,mJAAA,CAAA,UAAe;;;;;8BAChB,6LAAC,wJAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;AAI7B;GAjHwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACP,iIAAA,CAAA,iBAAc;QAClB,iIAAA,CAAA,UAAO;;;KAJF"}}, {"offset": {"line": 6291, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}