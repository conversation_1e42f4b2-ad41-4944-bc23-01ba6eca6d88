// src/app/modules/people/services/personsService.js
import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { exportService } from "@/app/services/exportService";
import { extractData, extractEntity, extractErrorMessage } from "@/utils/apiResponseAdapter";

/**
 * Formata o gênero para exibição
 * @param {string} gender - Código do gênero (M, F, O, etc)
 * @returns {string} - Gênero formatado
 */
function formatGender(gender) {
  const genderMap = {
    M: "Masculino",
    F: "Feminino",
    O: "Outro"
  };

  return genderMap[gender] || gender || "Não informado";
}

export const personsService = {
  // Get persons with optional filters
  getPersons: async (filters = {}) => {
    const {
      page = 1,
      limit = 10,
      search = "",
      personIds,
      active,
      clientId,
      relationship,
      companyId,
      sortField = 'fullName', // Default sort by fullName
      sortDirection = 'asc'   // Default sort direction
    } = filters;

    try {
      console.log("getPersons chamado com filtros:", filters);

      // Construir parâmetros para a API
      const params = {
        page,
        limit,
        search: search || undefined,
        active: active === undefined ? undefined : active,
        clientId: clientId || undefined,
        relationship: relationship || undefined,
        companyId: companyId || undefined,
        sortField,
        sortDirection
      };

      // Adicionar personIds como parâmetros separados com notação de array
      if (personIds && personIds.length > 0) {
        // Garantir que personIds seja um array
        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];

        // Adicionar cada ID como um parâmetro separado
        personIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          params[`personIds[${index}]`] = id;
        });

        console.log("Filtrando por múltiplos IDs de pacientes:", personIdsArray);
      }

      console.log("Enviando requisição para /persons com params:", params);
      const response = await api.get("/persons", { params });
      console.log("Resposta bruta da API de persons:", response.data);

      // Usar o adaptador para extrair os dados de forma consistente
      const extractedData = extractData(response.data, 'persons', ['people']);
      console.log("Dados extraídos pelo adaptador:", extractedData);

      return extractedData;
    } catch (error) {
      console.error("Error fetching persons:", error);
      throw error;
    }
  },

  // Get a single person by ID
  getPerson: async (id) => {
    try {
      console.log(`Buscando dados da pessoa ${id}`);
      const response = await api.get(`/persons/${id}`);
      console.log('Dados da pessoa recebidos:', response.data);
      console.log('URL da imagem de perfil:', response.data.profileImageFullUrl);
      // Usar o adaptador para extrair a entidade
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error fetching person ${id}:`, error);
      throw error;
    }
  },

  // Create a new person
  createPerson: async (personData) => {
    try {
      const response = await api.post("/persons", personData);
      return response.data;
    } catch (error) {
      console.error("Error creating person:", error);
      throw error;
    }
  },

  // Update an existing person
  updatePerson: async (id, personData) => {
    try {
      const response = await api.put(`/persons/${id}`, personData);
      return response.data;
    } catch (error) {
      console.error(`Error updating person ${id}:`, error);
      throw error;
    }
  },

  // Toggle person active status
  togglePersonStatus: async (id) => {
    try {
      const response = await api.patch(`/persons/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for person ${id}:`, error);
      throw error;
    }
  },

  // Delete a person (soft delete)
  deletePerson: async (id) => {
    try {
      const response = await api.delete(`/persons/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting person ${id}:`, error);
      throw error;
    }
  },

  // Upload profile image for a person
  uploadProfileImage: async (id, imageFile) => {
    try {
      console.log(`Iniciando upload de imagem para pessoa ${id}`);
      console.log('Arquivo:', imageFile.name, imageFile.type, imageFile.size);

      const formData = new FormData();
      formData.append('profileImage', imageFile);
      console.log('FormData criado com sucesso');

      // Verificar o token de autenticação
      const token = localStorage.getItem('token');
      console.log('Token de autenticação:', token ? 'Presente' : 'Ausente');

      console.log(`Enviando requisição POST para /persons/${id}/profile-image`);
      const response = await api.post(`/persons/${id}/profile-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });

      console.log('Resposta recebida:', response.status, response.statusText);
      console.log('Dados da resposta:', response.data);

      // Verificar se a resposta contém a URL da imagem
      if (response.data && response.data.fullImageUrl) {
        console.log('URL da imagem recebida:', response.data.fullImageUrl);
      } else {
        console.warn('Resposta não contém a URL da imagem');
      }

      return response.data;
    } catch (error) {
      console.error(`Error uploading profile image for person ${id}:`, error);
      console.error('Detalhes do erro:', error.response?.data || error.message);
      throw error;
    }
  },

  // Get profile image URL for a person
  getProfileImageUrl: (id, profileImageUrl) => {
    if (!id) return null;

    // Se tiver a URL completa da imagem, usar ela diretamente
    if (profileImageUrl) {
      return `${api.defaults.baseURL}/uploads/${profileImageUrl}`;
    }

    // Caso contrário, usar a rota da API
    return `${api.defaults.baseURL}/api/persons/${id}/profile-image`;
  },

  // Get all clients for dropdown selection
  getClientsForSelect: async () => {
    try {
      const response = await api.get("/clients", {
        params: { active: true, limit: 100 }
      });

      return response.data.clients.map(client => ({
        value: client.id,
        label: client.login
      }));
    } catch (error) {
      console.error("Error fetching clients for select:", error);
      return [];
    }
  },

  // Get insurances for person
  getPersonInsurances: async (personId) => {
    try {
      const response = await api.get(`/persons/${personId}/insurances`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching insurances for person ${personId}:`, error);
      return [];
    }
  },

  // Upload document for a person
  uploadDocument: async (personId, file, documentType) => {
    try {
      const formData = new FormData();
      formData.append('documents', file);
      formData.append('types', JSON.stringify([documentType]));

      const response = await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error(`Error uploading document for person ${personId}:`, error);
      throw error;
    }
  },

  // Create contact for a person
  createContact: async (personId, contactData) => {
    try {
      const payload = {
        personId: personId,
        name: contactData.name,
        relationship: contactData.relationship || null,
        email: contactData.email || null,
        phone: contactData.phone ? contactData.phone.replace(/\D/g, "") : null,
        notes: contactData.notes || null
      };

      const response = await api.post('/contacts', payload);
      return response.data;
    } catch (error) {
      console.error(`Error creating contact for person ${personId}:`, error);
      throw error;
    }
  },

  // Add insurance to a person
  addPersonInsurance: async (personId, insuranceData) => {
    try {
      const payload = {
        personId: personId,
        insuranceId: insuranceData.insuranceId,
        policyNumber: insuranceData.policyNumber || null,
        validUntil: insuranceData.validUntil || null,
        notes: insuranceData.notes || null
      };

      const response = await api.post('/person-insurances', payload);
      return response.data;
    } catch (error) {
      console.error(`Error adding insurance for person ${personId}:`, error);
      throw error;
    }
  },

  /**
   * Exporta a lista de pessoas com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, status, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportPersons: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await personsService.getPersons({
        ...filters,
        limit: 1000, // Aumentamos o limite para exportar mais dados
      });

      // Extrair os dados das pessoas usando o adaptador
      const { persons } = extractData(response, 'persons', ['people']);
      const data = persons;

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "fullName", header: "Nome Completo" },
        { key: "cpf", header: "CPF", type: "cpf" },
        { key: "email", header: "Email" },
        { key: "phone", header: "Telefone", type: "phone" },
        { key: "birthDate", header: "Data de Nascimento", type: "date" },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "gender", header: "Gênero", format: formatGender },
        { key: "relationship", header: "Relacionamento" },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(person => {
        // Para cada pessoa, montamos um objeto com todas as propriedades que queremos exportar
        return {
          fullName: person.fullName || "",
          cpf: person.cpf || "",
          email: person.email || "",
          phone: person.phone || "",
          birthDate: person.birthDate || "",
          active: person.active,
          gender: person.gender || "",
          relationship: person.relationship || "N/A",
          createdAt: person.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.personIds && filters.personIds.length > 0) {
        subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);
      }
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
      }
      if (filters.relationship) {
        subtitleParts.push(`Tipo: ${filters.relationship}`);
      }
      if (filters.companyId) {
        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "pacientes",
        columns,
        title: "Lista de Pacientes",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar pessoas:", error);
      return false;
    }
  }
};

export default personsService;