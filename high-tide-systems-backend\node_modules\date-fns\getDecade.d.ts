import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link getDecade} function options.
 */
export interface GetDecadeOptions extends ContextOptions<Date> {}
/**
 * @name getDecade
 * @category Decade Helpers
 * @summary Get the decade of the given date.
 *
 * @description
 * Get the decade of the given date.
 *
 * @param date - The given date
 * @param options - An object with options
 *
 * @returns The year of decade
 *
 * @example
 * // Which decade belongs 27 November 1942?
 * const result = getDecade(new Date(1942, 10, 27))
 * //=> 1940
 */
export declare function getDecade(
  date: DateArg<Date> & {},
  options?: GetDecadeOptions | undefined,
): number;
