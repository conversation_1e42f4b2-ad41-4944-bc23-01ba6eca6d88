module.exports = {

"[project]/src/components/PrivateRoute.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// components/PrivateRoute.js
__turbopack_esm__({
    "PrivateRoute": (()=>PrivateRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function PrivateRoute({ children }) {
    const { user, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const token = localStorage.getItem('token');
        if (!loading && !user && !token && pathname !== '/login') {
            router.push('/login');
        }
    }, [
        user,
        loading,
        router,
        pathname
    ]);
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"
            }, void 0, false, {
                fileName: "[project]/src/components/PrivateRoute.js",
                lineNumber: 24,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/PrivateRoute.js",
            lineNumber: 23,
            columnNumber: 7
        }, this);
    }
    return user ? children : null;
}
}}),
"[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "useConstructionMessage": (()=>useConstructionMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ConstructionContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/ConstructionContext.js [app-ssr] (ecmascript)");
"use client";
;
const useConstructionMessage = ()=>{
    const { showConstructionMessage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ConstructionContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useConstruction"])();
    /**
   * Função para adicionar a funcionalidade "Em Construção" a um elemento
   * @param {Object} options - Opções para a mensagem
   * @returns {Object} Objeto com props para adicionar ao elemento
   */ const constructionProps = (options = {})=>{
        return {
            onClick: (e)=>{
                e.preventDefault();
                e.stopPropagation();
                // Usar o elemento clicado como alvo para a mensagem
                const targetElement = e.currentTarget;
                const uniqueSelector = `construction-target-${Date.now()}`;
                // Adicionar um atributo temporário para identificar o elemento
                targetElement.setAttribute('data-construction-target', uniqueSelector);
                // Mostrar a mensagem
                showConstructionMessage({
                    ...options,
                    targetSelector: `[data-construction-target="${uniqueSelector}"]`
                });
                // Remover o atributo após um tempo
                setTimeout(()=>{
                    targetElement.removeAttribute('data-construction-target');
                }, 5000);
            }
        };
    };
    return {
        constructionProps,
        showConstructionMessage
    };
};
const __TURBOPACK__default__export__ = useConstructionMessage;
}}),
"[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
"use client";
;
;
;
/**
 * Botão que exibe uma mensagem "Em Construção" quando clicado
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - Título da mensagem
 * @param {string|React.ReactNode} props.content - Conteúdo/descrição da mensagem
 * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')
 * @param {string} props.icon - Ícone a ser exibido ('Construction', 'HardHat', 'Hammer', 'Wrench', 'AlertTriangle')
 * @param {React.ReactNode} props.children - Conteúdo do botão
 * @param {string} props.className - Classes CSS adicionais
 * @param {Object} props.buttonProps - Propriedades adicionais para o botão
 */ const ConstructionButton = ({ title = 'Em Construção', content = 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.', position = 'auto', icon = 'Construction', children, className = '', ...buttonProps })=>{
    const { constructionProps } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useConstructionMessage"])();
    // Obter props para o botão "Em Construção"
    const constructionButtonProps = constructionProps({
        title,
        content,
        position,
        icon
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: className,
        ...buttonProps,
        ...constructionButtonProps,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/construction/ConstructionButton.js",
        lineNumber: 38,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ConstructionButton;
}}),
"[project]/src/components/construction/withConstruction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "withConstruction": (()=>withConstruction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
"use client";
;
;
;
const withConstruction = (Component, options = {})=>{
    // Definir opções padrão
    const defaultOptions = {
        title: 'Em Construção',
        content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
        position: 'auto',
        icon: 'Construction'
    };
    // Mesclar opções padrão com as opções fornecidas
    const mergedOptions = {
        ...defaultOptions,
        ...options
    };
    // Retornar o componente envolvido
    return function ConstructionWrapper(props) {
        const { constructionProps } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useConstructionMessage"])();
        // Adicionar a funcionalidade "Em Construção" ao componente
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            ...constructionProps(mergedOptions)
        }, void 0, false, {
            fileName: "[project]/src/components/construction/withConstruction.js",
            lineNumber: 30,
            columnNumber: 12
        }, this);
    };
};
const __TURBOPACK__default__export__ = withConstruction;
}}),
"[project]/src/components/construction/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({});
;
;
;
;
}}),
"[project]/src/components/construction/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionDialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/construction/ConstructionDialog.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/construction/ConstructionMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$withConstruction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/construction/withConstruction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/ThemeToggle.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ThemeToggle": (()=>ThemeToggle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ThemeContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/ThemeContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sun$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-ssr] (ecmascript) <export default as Sun>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Moon$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-ssr] (ecmascript) <export default as Moon>");
'use client';
;
;
;
;
function ThemeToggle() {
    const { theme, toggleTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ThemeContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: toggleTheme,
        className: "p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",
        "aria-label": theme === 'light' ? 'Ativar modo escuro' : 'Ativar modo claro',
        children: theme === 'light' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Moon$3e$__["Moon"], {
            size: 20,
            "aria-hidden": "true"
        }, void 0, false, {
            fileName: "[project]/src/components/ThemeToggle.js",
            lineNumber: 17,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sun$3e$__["Sun"], {
            size: 20,
            "aria-hidden": "true"
        }, void 0, false, {
            fileName: "[project]/src/components/ThemeToggle.js",
            lineNumber: 19,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeToggle.js",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/config/appConfig.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Configurações globais da aplicação
 */ // Versão atual do sistema
__turbopack_esm__({
    "APP_DESCRIPTION": (()=>APP_DESCRIPTION),
    "APP_NAME": (()=>APP_NAME),
    "APP_VERSION": (()=>APP_VERSION)
});
const APP_VERSION = 'v0.1';
const APP_NAME = 'High Tide Systems';
const APP_DESCRIPTION = 'Sistema de gestão para clínicas e consultórios';
}}),
"[project]/src/components/chat/ChatButton.js [app-ssr] (ecmascript) <export default as ChatButton>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ChatButton": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/chat/ChatButton.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript) <export default as ConstructionButton>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ConstructionButton": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript)");
}}),
"[project]/src/app/dashboard/components.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Header": (()=>Header),
    "NavLink": (()=>NavLink),
    "moduleSubmenus": (()=>moduleSubmenus),
    "modules": (()=>modules)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$QuickNavContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/QuickNavContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/chat/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeToggle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ThemeToggle.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$appConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/config/appConfig.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/shield-check.js [app-ssr] (ecmascript) <export default as ShieldCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-ssr] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$cog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCog$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user-cog.js [app-ssr] (ecmascript) <export default as UserCog>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-ssr] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatButton$3e$__ = __turbopack_import__("[project]/src/components/chat/ChatButton.js [app-ssr] (ecmascript) <export default as ChatButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript) <export default as ConstructionButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-ssr] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-ssr] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldIcon$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-ssr] (ecmascript) <export default as ShieldIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-ssr] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user-check.js [app-ssr] (ecmascript) <export default as UserCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/brain.js [app-ssr] (ecmascript) <export default as Brain>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-ssr] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/briefcase.js [app-ssr] (ecmascript) <export default as Briefcase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-ssr] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/layout-dashboard.js [app-ssr] (ecmascript) <export default as LayoutDashboard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-ssr] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$no$2d$axes$2d$column$2d$increasing$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js [app-ssr] (ecmascript) <export default as BarChart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calculator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calculator$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/calculator.js [app-ssr] (ecmascript) <export default as Calculator>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/building.js [app-ssr] (ecmascript) <export default as Building>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gift$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gift$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/gift.js [app-ssr] (ecmascript) <export default as Gift>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/graduation-cap.js [app-ssr] (ecmascript) <export default as GraduationCap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserPlus$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user-plus.js [app-ssr] (ecmascript) <export default as UserPlus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-ssr] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-ssr] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/book-open.js [app-ssr] (ecmascript) <export default as BookOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clipboard$2d$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardList$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clipboard-list.js [app-ssr] (ecmascript) <export default as ClipboardList>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/award.js [app-ssr] (ecmascript) <export default as Award>");
'use client';
;
;
;
;
;
;
;
;
;
;
;
// Header Component
const Header = ({ toggleSidebar, isSidebarOpen })=>{
    const { user, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { openQuickNav } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$QuickNavContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuickNav"])();
    // Função para determinar o ícone e as cores do papel do usuário
    const getRoleInfo = ()=>{
        switch(user?.role){
            case 'SYSTEM_ADMIN':
                return {
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"],
                    bgColor: 'bg-red-50 dark:bg-red-900',
                    textColor: 'text-red-700 dark:text-red-300',
                    name: 'Admin do Sistema'
                };
            case 'COMPANY_ADMIN':
                return {
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"],
                    bgColor: 'bg-blue-50 dark:bg-blue-900',
                    textColor: 'text-blue-700 dark:text-blue-300',
                    name: 'Admin da Empresa'
                };
            default:
                return {
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$cog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCog$3e$__["UserCog"],
                    bgColor: 'bg-green-50 dark:bg-green-900',
                    textColor: 'text-green-700 dark:text-green-300',
                    name: 'Funcionário'
                };
        }
    };
    const roleInfo = getRoleInfo();
    const RoleIcon = roleInfo.icon;
    // Pegar primeira letra de cada nome para o avatar
    const getInitials = ()=>{
        if (!user?.fullName) return 'U';
        const names = user.fullName.split(' ');
        if (names.length === 1) return names[0].charAt(0);
        return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: toggleSidebar,
                        className: "p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors",
                        "aria-label": isSidebarOpen ? "Fechar menu lateral" : "Abrir menu lateral",
                        children: isSidebarOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 22,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/components.js",
                            lineNumber: 76,
                            columnNumber: 28
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                            size: 22,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/components.js",
                            lineNumber: 76,
                            columnNumber: 65
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    src: "/logo_horizontal_sem_fundo.png",
                                    alt: "High Tide Logo",
                                    className: "h-10 mr-2.5 dark:invert dark:text-white"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/components.js",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$appConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APP_VERSION"]
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/components.js",
                                    lineNumber: 86,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/components.js",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/components.js",
                lineNumber: 70,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: openQuickNav,
                        className: "flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors",
                        "aria-label": "Abrir pesquisa rápida",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                size: 18,
                                className: "text-gray-400 dark:text-gray-500",
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 99,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "hidden sm:inline",
                                children: "Pesquisar..."
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 100,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Ctrl + K"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/components.js",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 94,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatButton$3e$__["ChatButton"], {}, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 107,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
                        className: "p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors",
                        "aria-label": "Notificações",
                        title: "Sistema de Notificações",
                        content: "O sistema de notificações está em desenvolvimento e estará disponível em breve. Você receberá alertas sobre eventos importantes no sistema.",
                        icon: "Bell",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                size: 20,
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 116,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "absolute top-1 right-1 h-2 w-2 rounded-full bg-primary-500",
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 117,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/dashboard/admin/settings'),
                        className: "p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",
                        "aria-label": "Configurações",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                            size: 20,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/components.js",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 121,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeToggle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeToggle"], {}, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 130,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 border-l border-gray-200 dark:border-gray-700 mx-1",
                        "aria-hidden": "true"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 133,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative group",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                "aria-expanded": "false",
                                "aria-haspopup": "true",
                                "aria-label": "Menu do usuário",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-9 w-9 rounded-full flex items-center justify-center font-medium overflow-hidden",
                                        children: user?.profileImageFullUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: user.profileImageFullUrl,
                                            alt: `Foto de perfil de ${user?.fullName || 'Usuário'}`,
                                            className: "h-10 w-10 rounded-full object-cover",
                                            onError: (e)=>{
                                                e.target.onerror = null;
                                                e.target.style.display = 'none';
                                                e.target.parentNode.innerHTML = getInitials();
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/components.js",
                                            lineNumber: 145,
                                            columnNumber: 17
                                        }, this) : getInitials()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "hidden md:block text-left",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1",
                                                children: user?.fullName || 'Usuário'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 161,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `text-xs ${roleInfo.textColor} px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 ${roleInfo.bgColor}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(RoleIcon, {
                                                        size: 10,
                                                        className: "mr-1",
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/components.js",
                                                        lineNumber: 163,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: roleInfo.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/components.js",
                                                        lineNumber: 164,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 162,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                        size: 16,
                                        className: "text-gray-400 dark:text-gray-500 hidden md:block",
                                        "aria-hidden": "true"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 168,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right",
                                role: "menu",
                                "aria-orientation": "vertical",
                                "aria-labelledby": "user-menu-button",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-800 dark:text-gray-200",
                                                children: user?.fullName || 'Usuário'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 177,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 dark:text-gray-400 truncate",
                                                children: user?.email || '<EMAIL>'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 178,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-4 py-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 dark:text-gray-400",
                                                children: "Empresa"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 182,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-800 dark:text-gray-200",
                                                children: user?.company?.name || 'Minha Empresa'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 183,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 181,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border-t border-gray-100 dark:border-gray-700 pt-1 mt-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/dashboard/profile'),
                                                className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                                role: "menuitem",
                                                children: "Meu Perfil"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 187,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: logout,
                                                className: "w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors",
                                                role: "menuitem",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                        size: 14,
                                                        className: "mr-2",
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/components.js",
                                                        lineNumber: 199,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Sair do Sistema"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/components.js",
                                                lineNumber: 194,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/components.js",
                                        lineNumber: 186,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/components.js",
                                lineNumber: 172,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/components.js",
                        lineNumber: 136,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/components.js",
                lineNumber: 92,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/components.js",
        lineNumber: 68,
        columnNumber: 5
    }, this);
};
// Navigation Link Component - Atualizado para usar o sistema de temas por módulo
const NavLink = ({ icon: Icon, title, href, isAccessible, moduleId = 'scheduler' })=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    // Verifica se o link está ativo comparando com o pathname atual
    const isActive = pathname === href;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: ()=>router.push(href),
        disabled: !isAccessible,
        className: `
        w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors
        ${isActive ? `bg-module-${moduleId}-bg text-module-${moduleId}-icon` : isAccessible ? 'text-gray-600 hover:bg-gray-100' : 'opacity-50 cursor-not-allowed'}
      `,
        "aria-current": isActive ? 'page' : undefined,
        role: "link",
        "aria-disabled": !isAccessible,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                size: 20,
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/components.js",
                lineNumber: 235,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "font-medium",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/components.js",
                lineNumber: 236,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/components.js",
        lineNumber: 219,
        columnNumber: 5
    }, this);
};
const modules = [
    {
        id: 'admin',
        title: 'Administração',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldIcon$3e$__["ShieldIcon"],
        description: 'Gerencie todo o sistema, incluindo usuários e configurações.',
        role: 'ADMIN'
    },
    {
        id: 'financial',
        title: 'Financeiro',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"],
        description: 'Controle receitas, despesas e gere relatórios financeiros detalhados.',
        role: 'FINANCIAL'
    },
    {
        id: 'hr',
        title: 'RH',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
        description: 'Gerencie informações de funcionários, admissões e folha de pagamento.',
        role: 'RH'
    },
    {
        id: 'people',
        title: 'Pessoas',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__["UserCheck"],
        description: 'Cadastre e gerencie informações de pacientes e clientes.',
        role: 'BASIC'
    },
    {
        id: 'scheduler',
        title: 'Agendamento',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
        description: 'Agende e gerencie compromissos, consultas e eventos.',
        role: 'BASIC'
    },
    {
        id: 'abaplus',
        title: 'ABA+',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brain$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brain$3e$__["Brain"],
        description: 'Gerencie programas ABA, acompanhamento terapêutico.',
        role: 'BASIC'
    }
];
const moduleSubmenus = {
    admin: [
        {
            id: 'introduction',
            title: 'Introdução',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"],
            description: 'Visão geral do módulo de administração'
        },
        {
            id: 'users',
            title: 'Usuários',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            description: 'Gerenciar usuários do sistema'
        },
        {
            id: 'professions',
            title: 'Profissões',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
            description: 'Gerenciar profissões e grupos'
        },
        {
            id: 'settings',
            title: 'Configurações',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"],
            description: 'Configurações gerais do sistema'
        },
        {
            id: 'logs',
            title: 'Logs',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
            description: 'Histórico de atividades do sistema'
        },
        {
            id: 'backup',
            title: 'Backup',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"],
            description: 'Gerenciamento de backup dos dados'
        },
        {
            id: 'dashboard',
            title: 'Dashboard',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__["LayoutDashboard"],
            description: 'Visão geral do sistema'
        }
    ],
    financial: [
        {
            id: 'invoices',
            title: 'Faturas',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
            description: 'Gerenciar faturas e cobranças'
        },
        {
            id: 'payments',
            title: 'Pagamentos',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"],
            description: 'Controle de pagamentos'
        },
        {
            id: 'expenses',
            title: 'Despesas',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"],
            description: 'Gestão de despesas'
        },
        {
            id: 'reports',
            title: 'Relatórios',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$no$2d$axes$2d$column$2d$increasing$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart$3e$__["BarChart"],
            description: 'Relatórios financeiros'
        },
        {
            id: 'cashflow',
            title: 'Fluxo de Caixa',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"],
            description: 'Análise de fluxo de caixa'
        }
    ],
    hr: [
        {
            id: 'employees',
            title: 'Funcionários',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            description: 'Gerenciar funcionários'
        },
        {
            id: 'payroll',
            title: 'Folha de Pagamento',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calculator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calculator$3e$__["Calculator"],
            description: 'Processamento de salários'
        },
        {
            id: 'documents',
            title: 'Documentos',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
            description: 'Documentos e formulários de RH'
        },
        {
            id: 'departments',
            title: 'Departamentos',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"],
            description: 'Gestão de departamentos'
        },
        {
            id: 'attendance',
            title: 'Ponto',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
            description: 'Controle de ponto e ausências'
        },
        {
            id: 'benefits',
            title: 'Benefícios',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gift$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gift$3e$__["Gift"],
            description: 'Gestão de benefícios'
        },
        {
            id: 'training',
            title: 'Treinamentos',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$graduation$2d$cap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__GraduationCap$3e$__["GraduationCap"],
            description: 'Gestão de treinamentos'
        }
    ],
    people: [
        {
            id: 'introduction',
            title: 'Introdução',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"],
            description: 'Visão geral do módulo de pessoas'
        },
        {
            id: 'clients',
            title: 'Clientes',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__UserPlus$3e$__["UserPlus"],
            description: 'Gerenciar clientes e contas'
        },
        {
            id: 'persons',
            title: 'Pacientes',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            description: 'Gerenciar cadastro de pacientes'
        },
        {
            id: 'insurances',
            title: 'Convênios',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"],
            description: 'Gerenciar convênios associados'
        },
        {
            id: 'insurance-limits',
            title: 'Limites de Convênio',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"],
            description: 'Gerenciar limites de serviço por convênio'
        },
        {
            id: 'dashboard',
            title: 'Dashboard',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__["LayoutDashboard"],
            description: 'Análise e estatísticas de pessoas'
        }
    ],
    scheduler: [
        {
            id: 'introduction',
            title: 'Introdução',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"],
            description: 'Visão geral do módulo de agendamento'
        },
        {
            id: 'calendar',
            title: 'Agendar Consulta',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
            description: 'Visualizar agenda completa'
        },
        {
            id: 'working-hours',
            title: 'Horários de Trabalho',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
            description: 'Configurar horários de trabalho'
        },
        {
            id: 'service-types',
            title: 'Tipos de Serviço',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"],
            description: 'Gerenciar tipos de serviço'
        },
        {
            id: 'locations',
            title: 'Localizações',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"],
            description: 'Gerenciar localizações e endereços'
        },
        {
            id: 'occupancy',
            title: 'Ocupação',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"],
            description: 'Análise detalhada da ocupação dos profissionais'
        },
        {
            id: 'appointments-report',
            title: 'Relatório',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
            description: 'Gerenciar agendamentos em formato de lista'
        },
        {
            id: 'appointments-dashboard',
            title: 'Dashboard',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__["LayoutDashboard"],
            description: 'Análise de agendamentos e estatísticas'
        }
    ],
    abaplus: [
        {
            id: 'introduction',
            title: 'Introdução',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"],
            description: 'Visão geral do módulo ABA+'
        },
        {
            id: 'dashboard',
            title: 'Dashboard',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__["LayoutDashboard"],
            description: 'Análise e estatísticas ABA+'
        },
        {
            id: 'cadastro',
            title: 'Cadastro',
            type: 'group',
            items: [
                {
                    id: 'skills',
                    title: 'Habilidades',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"],
                    description: 'Gerenciar habilidades e competências'
                },
                {
                    id: 'programs',
                    title: 'Programas',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
                    description: 'Gerenciar programas terapêuticos'
                },
                {
                    id: 'evaluations',
                    title: 'Avaliações',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clipboard$2d$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardList$3e$__["ClipboardList"],
                    description: 'Gerenciar avaliações e protocolos'
                },
                {
                    id: 'standard-criteria',
                    title: 'Critérios Padrão',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__["Award"],
                    description: 'Gerenciar critérios padrão'
                },
                {
                    id: 'curriculum-folders',
                    title: 'Pastas Curriculares',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
                    description: 'Gerenciar pastas curriculares dos aprendizes'
                }
            ]
        },
        {
            id: 'atendimento',
            title: 'Atendimento',
            type: 'group',
            items: [
                {
                    id: 'anamnese',
                    title: 'Anamnese',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clipboard$2d$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardList$3e$__["ClipboardList"],
                    description: 'Gerenciar anamneses dos pacientes'
                },
                {
                    id: 'evolucoes-diarias',
                    title: 'Evoluções Diárias',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"],
                    description: 'Gerenciar evoluções diárias dos atendimentos'
                },
                {
                    id: 'sessao',
                    title: 'Sessão',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
                    description: 'Gerenciar sessões de atendimento'
                }
            ]
        }
    ]
};
;
}}),
"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("module", () => require("module"));

module.exports = mod;
}}),
"[project]/src/app/services/exportService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// services/exportService.js
__turbopack_esm__({
    "exportService": (()=>exportService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/file-saver/dist/FileSaver.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2d$autotable$2f$dist$2f$jspdf$2e$plugin$2e$autotable$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-ssr] (ecmascript)"); // Alterado para importar como função
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html2canvas$2f$dist$2f$html2canvas$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/html2canvas/dist/html2canvas.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/xlsx/xlsx.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
// Formatação para campos comuns
const formatters = {
    // Formata datas para o padrão brasileiro
    date: (value)=>{
        if (!value) return "";
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(value), "dd/MM/yyyy", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ptBR"]
            });
        } catch (error) {
            return value;
        }
    },
    // Formata CPF (000.000.000-00)
    cpf: (value)=>{
        if (!value) return "";
        const cpfNumbers = value.replace(/\D/g, "");
        return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
    },
    // Formata telefone (00) 00000-0000
    phone: (value)=>{
        if (!value) return "";
        const phoneNumbers = value.replace(/\D/g, "");
        return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    },
    // Formata valores booleanos
    boolean: (value, options = {
        trueText: "Sim",
        falseText: "Não"
    })=>{
        return value ? options.trueText : options.falseText;
    },
    // Formata valores monetários
    currency: (value)=>{
        if (value === null || value === undefined) return "";
        return new Intl.NumberFormat("pt-BR", {
            style: "currency",
            currency: "BRL"
        }).format(value);
    }
};
const exportService = {
    /**
   * Exporta dados para XLSX ou PDF
   * @param {Object[]|Object} data - Dados a serem exportados (array de objetos para tabela única ou objeto com arrays para múltiplas tabelas)
   * @param {Object} options - Opções de exportação
   * @param {string} options.format - Formato de exportação ('xlsx', 'pdf' ou 'image')
   * @param {string} options.filename - Nome do arquivo sem extensão
   * @param {Object[]} options.columns - Definição das colunas (para tabela única)
   * @param {Object} options.formatOptions - Opções adicionais de formatação
   * @param {string} options.title - Título principal do documento
   * @param {string} options.subtitle - Subtítulo do documento
   * @param {boolean} options.multiTable - Indica se os dados contêm múltiplas tabelas
   * @param {Object[]} options.tables - Definição das tabelas para exportação múltipla
   * @param {string} options.tables[].name - Nome da propriedade no objeto data que contém os dados da tabela
   * @param {string} options.tables[].title - Título da tabela
   * @param {Object[]} options.tables[].columns - Definição das colunas para esta tabela
   * @returns {Promise<boolean>} - Sucesso da exportação
   */ exportData: async (data, options = {})=>{
        try {
            const { format = "xlsx", filename = "export", columns = [], formatOptions = {}, title = null, subtitle = null, multiTable = false, tables = [] } = options;
            // Verifica se estamos lidando com múltiplas tabelas
            if (multiTable && !Array.isArray(data)) {
                // Caso de múltiplas tabelas (objeto com arrays)
                if (format === "xlsx") {
                    // Criar um workbook para múltiplas tabelas
                    const workbook = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_new();
                    // Data atual formatada para o cabeçalho
                    const currentDate = new Date();
                    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
                    // Processar cada tabela definida
                    for (const table of tables){
                        const tableData = data[table.name];
                        if (!tableData || !Array.isArray(tableData)) {
                            console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
                            continue;
                        }
                        // Formatar os dados desta tabela
                        const formattedTableData = tableData.map((item)=>{
                            const formattedItem = {};
                            table.columns.forEach((col)=>{
                                let value = item[col.key];
                                // Aplica formatação personalizada se especificada
                                if (col.format && typeof col.format === 'function') {
                                    formattedItem[col.key] = col.format(value, item);
                                } else if (col.type && formatters[col.type]) {
                                    formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
                                } else {
                                    formattedItem[col.key] = value !== null && value !== undefined ? value : '';
                                }
                            });
                            return formattedItem;
                        });
                        // Preparar dados para a worksheet
                        let worksheetData = [];
                        // Adiciona título e subtítulo se existirem
                        if (title) {
                            worksheetData.push([
                                title
                            ]);
                            if (table.title) {
                                worksheetData.push([
                                    table.title
                                ]);
                            }
                            worksheetData.push([
                                `Gerado em: ${formattedDate}`
                            ]);
                            if (subtitle) {
                                worksheetData.push([
                                    subtitle
                                ]);
                            }
                            worksheetData.push([]); // Linha em branco
                        }
                        // Adiciona os cabeçalhos
                        const headers = table.columns.map((col)=>col.header || col.key);
                        worksheetData.push(headers);
                        // Adiciona os dados
                        formattedTableData.forEach((item)=>{
                            const row = table.columns.map((col)=>item[col.key]);
                            worksheetData.push(row);
                        });
                        // Cria a worksheet
                        const worksheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].aoa_to_sheet(worksheetData);
                        // Configura os estilos
                        if (title) {
                            // Mescla células para o título
                            worksheet['!merges'] = [
                                {
                                    s: {
                                        r: 0,
                                        c: 0
                                    },
                                    e: {
                                        r: 0,
                                        c: headers.length - 1
                                    }
                                }
                            ];
                            if (table.title) {
                                worksheet['!merges'].push({
                                    s: {
                                        r: 1,
                                        c: 0
                                    },
                                    e: {
                                        r: 1,
                                        c: headers.length - 1
                                    }
                                } // Subtítulo da tabela
                                );
                                worksheet['!merges'].push({
                                    s: {
                                        r: 2,
                                        c: 0
                                    },
                                    e: {
                                        r: 2,
                                        c: headers.length - 1
                                    }
                                } // Data
                                );
                            } else {
                                worksheet['!merges'].push({
                                    s: {
                                        r: 1,
                                        c: 0
                                    },
                                    e: {
                                        r: 1,
                                        c: headers.length - 1
                                    }
                                } // Data
                                );
                            }
                            if (subtitle) {
                                worksheet['!merges'].push({
                                    s: {
                                        r: table.title ? 3 : 2,
                                        c: 0
                                    },
                                    e: {
                                        r: table.title ? 3 : 2,
                                        c: headers.length - 1
                                    }
                                } // Subtítulo
                                );
                            }
                            // Ajusta largura das colunas
                            if (!worksheet['!cols']) worksheet['!cols'] = [];
                            table.columns.forEach((col, idx)=>{
                                // Calcula largura ideal para cada coluna
                                const headerWidth = (col.header || col.key).length * 1.2;
                                let maxDataWidth = 0;
                                // Verifica o tamanho máximo dos dados em cada coluna
                                formattedTableData.forEach((item)=>{
                                    const cellValue = item[col.key];
                                    const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';
                                    maxDataWidth = Math.max(maxDataWidth, cellText.length);
                                });
                                worksheet['!cols'][idx] = {
                                    wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))
                                };
                            });
                        }
                        // Limita o nome da planilha a 31 caracteres (limite do Excel)
                        let sheetName = table.title || table.name;
                        // Se o nome for muito longo, cria um nome curto
                        if (sheetName.length > 31) {
                            // Tenta usar apenas a primeira palavra do título
                            const firstWord = sheetName.split(' ')[0];
                            if (firstWord.length <= 28) {
                                sheetName = firstWord + "...";
                            } else {
                                // Se ainda for muito longo, trunca para 28 caracteres e adiciona "..."
                                sheetName = sheetName.substring(0, 28) + "...";
                            }
                        }
                        // Adiciona a worksheet ao workbook
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_append_sheet(workbook, worksheet, sheetName);
                    }
                    try {
                        // Verifica se há pelo menos uma planilha no workbook
                        if (workbook.SheetNames && workbook.SheetNames.length > 0) {
                            // Verifica se todos os nomes de planilhas estão dentro do limite
                            let allNamesValid = true;
                            for (const sheetName of workbook.SheetNames){
                                if (sheetName.length > 31) {
                                    console.error(`Nome de planilha muito longo: "${sheetName}" (${sheetName.length} caracteres)`);
                                    allNamesValid = false;
                                    break;
                                }
                            }
                            if (!allNamesValid) {
                                // Criar uma planilha padrão com mensagem de erro
                                workbook.SheetNames = []; // Limpa as planilhas existentes
                                workbook.Sheets = {}; // Limpa as planilhas existentes
                                const worksheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].aoa_to_sheet([
                                    [
                                        "Erro na exportação"
                                    ],
                                    [
                                        "Um ou mais nomes de planilhas excedem o limite de 31 caracteres"
                                    ],
                                    [
                                        "Por favor, contate o suporte técnico"
                                    ]
                                ]);
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_append_sheet(workbook, worksheet, "Erro");
                            }
                            // Converte para binário e salva
                            const excelBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["write"])(workbook, {
                                bookType: "xlsx",
                                type: "array",
                                bookSST: false,
                                compression: true
                            });
                            const blob = new Blob([
                                excelBuffer
                            ], {
                                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            });
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.xlsx`);
                            return allNamesValid;
                        } else {
                            console.error("Nenhuma planilha foi criada no workbook");
                            // Criar uma planilha padrão com mensagem de erro
                            const worksheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].aoa_to_sheet([
                                [
                                    "Erro na exportação"
                                ],
                                [
                                    "Não foi possível gerar as planilhas com os dados fornecidos"
                                ],
                                [
                                    "Por favor, tente novamente ou contate o suporte"
                                ]
                            ]);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_append_sheet(workbook, worksheet, "Erro");
                            const excelBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["write"])(workbook, {
                                bookType: "xlsx",
                                type: "array",
                                bookSST: false,
                                compression: true
                            });
                            const blob = new Blob([
                                excelBuffer
                            ], {
                                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            });
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.xlsx`);
                            return false;
                        }
                    } catch (error) {
                        console.error("Erro ao gerar arquivo Excel:", error);
                        try {
                            // Tentar criar um arquivo de erro
                            const workbook = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_new();
                            const worksheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].aoa_to_sheet([
                                [
                                    "Erro na exportação"
                                ],
                                [
                                    "Ocorreu um erro ao gerar o arquivo Excel"
                                ],
                                [
                                    "Detalhes do erro: " + (error.message || "Erro desconhecido")
                                ],
                                [
                                    "Por favor, tente novamente ou contate o suporte"
                                ]
                            ]);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_append_sheet(workbook, worksheet, "Erro");
                            const excelBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["write"])(workbook, {
                                bookType: "xlsx",
                                type: "array",
                                bookSST: false,
                                compression: true
                            });
                            const blob = new Blob([
                                excelBuffer
                            ], {
                                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            });
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}-erro.xlsx`);
                        } catch (fallbackError) {
                            console.error("Erro ao gerar arquivo de erro:", fallbackError);
                        }
                        return false;
                    }
                } else if (format === "pdf") {
                    // Cria um novo documento PDF no tamanho A4
                    const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsPDF"]({
                        orientation: "portrait",
                        unit: "mm",
                        format: "a4"
                    });
                    // Configurações da página
                    const pageWidth = doc.internal.pageSize.width;
                    const pageHeight = doc.internal.pageSize.height;
                    const margin = 10;
                    // Define cores do tema
                    const themeColors = {
                        primary: {
                            light: [
                                255,
                                153,
                                51
                            ],
                            dark: [
                                255,
                                127,
                                0
                            ],
                            text: [
                                255,
                                255,
                                255
                            ] // #FFFFFF (texto branco)
                        },
                        secondary: {
                            light: [
                                255,
                                237,
                                213
                            ],
                            dark: [
                                154,
                                52,
                                18
                            ],
                            border: [
                                251,
                                146,
                                60
                            ] // #FB923C (orange-400)
                        }
                    };
                    // Desenha cabeçalho com título principal
                    const headerHeight = subtitle ? 30 : 25;
                    // Simulando um gradiente com múltiplos retângulos coloridos
                    const gradientSteps = 20;
                    const stepHeight = headerHeight / gradientSteps;
                    for(let i = 0; i < gradientSteps; i++){
                        const ratio = i / gradientSteps;
                        // Interpola as cores para criar efeito de gradiente
                        const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);
                        const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);
                        const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);
                        doc.setFillColor(r, g, b);
                        doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');
                    }
                    // Adiciona título no cabeçalho
                    if (title) {
                        doc.setFont("helvetica", "bold");
                        doc.setFontSize(18);
                        doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho
                        doc.text(title, pageWidth / 2, 15, {
                            align: "center"
                        });
                    }
                    // Adiciona subtítulo no cabeçalho se existir
                    if (subtitle) {
                        doc.setFont("helvetica", "normal");
                        doc.setFontSize(10);
                        doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência
                        doc.text(subtitle, pageWidth / 2, 22, {
                            align: "center"
                        });
                    }
                    // Data de geração do relatório
                    const currentDate = new Date();
                    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
                    doc.setFontSize(8);
                    doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, {
                        align: "right"
                    });
                    // Posição vertical atual para adicionar tabelas
                    let yPosition = headerHeight + 10;
                    // Processar cada tabela
                    for(let tableIndex = 0; tableIndex < tables.length; tableIndex++){
                        const table = tables[tableIndex];
                        const tableData = data[table.name];
                        if (!tableData || !Array.isArray(tableData)) {
                            console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
                            continue;
                        }
                        // Formatar os dados desta tabela
                        const formattedTableData = tableData.map((item)=>{
                            const formattedItem = {};
                            table.columns.forEach((col)=>{
                                let value = item[col.key];
                                // Aplica formatação personalizada se especificada
                                if (col.format && typeof col.format === 'function') {
                                    formattedItem[col.key] = col.format(value, item);
                                } else if (col.type && formatters[col.type]) {
                                    formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
                                } else {
                                    formattedItem[col.key] = value !== null && value !== undefined ? value : '';
                                }
                            });
                            return formattedItem;
                        });
                        // Adiciona título da tabela
                        if (table.title) {
                            // Verifica se precisa adicionar uma nova página
                            if (yPosition > pageHeight - 40) {
                                doc.addPage();
                                yPosition = 20;
                            }
                            doc.setFont("helvetica", "bold");
                            doc.setFontSize(14);
                            doc.setTextColor(50, 50, 50);
                            doc.text(table.title, margin, yPosition);
                            yPosition += 10;
                        }
                        // Prepara os dados para a tabela
                        const headers = table.columns.map((col)=>col.header || col.key);
                        const rows = formattedTableData.map((item)=>{
                            return table.columns.map((col)=>item[col.key]);
                        });
                        // Prepara cabeçalhos mais curtos para prevenir quebras
                        const shortHeaders = headers.map((header)=>{
                            // Substituições específicas para cabeçalhos problemáticos
                            const replacements = {
                                'Nome Completo': 'Nome',
                                'Data de Nascimento': 'Nascimento',
                                'Data de Cadastro': 'Cadastro',
                                'Relacionamento': 'Relação',
                                'Telefone': 'Telefone'
                            };
                            return replacements[header] || header;
                        });
                        // Adiciona a tabela
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2d$autotable$2f$dist$2f$jspdf$2e$plugin$2e$autotable$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(doc, {
                            startY: yPosition,
                            head: [
                                shortHeaders
                            ],
                            body: rows,
                            theme: "grid",
                            headStyles: {
                                fillColor: themeColors.primary.dark,
                                textColor: themeColors.primary.text,
                                fontStyle: "bold",
                                halign: "center",
                                fontSize: 9,
                                cellPadding: {
                                    top: 3,
                                    right: 2,
                                    bottom: 3,
                                    left: 2
                                },
                                lineWidth: 0.1,
                                minCellWidth: 15,
                                overflow: 'linebreak'
                            },
                            styles: {
                                fontSize: 9,
                                cellPadding: {
                                    top: 2,
                                    right: 2,
                                    bottom: 2,
                                    left: 2
                                },
                                overflow: "linebreak",
                                lineColor: [
                                    220,
                                    220,
                                    220
                                ],
                                lineWidth: 0.1
                            },
                            tableWidth: 'auto',
                            bodyStyles: {
                                minCellHeight: 10
                            },
                            alternateRowStyles: {
                                fillColor: [
                                    252,
                                    252,
                                    252
                                ]
                            },
                            columnStyles: table.columns.reduce((styles, col, index)=>{
                                // Definir larguras mínimas específicas para evitar quebras nos títulos
                                const minWidths = {
                                    'fullName': 30,
                                    'cpf': 20,
                                    'email': 28,
                                    'phone': 20,
                                    'birthDate': 20,
                                    'gender': 15,
                                    'relationship': 20,
                                    'createdAt': 20,
                                    'active': 15
                                };
                                // Define a largura baseada na configuração ou no mínimo predefinido
                                if (col.key && minWidths[col.key]) {
                                    styles[index] = {
                                        ...styles[index],
                                        cellWidth: minWidths[col.key],
                                        overflow: 'linebreak'
                                    };
                                } else if (col.width) {
                                    styles[index] = {
                                        ...styles[index],
                                        cellWidth: col.width
                                    };
                                }
                                // Aplica o alinhamento se definido
                                if (col.align) {
                                    styles[index] = {
                                        ...styles[index],
                                        halign: col.align
                                    };
                                }
                                return styles;
                            }, {}),
                            margin: {
                                top: yPosition,
                                left: margin,
                                right: margin,
                                bottom: margin + 15
                            },
                            didDrawPage: function(data) {
                                // Adiciona rodapé colorido em cada página
                                doc.setFillColor(240, 240, 240);
                                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');
                                // Linha sutil acima do rodapé
                                doc.setDrawColor(200, 200, 200);
                                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);
                                // Adiciona numeração de páginas no rodapé
                                doc.setFontSize(8);
                                doc.setTextColor(100, 100, 100);
                                doc.text(`Página ${data.pageNumber} de ${data.pageCount}`, pageWidth - margin - 2, pageHeight - 5, {
                                    align: "right"
                                });
                                // Adiciona nome do sistema no rodapé
                                doc.setTextColor(80, 80, 80);
                                doc.setFontSize(8);
                                doc.text("High Tide Systems", margin + 2, pageHeight - 5);
                            }
                        });
                        // Atualiza a posição Y para a próxima tabela
                        // Obtém a última posição Y após desenhar a tabela
                        yPosition = doc.lastAutoTable.finalY + 15;
                    }
                    // Salva o documento
                    doc.save(`${filename}.pdf`);
                    return true;
                } else if (format === "image") {
                    try {
                        // Criar um elemento temporário para renderizar todas as tabelas
                        const tempContainer = document.createElement('div');
                        tempContainer.style.position = 'absolute';
                        tempContainer.style.left = '-9999px';
                        tempContainer.style.top = '-9999px';
                        tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade
                        tempContainer.style.backgroundColor = '#ffffff';
                        tempContainer.style.padding = '20px';
                        tempContainer.style.fontFamily = 'Arial, sans-serif';
                        tempContainer.style.color = '#333333';
                        tempContainer.style.boxSizing = 'border-box';
                        // Adicionar título principal e subtítulo
                        if (title) {
                            const titleElement = document.createElement('h1');
                            titleElement.textContent = title;
                            titleElement.style.color = '#FF7F00'; // Cor laranja do tema
                            titleElement.style.marginBottom = '5px';
                            titleElement.style.fontSize = '28px';
                            titleElement.style.fontWeight = 'bold';
                            titleElement.style.textAlign = 'center';
                            tempContainer.appendChild(titleElement);
                        }
                        if (subtitle) {
                            const subtitleElement = document.createElement('p');
                            subtitleElement.textContent = subtitle;
                            subtitleElement.style.color = '#666666';
                            subtitleElement.style.marginBottom = '20px';
                            subtitleElement.style.fontSize = '16px';
                            subtitleElement.style.textAlign = 'center';
                            tempContainer.appendChild(subtitleElement);
                        }
                        // Data atual formatada
                        const currentDate = new Date();
                        const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
                        const dateElement = document.createElement('p');
                        dateElement.textContent = `Gerado em: ${formattedDate}`;
                        dateElement.style.color = '#666666';
                        dateElement.style.marginBottom = '30px';
                        dateElement.style.fontSize = '12px';
                        dateElement.style.textAlign = 'center';
                        tempContainer.appendChild(dateElement);
                        // Processar cada tabela
                        for (const table of tables){
                            const tableData = data[table.name];
                            if (!tableData || !Array.isArray(tableData)) {
                                console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
                                continue;
                            }
                            // Adicionar título da seção
                            const sectionTitle = document.createElement('h2');
                            sectionTitle.textContent = table.title || table.name;
                            sectionTitle.style.color = '#FF7F00'; // Cor laranja do tema
                            sectionTitle.style.marginTop = '30px';
                            sectionTitle.style.marginBottom = '15px';
                            sectionTitle.style.fontSize = '20px';
                            sectionTitle.style.fontWeight = 'bold';
                            tempContainer.appendChild(sectionTitle);
                            // Criar a tabela
                            const tableElement = document.createElement('table');
                            tableElement.style.width = '100%';
                            tableElement.style.borderCollapse = 'collapse';
                            tableElement.style.marginBottom = '30px';
                            tableElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                            // Criar o cabeçalho da tabela
                            const thead = document.createElement('thead');
                            const headerRow = document.createElement('tr');
                            headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema
                            headerRow.style.color = '#ffffff';
                            table.columns.forEach((col)=>{
                                const th = document.createElement('th');
                                th.textContent = col.header || col.key;
                                th.style.padding = '10px';
                                th.style.textAlign = col.align || 'left';
                                th.style.fontWeight = 'bold';
                                th.style.fontSize = '14px';
                                th.style.borderBottom = '2px solid #FF9933';
                                headerRow.appendChild(th);
                            });
                            thead.appendChild(headerRow);
                            tableElement.appendChild(thead);
                            // Criar o corpo da tabela
                            const tbody = document.createElement('tbody');
                            tableData.forEach((item, rowIndex)=>{
                                const row = document.createElement('tr');
                                row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                                row.style.borderBottom = '1px solid #eeeeee';
                                table.columns.forEach((col)=>{
                                    const td = document.createElement('td');
                                    // Formatar o valor da célula
                                    let value = item[col.key];
                                    // Aplica formatação personalizada se especificada
                                    if (col.format && typeof col.format === 'function') {
                                        value = col.format(value, item);
                                    } else if (col.type && formatters[col.type]) {
                                        value = formatters[col.type](value, {});
                                    }
                                    td.textContent = value !== undefined && value !== null ? value : '';
                                    td.style.padding = '8px 10px';
                                    td.style.fontSize = '13px';
                                    td.style.textAlign = col.align || 'left';
                                    // Estilização especial para status
                                    if (col.key === 'active' || col.key === 'status') {
                                        if (td.textContent === 'Ativo') {
                                            td.style.color = '#10B981'; // Verde para ativo
                                            td.style.fontWeight = 'bold';
                                        } else if (td.textContent === 'Inativo') {
                                            td.style.color = '#DC2626'; // Vermelho para inativo
                                            td.style.fontWeight = 'bold';
                                        } else if (td.textContent === 'Crítica') {
                                            td.style.color = '#DC2626'; // Vermelho para crítica
                                            td.style.fontWeight = 'bold';
                                        } else if (td.textContent === 'Alta') {
                                            td.style.color = '#F59E0B'; // Âmbar para alta
                                            td.style.fontWeight = 'bold';
                                        } else if (td.textContent === 'Média') {
                                            td.style.color = '#10B981'; // Verde para média
                                            td.style.fontWeight = 'bold';
                                        } else if (td.textContent === 'Baixa') {
                                            td.style.color = '#3B82F6'; // Azul para baixa
                                            td.style.fontWeight = 'bold';
                                        }
                                    }
                                    row.appendChild(td);
                                });
                                tbody.appendChild(row);
                            });
                            tableElement.appendChild(tbody);
                            tempContainer.appendChild(tableElement);
                        }
                        // Adicionar rodapé com data de geração
                        const footer = document.createElement('div');
                        footer.style.fontSize = '12px';
                        footer.style.color = '#666666';
                        footer.style.textAlign = 'right';
                        footer.style.marginTop = '20px';
                        footer.style.borderTop = '1px solid #eeeeee';
                        footer.style.paddingTop = '10px';
                        footer.textContent = `High Tide Systems`;
                        tempContainer.appendChild(footer);
                        // Adicionar o container temporário ao DOM
                        document.body.appendChild(tempContainer);
                        // Usar html2canvas para converter a tabela em uma imagem
                        return new Promise((resolve)=>{
                            // Adicionar um pequeno atraso para garantir que o DOM esteja pronto
                            setTimeout(()=>{
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html2canvas$2f$dist$2f$html2canvas$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(tempContainer, {
                                    scale: 2,
                                    useCORS: true,
                                    allowTaint: true,
                                    backgroundColor: '#ffffff',
                                    logging: false,
                                    letterRendering: true
                                }).then((canvas)=>{
                                    // Remover o container temporário
                                    document.body.removeChild(tempContainer);
                                    // Converter o canvas para blob e salvar
                                    canvas.toBlob((blob)=>{
                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.png`);
                                        resolve(true);
                                    }, 'image/png');
                                }).catch((error)=>{
                                    console.error("Erro ao converter tabelas para imagem:", error);
                                    // Remover o container temporário em caso de erro
                                    if (document.body.contains(tempContainer)) {
                                        document.body.removeChild(tempContainer);
                                    }
                                    resolve(false);
                                });
                            }, 100); // Pequeno atraso para garantir que o DOM esteja pronto
                        });
                    } catch (error) {
                        console.error("Erro na exportação para imagem:", error);
                        return false;
                    }
                } else {
                    console.error("Formato não suportado para exportação de múltiplas tabelas");
                    return false;
                }
            } else {
                // Caso de tabela única (array de objetos)
                // Se não forem fornecidas colunas, usa as chaves dos objetos
                const tableColumns = columns.length > 0 ? columns : data.length > 0 ? Object.keys(data[0]).map((key)=>({
                        key,
                        header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
                        format: null
                    })) : [];
                // Formata os dados com base nas colunas definidas
                const formattedData = data.map((item)=>{
                    const formattedItem = {};
                    tableColumns.forEach((col)=>{
                        let value = item[col.key];
                        // Aplica formatação personalizada se especificada
                        if (col.format && typeof col.format === 'function') {
                            formattedItem[col.key] = col.format(value, item);
                        } else if (col.type && formatters[col.type]) {
                            formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
                        } else {
                            formattedItem[col.key] = value !== null && value !== undefined ? value : '';
                        }
                    });
                    return formattedItem;
                });
                // Exportação em formato XLSX
                if (format === "xlsx") {
                    return exportExcel(formattedData, tableColumns, filename, title);
                } else if (format === "pdf") {
                    return exportPdf(formattedData, tableColumns, filename, title, subtitle);
                } else if (format === "image") {
                    // Para exportação de imagem, precisamos de um elemento DOM
                    // Vamos criar uma tabela temporária e convertê-la em imagem
                    return exportImage(formattedData, tableColumns, filename, title, subtitle);
                } else {
                    console.error("Formato de exportação não suportado");
                    return false;
                }
            }
        } catch (error) {
            console.error("Erro na exportação:", error);
            return false;
        }
    },
    /**
   * Exporta dados da API com os filtros atuais
   * @param {string} endpoint - Endpoint da API
   * @param {Object} filters - Filtros a serem aplicados
   * @param {Object} options - Opções de exportação
   * @returns {Promise<boolean>} - Sucesso da exportação
   */ exportFromApi: async (endpoint, filters = {}, options = {})=>{
        try {
            const { format = "xlsx", filename = "export", columns = [] } = options;
            // Se a API suporta exportação direta
            if (options.useApiExport) {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`${endpoint}/export`, {
                    params: {
                        ...filters,
                        format
                    },
                    responseType: format === "pdf" ? "arraybuffer" : "blob"
                });
                const blob = new Blob([
                    response.data
                ], {
                    type: format === "pdf" ? "application/pdf" : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.${format}`);
                return true;
            }
            // Se a API não suporta exportação, faz uma consulta normal e exporta os dados no cliente
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(endpoint, {
                params: filters
            });
            // Tenta obter os dados de diferentes formatos de resposta
            const data = response.data?.data || response.data?.items || response.data || [];
            // Exporta os dados
            return exportService.exportData(data, {
                ...options,
                format,
                filename,
                columns
            });
        } catch (error) {
            console.error("Erro ao exportar dados da API:", error);
            return false;
        }
    }
};
// Funções auxiliares de exportação
/**
 * Exporta dados para Excel com formatação aprimorada
 */ function exportExcel(data, columns, filename, title) {
    try {
        // Cria um novo workbook
        const workbook = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_new();
        // Se tiver título, adiciona como primeira linha
        let worksheetData = [];
        // Data atual formatada
        const currentDate = new Date();
        const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
        // Adiciona título e data
        if (title) {
            worksheetData.push([
                title
            ]);
            worksheetData.push([
                `Gerado em: ${formattedDate}`
            ]);
            worksheetData.push([]); // Linha em branco
        }
        // Adiciona os cabeçalhos
        const headers = columns.map((col)=>col.header || col.key);
        worksheetData.push(headers);
        // Adiciona os dados
        data.forEach((item)=>{
            const row = columns.map((col)=>item[col.key]);
            worksheetData.push(row);
        });
        // Cria a worksheet
        const worksheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].aoa_to_sheet(worksheetData);
        // Configura os estilos (apenas as propriedades básicas são suportadas no xlsx)
        if (title) {
            // Mescla células para o título
            worksheet['!merges'] = [
                {
                    s: {
                        r: 0,
                        c: 0
                    },
                    e: {
                        r: 0,
                        c: headers.length - 1
                    }
                },
                {
                    s: {
                        r: 1,
                        c: 0
                    },
                    e: {
                        r: 1,
                        c: headers.length - 1
                    }
                }
            ];
            // Ajusta largura das colunas
            if (!worksheet['!cols']) worksheet['!cols'] = [];
            columns.forEach((col, idx)=>{
                // Calcula largura ideal para cada coluna
                const headerWidth = (col.header || col.key).length * 1.2;
                let maxDataWidth = 0;
                // Verifica o tamanho máximo dos dados em cada coluna
                data.forEach((item)=>{
                    const cellValue = item[col.key];
                    const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';
                    maxDataWidth = Math.max(maxDataWidth, cellText.length);
                });
                worksheet['!cols'][idx] = {
                    wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))
                };
            });
        }
        // Adiciona a worksheet ao workbook
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].book_append_sheet(workbook, worksheet, "Dados");
        // Converte para binário e salva
        const excelBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$xlsx$2f$xlsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["write"])(workbook, {
            bookType: "xlsx",
            type: "array",
            bookSST: false,
            compression: true
        });
        const blob = new Blob([
            excelBuffer
        ], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.xlsx`);
        return true;
    } catch (error) {
        console.error("Erro na exportação Excel:", error);
        return false;
    }
}
/**
 * Exporta dados para PDF com design aprimorado
 */ function exportPdf(data, columns, filename, title, subtitle) {
    try {
        // Cria um novo documento PDF no tamanho A4
        const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsPDF"]({
            orientation: "portrait",
            unit: "mm",
            format: "a4"
        });
        // Configurações da página
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 10;
        const contentWidth = pageWidth - margin * 2;
        // Define cores do tema baseadas no estilo do módulo people (laranja)
        const themeColors = {
            primary: {
                light: [
                    255,
                    153,
                    51
                ],
                dark: [
                    255,
                    127,
                    0
                ],
                text: [
                    255,
                    255,
                    255
                ] // #FFFFFF (texto branco)
            },
            secondary: {
                light: [
                    255,
                    237,
                    213
                ],
                dark: [
                    154,
                    52,
                    18
                ],
                border: [
                    251,
                    146,
                    60
                ] // #FB923C (orange-400)
            }
        };
        // ===== CABEÇALHO COM GRADIENTE =====
        // Desenha um retângulo para o cabeçalho com gradiente
        const headerHeight = subtitle ? 30 : 25;
        // Simulando um gradiente com múltiplos retângulos coloridos
        const gradientSteps = 20;
        const stepHeight = headerHeight / gradientSteps;
        for(let i = 0; i < gradientSteps; i++){
            const ratio = i / gradientSteps;
            // Interpola as cores para criar efeito de gradiente
            const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);
            const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);
            const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);
            doc.setFillColor(r, g, b);
            doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');
        }
        // Adiciona um pequeno ícone ou logotipo
        doc.setDrawColor(255, 255, 255);
        doc.setFillColor(255, 255, 255);
        // Adiciona título no cabeçalho
        if (title) {
            doc.setFont("helvetica", "bold");
            doc.setFontSize(18);
            doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho
            doc.text(title, pageWidth / 2, 15, {
                align: "center"
            });
        }
        // Adiciona subtítulo no cabeçalho se existir
        if (subtitle) {
            doc.setFont("helvetica", "normal");
            doc.setFontSize(10);
            doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência
            doc.text(subtitle, pageWidth / 2, 22, {
                align: "center"
            });
        }
        // Data de geração do relatório
        const currentDate = new Date();
        const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
        doc.setFontSize(8);
        doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, {
            align: "right"
        });
        // ===== CONTEÚDO DA TABELA =====
        // Prepara os dados para a tabela
        const headers = columns.map((col)=>col.header || col.key);
        const rows = data.map((item)=>{
            return columns.map((col)=>item[col.key]);
        });
        // Prepara cabeçalhos mais curtos para prevenir quebras
        const shortHeaders = headers.map((header)=>{
            // Substituições específicas para cabeçalhos problemáticos
            const replacements = {
                'Nome Completo': 'Nome',
                'Data de Nascimento': 'Nascimento',
                'Data de Cadastro': 'Cadastro',
                'Relacionamento': 'Relação',
                'Telefone': 'Telefone'
            };
            return replacements[header] || header;
        });
        // Adiciona a tabela com estilo aprimorado
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2d$autotable$2f$dist$2f$jspdf$2e$plugin$2e$autotable$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(doc, {
            startY: headerHeight + 5,
            head: [
                shortHeaders
            ],
            body: rows,
            theme: "grid",
            headStyles: {
                fillColor: themeColors.primary.dark,
                textColor: themeColors.primary.text,
                fontStyle: "bold",
                halign: "center",
                fontSize: 9,
                cellPadding: {
                    top: 3,
                    right: 2,
                    bottom: 3,
                    left: 2
                },
                lineWidth: 0.1,
                minCellWidth: 15,
                overflow: 'linebreak' // Evita quebra de linha nos cabeçalhos
            },
            styles: {
                fontSize: 9,
                cellPadding: {
                    top: 2,
                    right: 2,
                    bottom: 2,
                    left: 2
                },
                overflow: "linebreak",
                lineColor: [
                    220,
                    220,
                    220
                ],
                lineWidth: 0.1
            },
            tableWidth: 'auto',
            bodyStyles: {
                minCellHeight: 10
            },
            alternateRowStyles: {
                fillColor: [
                    252,
                    252,
                    252
                ]
            },
            columnStyles: columns.reduce((styles, col, index)=>{
                // Definir larguras mínimas específicas para evitar quebras nos títulos
                const minWidths = {
                    'fullName': 30,
                    'cpf': 20,
                    'email': 28,
                    'phone': 20,
                    'birthDate': 20,
                    'gender': 15,
                    'relationship': 20,
                    'createdAt': 20,
                    'active': 15
                };
                // Define a largura baseada na configuração ou no mínimo predefinido
                if (col.key && minWidths[col.key]) {
                    styles[index] = {
                        ...styles[index],
                        cellWidth: minWidths[col.key],
                        overflow: 'linebreak'
                    };
                } else if (col.width) {
                    styles[index] = {
                        ...styles[index],
                        cellWidth: col.width
                    };
                }
                // Aplica o alinhamento se definido
                if (col.align) {
                    styles[index] = {
                        ...styles[index],
                        halign: col.align
                    };
                }
                return styles;
            }, {}),
            margin: {
                top: headerHeight + 5,
                left: margin,
                right: margin,
                bottom: margin + 15
            },
            didDrawPage: function(data) {
                // Adiciona rodapé colorido em cada página
                doc.setFillColor(240, 240, 240);
                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');
                // Linha sutil acima do rodapé
                doc.setDrawColor(200, 200, 200);
                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);
                // Adiciona numeração de páginas no rodapé
                doc.setFontSize(8);
                doc.setTextColor(100, 100, 100);
                doc.text(`Página ${data.pageNumber} de ${data.pageCount}`, pageWidth - margin - 2, pageHeight - 5, {
                    align: "right"
                });
                // Adiciona nome do sistema no rodapé
                doc.setTextColor(80, 80, 80);
                doc.setFontSize(8);
                doc.text("High Tide Systems", margin + 2, pageHeight - 5);
            },
            didParseCell: function(data) {
                // Aplicar formatação específica para células com status
                const col = columns[data.column.index];
                if (col && col.key === 'active') {
                    if (data.cell.section === 'body') {
                        if (data.cell.raw === 'Ativo') {
                            data.cell.styles.fontStyle = 'bold';
                            data.cell.styles.textColor = [
                                16,
                                185,
                                129
                            ]; // Cor verde para Ativo
                        } else if (data.cell.raw === 'Inativo') {
                            data.cell.styles.fontStyle = 'bold';
                            data.cell.styles.textColor = [
                                220,
                                38,
                                38
                            ]; // Cor vermelha para Inativo
                        }
                    }
                }
            }
        });
        // Salva o documento
        doc.save(`${filename}.pdf`);
        return true;
    } catch (error) {
        console.error("Erro na exportação PDF:", error);
        return false;
    }
}
/**
 * Exporta dados para imagem (PNG) criando uma tabela HTML temporária
 */ function exportImage(data, columns, filename, title, subtitle) {
    try {
        // Criar um elemento temporário para renderizar a tabela
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade
        tempContainer.style.backgroundColor = '#ffffff';
        tempContainer.style.padding = '20px';
        tempContainer.style.fontFamily = 'Arial, sans-serif';
        tempContainer.style.color = '#333333';
        tempContainer.style.boxSizing = 'border-box';
        // Adicionar título e subtítulo
        if (title) {
            const titleElement = document.createElement('h2');
            titleElement.textContent = title;
            titleElement.style.color = '#FF7F00'; // Cor laranja do tema
            titleElement.style.marginBottom = '5px';
            titleElement.style.fontSize = '24px';
            titleElement.style.fontWeight = 'bold';
            titleElement.style.textAlign = 'center';
            tempContainer.appendChild(titleElement);
        }
        if (subtitle) {
            const subtitleElement = document.createElement('p');
            subtitleElement.textContent = subtitle;
            subtitleElement.style.color = '#666666';
            subtitleElement.style.marginBottom = '20px';
            subtitleElement.style.fontSize = '14px';
            subtitleElement.style.textAlign = 'center';
            tempContainer.appendChild(subtitleElement);
        }
        // Criar a tabela
        const table = document.createElement('table');
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';
        table.style.marginBottom = '20px';
        table.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
        // Criar o cabeçalho da tabela
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema
        headerRow.style.color = '#ffffff';
        columns.forEach((col)=>{
            const th = document.createElement('th');
            th.textContent = col.header || col.key;
            th.style.padding = '10px';
            th.style.textAlign = 'left';
            th.style.fontWeight = 'bold';
            th.style.fontSize = '14px';
            th.style.borderBottom = '2px solid #FF9933';
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);
        // Criar o corpo da tabela
        const tbody = document.createElement('tbody');
        data.forEach((item, rowIndex)=>{
            const row = document.createElement('tr');
            row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
            row.style.borderBottom = '1px solid #eeeeee';
            columns.forEach((col)=>{
                const td = document.createElement('td');
                td.textContent = item[col.key] !== undefined && item[col.key] !== null ? item[col.key] : '';
                td.style.padding = '8px 10px';
                td.style.fontSize = '13px';
                // Estilização especial para status
                if (col.key === 'active') {
                    if (item[col.key] === 'Ativo') {
                        td.style.color = '#10B981'; // Verde para ativo
                        td.style.fontWeight = 'bold';
                    } else if (item[col.key] === 'Inativo') {
                        td.style.color = '#DC2626'; // Vermelho para inativo
                        td.style.fontWeight = 'bold';
                    }
                }
                row.appendChild(td);
            });
            tbody.appendChild(row);
        });
        table.appendChild(tbody);
        tempContainer.appendChild(table);
        // Adicionar rodapé com data de geração
        const footer = document.createElement('div');
        footer.style.fontSize = '12px';
        footer.style.color = '#666666';
        footer.style.textAlign = 'right';
        footer.style.marginTop = '10px';
        const currentDate = new Date();
        const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
        footer.textContent = `Gerado em: ${formattedDate} | High Tide Systems`;
        tempContainer.appendChild(footer);
        // Adicionar o container temporário ao DOM
        document.body.appendChild(tempContainer);
        // Usar html2canvas para converter a tabela em uma imagem
        return new Promise((resolve)=>{
            // Adicionar um pequeno atraso para garantir que o DOM esteja pronto
            setTimeout(()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html2canvas$2f$dist$2f$html2canvas$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(tempContainer, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    letterRendering: true
                }).then((canvas)=>{
                    // Remover o container temporário
                    document.body.removeChild(tempContainer);
                    // Converter o canvas para blob e salvar
                    canvas.toBlob((blob)=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$file$2d$saver$2f$dist$2f$FileSaver$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveAs"])(blob, `${filename}.png`);
                        resolve(true);
                    }, 'image/png');
                }).catch((error)=>{
                    console.error("Erro ao converter tabela para imagem:", error);
                    // Remover o container temporário em caso de erro
                    if (document.body.contains(tempContainer)) {
                        document.body.removeChild(tempContainer);
                    }
                    resolve(false);
                });
            }, 100); // Pequeno atraso para garantir que o DOM esteja pronto
        });
    } catch (error) {
        console.error("Erro na exportação para imagem:", error);
        return false;
    }
}
}}),
"[project]/src/utils/apiResponseAdapter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Adaptador para processar respostas da API de forma consistente
 * Este utilitário garante que as respostas da API sejam processadas de forma padronizada,
 * independentemente do formato retornado pelo backend
 */ /**
 * Extrai os dados de uma resposta da API, lidando com diferentes formatos possíveis
 * @param {Object} response - A resposta da API
 * @param {string} entityName - O nome da entidade principal (ex: 'persons', 'clients', 'appointments')
 * @param {string[]} alternativeNames - Nomes alternativos para a entidade (ex: ['people'] para 'persons')
 * @returns {Object} Dados normalizados com propriedades padronizadas
 */ __turbopack_esm__({
    "extractData": (()=>extractData),
    "extractEntity": (()=>extractEntity),
    "extractErrorMessage": (()=>extractErrorMessage),
    "processAppointments": (()=>processAppointments)
});
const extractData = (response, entityName, alternativeNames = [])=>{
    console.log(`extractData chamado para entidade '${entityName}' com alternativas:`, alternativeNames);
    console.log("Resposta recebida:", response);
    // Se a resposta for nula ou indefinida, retornar objeto vazio
    if (!response) {
        console.log("Resposta nula ou indefinida, retornando objeto vazio");
        return {
            [entityName]: [],
            total: 0,
            pages: 1
        };
    }
    // Se a resposta já estiver no novo formato padronizado (com campo 'data')
    if (response.success && response.data) {
        console.log("Resposta no formato padronizado com campo 'data'");
        response = response.data; // Use o campo 'data' como a resposta real
    }
    // Extrair a lista de entidades
    let entities = [];
    // Verificar se a resposta é um array
    if (Array.isArray(response)) {
        console.log("Resposta é um array");
        entities = response;
    } else if (response[entityName] && Array.isArray(response[entityName])) {
        console.log(`Entidade encontrada no campo esperado '${entityName}'`);
        entities = response[entityName];
    } else {
        for (const name of alternativeNames){
            if (response[name] && Array.isArray(response[name])) {
                console.log(`Entidade encontrada no campo alternativo '${name}'`);
                entities = response[name];
                break;
            }
        }
        // Se ainda não encontrou, verificar campo 'data' genérico
        if (entities.length === 0 && response.data && Array.isArray(response.data)) {
            console.log("Entidade encontrada no campo genérico 'data'");
            entities = response.data;
        }
    }
    console.log(`Entidades extraídas (${entities.length}):`, entities.slice(0, 2));
    // Extrair metadados de paginação
    const total = response.total || entities.length || 0;
    const pages = response.pages || Math.ceil(total / 10) || 1;
    console.log("Metadados de paginação:", {
        total,
        pages
    });
    // Retornar objeto com formato padronizado
    const result = {
        [entityName]: entities,
        total,
        pages
    };
    console.log("Resultado final do extractData:", result);
    return result;
};
const extractEntity = (response)=>{
    if (!response) {
        return null;
    }
    // Se a resposta já estiver no novo formato padronizado (com campo 'data')
    if (response.success && response.data) {
        return response.data;
    }
    return response;
};
const extractErrorMessage = (error)=>{
    // Verificar se é um erro de resposta da API
    if (error.response) {
        // Novo formato padronizado
        if (error.response.data && error.response.data.error) {
            return error.response.data.error.message;
        }
        // Formato antigo
        if (error.response.data && error.response.data.message) {
            return error.response.data.message;
        }
        // Mensagem genérica baseada no status HTTP
        if (error.response.status === 404) {
            return 'Recurso não encontrado';
        }
        if (error.response.status === 401) {
            return 'Não autorizado. Faça login novamente.';
        }
        if (error.response.status === 403) {
            return 'Você não tem permissão para acessar este recurso';
        }
        if (error.response.status >= 500) {
            return 'Erro interno do servidor. Tente novamente mais tarde.';
        }
    }
    // Erro de rede ou outro erro
    return error.message || 'Ocorreu um erro desconhecido';
};
const processAppointments = (appointments)=>{
    if (!appointments || !Array.isArray(appointments)) {
        return [];
    }
    return appointments.map((appointment)=>{
        // Extrair informações do provedor
        const provider = appointment.provider || {};
        const providerProfession = provider.profession || (provider.professionObj ? provider.professionObj.name : null);
        // Extrair informações da pessoa/paciente
        const person = appointment.Person && appointment.Person.length > 0 ? appointment.Person[0] : appointment.person || {};
        // Log detalhado para depuração
        if (("TURBOPACK compile-time value", "development") !== 'production') {
        // console.log("[API-ADAPTER] Processando agendamento:", {
        //   id: appointment.id,
        //   personId: appointment.personId,
        //   clientId: appointment.clientId,
        //   Person: appointment.Person,
        //   person: appointment.person,
        //   extractedPerson: person
        // });
        }
        // Extrair informações do tipo de serviço
        const serviceType = appointment.serviceType || {};
        // Extrair informações do local
        const location = appointment.location || {};
        // Extrair o personId de todas as fontes possíveis
        const personId = person.id || appointment.personId || appointment.clientId || "";
        // Log detalhado para depuração
        if (("TURBOPACK compile-time value", "development") !== 'production') {
        // console.log("[API-ADAPTER] Fontes de personId:", {
        //   fromPerson: person.id,
        //   fromPersonId: appointment.personId,
        //   fromClientId: appointment.clientId,
        //   final: personId
        // });
        }
        // Retornar objeto processado
        return {
            ...appointment,
            // Adicionar campos processados
            providerfullName: provider.fullName || 'Profissional não especificado',
            providerProfession: providerProfession || 'Sem profissão',
            personfullName: person.fullName || 'Paciente não especificado',
            serviceTypefullName: serviceType.name || 'Serviço não especificado',
            locationName: location.name || 'Local não especificado',
            // Garantir que o personId seja definido corretamente
            personId: personId,
            // Adicionar objetos completos para uso no modal
            person: person
        };
    });
};
}}),
"[project]/src/app/modules/people/services/personsService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// src/app/modules/people/services/personsService.js
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "personsService": (()=>personsService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/services/exportService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/apiResponseAdapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * Formata o gênero para exibição
 * @param {string} gender - Código do gênero (M, F, O, etc)
 * @returns {string} - Gênero formatado
 */ function formatGender(gender) {
    const genderMap = {
        M: "Masculino",
        F: "Feminino",
        O: "Outro"
    };
    return genderMap[gender] || gender || "Não informado";
}
const personsService = {
    // Get persons with optional filters
    getPersons: async (filters = {})=>{
        const { page = 1, limit = 10, search = "", personIds, active, clientId, relationship, companyId, sortField = 'fullName', sortDirection = 'asc' // Default sort direction
         } = filters;
        try {
            console.log("getPersons chamado com filtros:", filters);
            // Construir parâmetros para a API
            const params = {
                page,
                limit,
                search: search || undefined,
                active: active === undefined ? undefined : active,
                clientId: clientId || undefined,
                relationship: relationship || undefined,
                companyId: companyId || undefined,
                sortField,
                sortDirection
            };
            // Adicionar personIds como parâmetros separados com notação de array
            if (personIds && personIds.length > 0) {
                // Garantir que personIds seja um array
                const personIdsArray = Array.isArray(personIds) ? personIds : [
                    personIds
                ];
                // Adicionar cada ID como um parâmetro separado
                personIdsArray.forEach((id, index)=>{
                    // Usar a notação de array para compatibilidade com a API
                    params[`personIds[${index}]`] = id;
                });
                console.log("Filtrando por múltiplos IDs de pacientes:", personIdsArray);
            }
            console.log("Enviando requisição para /persons com params:", params);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get("/persons", {
                params
            });
            console.log("Resposta bruta da API de persons:", response.data);
            // Usar o adaptador para extrair os dados de forma consistente
            const extractedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractData"])(response.data, 'persons', [
                'people'
            ]);
            console.log("Dados extraídos pelo adaptador:", extractedData);
            return extractedData;
        } catch (error) {
            console.error("Error fetching persons:", error);
            throw error;
        }
    },
    // Get a single person by ID
    getPerson: async (id)=>{
        try {
            console.log(`Buscando dados da pessoa ${id}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/persons/${id}`);
            console.log('Dados da pessoa recebidos:', response.data);
            console.log('URL da imagem de perfil:', response.data.profileImageFullUrl);
            // Usar o adaptador para extrair a entidade
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractEntity"])(response.data);
        } catch (error) {
            console.error(`Error fetching person ${id}:`, error);
            throw error;
        }
    },
    // Create a new person
    createPerson: async (personData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post("/persons", personData);
            return response.data;
        } catch (error) {
            console.error("Error creating person:", error);
            throw error;
        }
    },
    // Update an existing person
    updatePerson: async (id, personData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/persons/${id}`, personData);
            return response.data;
        } catch (error) {
            console.error(`Error updating person ${id}:`, error);
            throw error;
        }
    },
    // Toggle person active status
    togglePersonStatus: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].patch(`/persons/${id}/status`);
            return response.data;
        } catch (error) {
            console.error(`Error toggling status for person ${id}:`, error);
            throw error;
        }
    },
    // Delete a person (soft delete)
    deletePerson: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/persons/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting person ${id}:`, error);
            throw error;
        }
    },
    // Upload profile image for a person
    uploadProfileImage: async (id, imageFile)=>{
        try {
            console.log(`Iniciando upload de imagem para pessoa ${id}`);
            console.log('Arquivo:', imageFile.name, imageFile.type, imageFile.size);
            const formData = new FormData();
            formData.append('profileImage', imageFile);
            console.log('FormData criado com sucesso');
            // Verificar o token de autenticação
            const token = localStorage.getItem('token');
            console.log('Token de autenticação:', token ? 'Presente' : 'Ausente');
            console.log(`Enviando requisição POST para /persons/${id}/profile-image`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post(`/persons/${id}/profile-image`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`
                }
            });
            console.log('Resposta recebida:', response.status, response.statusText);
            console.log('Dados da resposta:', response.data);
            // Verificar se a resposta contém a URL da imagem
            if (response.data && response.data.fullImageUrl) {
                console.log('URL da imagem recebida:', response.data.fullImageUrl);
            } else {
                console.warn('Resposta não contém a URL da imagem');
            }
            return response.data;
        } catch (error) {
            console.error(`Error uploading profile image for person ${id}:`, error);
            console.error('Detalhes do erro:', error.response?.data || error.message);
            throw error;
        }
    },
    // Get profile image URL for a person
    getProfileImageUrl: (id, profileImageUrl)=>{
        if (!id) return null;
        // Se tiver a URL completa da imagem, usar ela diretamente
        if (profileImageUrl) {
            return `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].defaults.baseURL}/uploads/${profileImageUrl}`;
        }
        // Caso contrário, usar a rota da API
        return `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].defaults.baseURL}/api/persons/${id}/profile-image`;
    },
    // Get all clients for dropdown selection
    getClientsForSelect: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get("/clients", {
                params: {
                    active: true,
                    limit: 100
                }
            });
            return response.data.clients.map((client)=>({
                    value: client.id,
                    label: client.login
                }));
        } catch (error) {
            console.error("Error fetching clients for select:", error);
            return [];
        }
    },
    // Get insurances for person
    getPersonInsurances: async (personId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/persons/${personId}/insurances`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching insurances for person ${personId}:`, error);
            return [];
        }
    },
    // Upload document for a person
    uploadDocument: async (personId, file, documentType)=>{
        try {
            const formData = new FormData();
            formData.append('documents', file);
            formData.append('types', JSON.stringify([
                documentType
            ]));
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Error uploading document for person ${personId}:`, error);
            throw error;
        }
    },
    // Create contact for a person
    createContact: async (personId, contactData)=>{
        try {
            const payload = {
                personId: personId,
                name: contactData.name,
                relationship: contactData.relationship || null,
                email: contactData.email || null,
                phone: contactData.phone ? contactData.phone.replace(/\D/g, "") : null,
                notes: contactData.notes || null
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/contacts', payload);
            return response.data;
        } catch (error) {
            console.error(`Error creating contact for person ${personId}:`, error);
            throw error;
        }
    },
    // Add insurance to a person
    addPersonInsurance: async (personId, insuranceData)=>{
        try {
            const payload = {
                personId: personId,
                insuranceId: insuranceData.insuranceId,
                policyNumber: insuranceData.policyNumber || null,
                validUntil: insuranceData.validUntil || null,
                notes: insuranceData.notes || null
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/person-insurances', payload);
            return response.data;
        } catch (error) {
            console.error(`Error adding insurance for person ${personId}:`, error);
            throw error;
        }
    },
    /**
   * Exporta a lista de pessoas com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, status, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */ exportPersons: async (filters, exportFormat = "xlsx")=>{
        try {
            // Obter os dados filtrados da API
            const response = await personsService.getPersons({
                ...filters,
                limit: 1000
            });
            // Extrair os dados das pessoas usando o adaptador
            const { persons } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractData"])(response, 'persons', [
                'people'
            ]);
            const data = persons;
            // Timestamp atual para o subtítulo
            const timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "dd/MM/yyyy 'às' HH:mm", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ptBR"]
            });
            // Definição das colunas com formatação
            const columns = [
                {
                    key: "fullName",
                    header: "Nome Completo"
                },
                {
                    key: "cpf",
                    header: "CPF",
                    type: "cpf"
                },
                {
                    key: "email",
                    header: "Email"
                },
                {
                    key: "phone",
                    header: "Telefone",
                    type: "phone"
                },
                {
                    key: "birthDate",
                    header: "Data de Nascimento",
                    type: "date"
                },
                {
                    key: "active",
                    header: "Status",
                    format: (value)=>value ? "Ativo" : "Inativo",
                    align: "center",
                    width: 20
                },
                {
                    key: "gender",
                    header: "Gênero",
                    format: formatGender
                },
                {
                    key: "relationship",
                    header: "Relacionamento"
                },
                {
                    key: "createdAt",
                    header: "Data de Cadastro",
                    type: "date"
                }
            ];
            // Preparar os dados para exportação
            const preparedData = data.map((person)=>{
                // Para cada pessoa, montamos um objeto com todas as propriedades que queremos exportar
                return {
                    fullName: person.fullName || "",
                    cpf: person.cpf || "",
                    email: person.email || "",
                    phone: person.phone || "",
                    birthDate: person.birthDate || "",
                    active: person.active,
                    gender: person.gender || "",
                    relationship: person.relationship || "N/A",
                    createdAt: person.createdAt || ""
                };
            });
            // Filtros aplicados para subtítulo
            let subtitleParts = [];
            if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
            if (filters.personIds && filters.personIds.length > 0) {
                subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);
            }
            if (filters.active !== undefined) {
                subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
            }
            if (filters.relationship) {
                subtitleParts.push(`Tipo: ${filters.relationship}`);
            }
            if (filters.companyId) {
                subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
            }
            // Construir o subtítulo
            let subtitle = `Exportado em: ${timestamp}`;
            if (subtitleParts.length > 0) {
                subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
            }
            // Exportar os dados
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportService"].exportData(preparedData, {
                format: exportFormat,
                filename: "pacientes",
                columns,
                title: "Lista de Pacientes",
                subtitle
            });
        } catch (error) {
            console.error("Erro ao exportar pessoas:", error);
            return false;
        }
    }
};
const __TURBOPACK__default__export__ = personsService;
}}),
"[project]/src/app/dashboard/ClientHeader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$QuickNavContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/QuickNavContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeToggle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ThemeToggle.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$appConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/config/appConfig.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/personsService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-ssr] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-ssr] (ecmascript) <export default as LogOut>");
'use client';
;
;
;
;
;
;
;
;
;
// Header Component adaptado para clientes
const ClientHeader = ({ toggleSidebar, isSidebarOpen })=>{
    const { user, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { openQuickNav } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$QuickNavContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuickNav"])();
    // Pegar primeira letra de cada nome para o avatar
    const getInitials = ()=>{
        // Verificar se temos uma pessoa associada ao cliente
        if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {
            const fullName = user.persons[0].fullName;
            const names = fullName.split(' ');
            if (names.length === 1) return names[0].charAt(0);
            return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
        }
        // Fallback para o login do cliente
        return user?.login?.charAt(0) || 'C';
    };
    // Obter o nome completo da pessoa associada ao cliente ou o login do cliente
    const getDisplayName = ()=>{
        if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {
            return user.persons[0].fullName;
        }
        return user?.login || 'Cliente';
    };
    // Obter a URL da imagem de perfil da pessoa associada ao cliente
    const getProfileImage = ()=>{
        if (user?.persons && user.persons.length > 0) {
            // Primeiro tenta usar a URL completa se disponível
            if (user.persons[0].profileImageFullUrl) {
                return user.persons[0].profileImageFullUrl;
            } else if (user.persons[0].profileImageUrl) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["personsService"].getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);
            }
        }
        return null;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: toggleSidebar,
                        className: "p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors",
                        "aria-label": isSidebarOpen ? "Fechar menu lateral" : "Abrir menu lateral",
                        children: isSidebarOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 22,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                            lineNumber: 67,
                            columnNumber: 28
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                            size: 22,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                            lineNumber: 67,
                            columnNumber: 65
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 62,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    src: "/logo_horizontal_sem_fundo.png",
                                    alt: "High Tide Logo",
                                    className: "h-10 mr-2.5 dark:invert dark:text-white"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                    lineNumber: 72,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$appConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APP_VERSION"]
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                    lineNumber: 77,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: openQuickNav,
                        className: "flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors",
                        "aria-label": "Abrir pesquisa rápida",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                size: 18,
                                className: "text-gray-400 dark:text-gray-500",
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                lineNumber: 90,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "hidden sm:inline",
                                children: "Pesquisar..."
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Ctrl + K"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                    lineNumber: 93,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                lineNumber: 92,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 85,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/dashboard/scheduler/calendar'),
                        className: "p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",
                        "aria-label": "Calendário",
                        title: "Ver calendário",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                            size: 20,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                            lineNumber: 104,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),
                        className: "p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",
                        "aria-label": "Meus Agendamentos",
                        title: "Ver meus agendamentos",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                            size: 20,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                            lineNumber: 113,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 107,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeToggle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeToggle"], {}, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 117,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 border-l border-gray-200 dark:border-gray-700 mx-1",
                        "aria-hidden": "true"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 120,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative group",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                "aria-expanded": "false",
                                "aria-haspopup": "true",
                                "aria-label": "Menu do usuário",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden",
                                        children: getProfileImage() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: getProfileImage(),
                                            alt: `Foto de perfil de ${getDisplayName()}`,
                                            className: "h-10 w-10 rounded-full object-cover",
                                            onError: (e)=>{
                                                e.target.onerror = null;
                                                e.target.style.display = 'none';
                                                e.target.parentNode.innerHTML = getInitials();
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                            lineNumber: 132,
                                            columnNumber: 17
                                        }, this) : getInitials()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                        lineNumber: 130,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "hidden md:block text-left",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1",
                                                children: getDisplayName()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 148,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                        size: 10,
                                                        className: "mr-1",
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                        lineNumber: 150,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "Cliente"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                        lineNumber: 151,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 149,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                        size: 16,
                                        className: "text-gray-400 dark:text-gray-500 hidden md:block",
                                        "aria-hidden": "true"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right",
                                role: "menu",
                                "aria-orientation": "vertical",
                                "aria-labelledby": "user-menu-button",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-800 dark:text-gray-200",
                                                children: getDisplayName()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 164,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 dark:text-gray-400 truncate",
                                                children: user?.email || '<EMAIL>'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 165,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                        lineNumber: 163,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "pt-1 mt-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/dashboard/profile'),
                                                className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                                role: "menuitem",
                                                children: "Meu Perfil"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 174,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/dashboard/people/persons'),
                                                className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                                role: "menuitem",
                                                children: "Minhas Pessoas"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 181,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),
                                                className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                                                role: "menuitem",
                                                children: "Meus Agendamentos"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 188,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: logout,
                                                className: "w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors",
                                                role: "menuitem",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                        size: 14,
                                                        className: "mr-2",
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                        lineNumber: 200,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Sair do Sistema"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                                lineNumber: 195,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                        lineNumber: 173,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                                lineNumber: 159,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/ClientHeader.js",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/ClientHeader.js",
                lineNumber: 83,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/ClientHeader.js",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ClientHeader;
}}),
"[project]/src/utils/constructionUtils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "constructionMessages": (()=>constructionMessages),
    "getConstructionMessage": (()=>getConstructionMessage),
    "isUnderConstruction": (()=>isUnderConstruction),
    "underConstructionModules": (()=>underConstructionModules),
    "underConstructionSubmenus": (()=>underConstructionSubmenus)
});
"use client";
const underConstructionModules = [
    'financial',
    'hr'
];
const underConstructionSubmenus = [
    'admin.logs',
    'admin.backup',
    'financial.reports',
    'financial.invoices',
    'financial.payments',
    'financial.expenses',
    'hr.payroll',
    'hr.benefits',
    'hr.attendance',
    'scheduling.appointments-dashboard'
];
const constructionMessages = {
    'admin.logs': {
        title: 'Logs do Sistema em Construção',
        content: 'O módulo de logs do sistema está em desenvolvimento. Em breve você poderá visualizar todas as atividades realizadas no sistema.',
        icon: 'AlertTriangle'
    },
    'admin.backup': {
        title: 'Backup em Construção',
        content: 'O módulo de backup está em desenvolvimento. Em breve você poderá realizar backups e restaurações dos dados do sistema.',
        icon: 'Database'
    },
    'financial.reports': {
        title: 'Relatórios Financeiros em Construção',
        content: 'O módulo de relatórios financeiros está em desenvolvimento. Em breve você poderá gerar relatórios detalhados sobre as finanças da empresa.',
        icon: 'BarChart'
    },
    'financial.invoices': {
        title: 'Faturas em Construção',
        content: 'O módulo de faturas está em desenvolvimento. Em breve você poderá gerenciar todas as faturas da empresa.',
        icon: 'FileText'
    },
    'financial.payments': {
        title: 'Pagamentos em Construção',
        content: 'O módulo de pagamentos está em desenvolvimento. Em breve você poderá gerenciar todos os pagamentos da empresa.',
        icon: 'CreditCard'
    },
    'financial.expenses': {
        title: 'Despesas em Construção',
        content: 'O módulo de despesas está em desenvolvimento. Em breve você poderá gerenciar todas as despesas da empresa.',
        icon: 'DollarSign'
    },
    'hr.payroll': {
        title: 'Folha de Pagamento em Construção',
        content: 'O módulo de folha de pagamento está em desenvolvimento. Em breve você poderá gerenciar os salários e benefícios dos funcionários.',
        icon: 'Briefcase'
    },
    'hr.benefits': {
        title: 'Benefícios em Construção',
        content: 'O módulo de benefícios está em desenvolvimento. Em breve você poderá gerenciar os benefícios oferecidos aos funcionários.',
        icon: 'Gift'
    },
    'hr.attendance': {
        title: 'Presença em Construção',
        content: 'O módulo de controle de presença está em desenvolvimento. Em breve você poderá gerenciar a frequência dos funcionários.',
        icon: 'Clock'
    },
    'scheduling.appointments-dashboard': {
        title: 'Dashboard de Agendamentos em Construção',
        content: 'O dashboard de agendamentos está em desenvolvimento. Em breve você poderá visualizar estatísticas e análises sobre os agendamentos.',
        icon: 'BarChart'
    }
};
const isUnderConstruction = (moduleId, submenuId = null)=>{
    // Verificar se o módulo inteiro está em construção
    if (underConstructionModules.includes(moduleId)) {
        return true;
    }
    // Verificar se o submenu específico está em construção
    if (submenuId) {
        return underConstructionSubmenus.includes(`${moduleId}.${submenuId}`);
    }
    // Caso especial: o módulo admin não deve ser considerado em construção na página inicial
    if (moduleId === 'admin') {
        return false;
    }
    // Verificar se todos os submenus do módulo estão em construção
    return underConstructionSubmenus.filter((key)=>key.startsWith(`${moduleId}.`)).length > 0;
};
const getConstructionMessage = (moduleId, submenuId = null)=>{
    // Mensagens específicas para módulos
    const moduleMessages = {
        'financial': {
            title: 'Módulo Financeiro em Construção',
            content: 'O módulo financeiro está em desenvolvimento e estará disponível em breve. Você poderá gerenciar faturas, pagamentos, despesas e relatórios financeiros.',
            icon: 'DollarSign'
        },
        'hr': {
            title: 'Módulo de RH em Construção',
            content: 'O módulo de Recursos Humanos está em desenvolvimento e estará disponível em breve. Você poderá gerenciar funcionários, folha de pagamento, benefícios e muito mais.',
            icon: 'Users'
        }
    };
    // Se for um módulo inteiro em construção
    if (underConstructionModules.includes(moduleId) && !submenuId) {
        return moduleMessages[moduleId] || {
            title: 'Módulo em Construção',
            content: 'Este módulo está em desenvolvimento e estará disponível em breve.',
            icon: 'Construction'
        };
    }
    // Se for um submenu específico
    if (submenuId) {
        const key = `${moduleId}.${submenuId}`;
        return constructionMessages[key] || {
            title: 'Em Construção',
            content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
            icon: 'Construction'
        };
    }
    return {
        title: 'Módulo em Construção',
        content: 'Este módulo está em desenvolvimento e estará disponível em breve.',
        icon: 'Construction'
    };
};
}}),
"[project]/src/components/dashboard/Sidebar/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/dashboard/components.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/usePermissions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$CustomScrollArea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/CustomScrollArea.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constructionUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/constructionUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript) <export default as ConstructionButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
'use client';
;
;
;
;
;
;
;
;
;
;
const ModuleTitle = ({ moduleId, title, icon: Icon })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `
        relative overflow-hidden rounded-xl p-4 mb-4
        bg-gradient-to-r from-white dark:from-gray-800 to-module-${moduleId}-bg/30 dark:to-module-${moduleId}-bg-dark/30
        border-l-4 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
        shadow-sm dark:shadow-md dark:shadow-black/20
      `,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            w-12 h-12 rounded-lg mr-3 flex items-center justify-center
            bg-module-${moduleId}-bg/70 dark:bg-module-${moduleId}-bg-dark/70
            text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark
          `,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                            size: 26
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                            lineNumber: 30,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                        lineNumber: 25,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold",
                                    children: "Módulo"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                    lineNumber: 36,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-bold text-gray-800 dark:text-gray-100",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                    lineNumber: 37,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                            lineNumber: 35,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                        lineNumber: 33,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                lineNumber: 23,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
        lineNumber: 15,
        columnNumber: 5
    }, this);
};
// Mapeamento de submenus para permissões
const submenuPermissionsMap = {
    // Admin
    'admin.introduction': 'admin.dashboard.view',
    'admin.dashboard': 'admin.dashboard.view',
    'admin.users': 'admin.users.view',
    'admin.professions': [
        'admin.professions.view',
        'admin.profession-groups.view'
    ],
    'admin.logs': 'admin.logs.view',
    'admin.settings': 'admin.config.edit',
    'admin.backup': 'admin.config.edit',
    // ABA+
    'abaplus.anamnese': 'abaplus.anamnese.view',
    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',
    'abaplus.sessao': 'abaplus.sessao.view',
    // Financeiro
    'financial.invoices': 'financial.invoices.view',
    'financial.payments': 'financial.payments.view',
    'financial.expenses': 'financial.expenses.view',
    'financial.reports': 'financial.reports.view',
    'financial.cashflow': 'financial.reports.view',
    // RH
    'hr.employees': 'rh.employees.view',
    'hr.payroll': 'rh.payroll.view',
    'hr.documents': 'rh.employees.view',
    'hr.departments': 'rh.employees.view',
    'hr.attendance': 'rh.attendance.view',
    'hr.benefits': 'rh.benefits.view',
    'hr.training': 'rh.employees.view',
    // Pessoas
    'people.clients': 'people.clients.view',
    'people.persons': 'people.persons.view',
    'people.insurances': 'people.insurances.view',
    'people.insurance-limits': 'people.insurance-limits.view',
    // Agendamento
    'scheduler.calendar': 'scheduling.calendar.view',
    'scheduler.working-hours': 'scheduling.working-hours.view',
    'scheduler.service-types': 'scheduling.service-types.view',
    'scheduler.locations': 'scheduling.locations.view',
    'scheduler.occupancy': 'scheduling.occupancy.view',
    'scheduler.appointments-report': 'scheduling.appointments-report.view',
    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'
};
const Sidebar = ({ activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen })=>{
    const { can, hasModule, isAdmin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    // Inicializar o estado de grupos expandidos a partir do localStorage
    const [expandedGroups, setExpandedGroups] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        // Verificar se estamos no cliente (browser) antes de acessar localStorage
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return {};
    });
    // Encontrar o objeto do módulo ativo para acessar seu ícone
    const activeModuleObject = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["modules"].find((m)=>m.id === activeModule);
    const ModuleIcon = activeModuleObject?.icon;
    // Função para verificar se o usuário tem permissão para ver um submenu
    const hasPermissionForSubmenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((moduleId, submenuId)=>{
        // Ignorar verificação para grupos, pois a permissão é verificada para cada item
        if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {
            return true;
        }
        const permissionKey = `${moduleId}.${submenuId}`;
        const requiredPermission = submenuPermissionsMap[permissionKey];
        // Se não há mapeamento de permissão, permitir acesso
        if (!requiredPermission) return true;
        // Administradores têm acesso a tudo
        if (isAdmin()) return true;
        // Verificar permissão específica
        if (Array.isArray(requiredPermission)) {
            // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas
            return requiredPermission.some((perm)=>can(perm));
        } else {
            // Se for uma única permissão, verificar normalmente
            return can(requiredPermission);
        }
    }, [
        can,
        isAdmin
    ]);
    // Função para alternar a expansão de um grupo
    const toggleGroup = (groupId)=>{
        setExpandedGroups((prev)=>({
                ...prev,
                [groupId]: !prev[groupId]
            }));
    };
    // Verificar se algum item dentro de um grupo está ativo
    const isGroupActive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((moduleId, groupItems)=>{
        return groupItems.some((item)=>isSubmenuActive(moduleId, item.id));
    }, [
        isSubmenuActive
    ]);
    // Expandir automaticamente grupos que contêm o item ativo
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (activeModule && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["moduleSubmenus"][activeModule]) {
            const newExpandedGroups = {
                ...expandedGroups
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["moduleSubmenus"][activeModule].forEach((submenu)=>{
                if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {
                    newExpandedGroups[submenu.id] = true;
                }
            });
            setExpandedGroups(newExpandedGroups);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        activeModule,
        pathname,
        isGroupActive
    ]);
    // Verificar se um item de submenu tem permissão para ser exibido
    const hasPermissionForSubmenuItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((moduleId, submenuId)=>{
        return hasPermissionForSubmenu(moduleId, submenuId);
    }, [
        hasPermissionForSubmenu
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
        className: `w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`,
        "aria-label": "Navegação lateral",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$CustomScrollArea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                className: "flex-1 p-5",
                moduleColor: activeModule,
                children: [
                    activeModuleObject && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ModuleTitle, {
                        moduleId: activeModule,
                        title: activeModuleTitle,
                        icon: ModuleIcon
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                        lineNumber: 193,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "space-y-2",
                        "aria-labelledby": "sidebar-heading",
                        children: activeModule && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["moduleSubmenus"][activeModule]?.map((submenu)=>{
                            // Verificar se é um grupo ou um item individual
                            if (submenu.type === 'group') {
                                // Verificar se algum item do grupo tem permissão para ser exibido
                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));
                                if (!hasAnyPermission) {
                                    return null; // Não renderizar o grupo se nenhum item tiver permissão
                                }
                                const isGroupExpanded = expandedGroups[submenu.id] || false;
                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>toggleGroup(submenu.id),
                                            className: `
                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300
                      ${isAnyItemActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark font-medium` : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
                    `,
                                            "aria-expanded": isGroupExpanded,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium text-left",
                                                    children: submenu.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                    lineNumber: 233,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-gray-500 dark:text-gray-400",
                                                    children: isGroupExpanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                        size: 18,
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                        lineNumber: 236,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                        size: 18,
                                                        "aria-hidden": "true"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                        lineNumber: 238,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                    lineNumber: 234,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                            lineNumber: 222,
                                            columnNumber: 19
                                        }, this),
                                        isGroupExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1",
                                            children: submenu.items.map((item)=>{
                                                const isItemActive = isSubmenuActive(activeModule, item.id);
                                                // Verificar permissão antes de renderizar o item
                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {
                                                    return null; // Não renderizar se não tiver permissão
                                                }
                                                // Verificar se o item está em construção
                                                const itemKey = `${activeModule}.${item.id}`;
                                                const isItemUnderConstruction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constructionUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isUnderConstruction"])(activeModule, item.id);
                                                const constructionMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constructionUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getConstructionMessage"])(activeModule, item.id);
                                                // Estilo comum para os itens
                                                const itemClassName = `
                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300
                          ${isItemActive ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                               bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                               shadow-md dark:shadow-black/20` : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
                        `;
                                                // Se estiver em construção, usar o ConstructionButton
                                                if (isItemUnderConstruction) {
                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
                                                        className: itemClassName,
                                                        "aria-current": isItemActive ? 'page' : undefined,
                                                        title: constructionMessage.title,
                                                        content: constructionMessage.content,
                                                        icon: constructionMessage.icon,
                                                        position: "right",
                                                        children: [
                                                            item.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `
                                  ${isItemActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark` : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`}
                                `,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(item.icon, {
                                                                    size: 18,
                                                                    "aria-hidden": "true"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                                    lineNumber: 289,
                                                                    columnNumber: 35
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                                lineNumber: 283,
                                                                columnNumber: 33
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`,
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                                lineNumber: 295,
                                                                columnNumber: 31
                                                            }, this)
                                                        ]
                                                    }, item.id, true, {
                                                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                        lineNumber: 273,
                                                        columnNumber: 29
                                                    }, this);
                                                }
                                                // Se não estiver em construção, usar o botão normal
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),
                                                    className: itemClassName,
                                                    "aria-current": isItemActive ? 'page' : undefined,
                                                    children: [
                                                        item.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `
                                ${isItemActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark` : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`}
                              `,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(item.icon, {
                                                                size: 18,
                                                                "aria-hidden": "true"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                                lineNumber: 315,
                                                                columnNumber: 33
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                            lineNumber: 309,
                                                            columnNumber: 31
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`,
                                                            children: item.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                            lineNumber: 321,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, item.id, true, {
                                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                    lineNumber: 302,
                                                    columnNumber: 27
                                                }, this);
                                            })
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                            lineNumber: 245,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, submenu.id, true, {
                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                    lineNumber: 220,
                                    columnNumber: 17
                                }, this);
                            } else {
                                // Renderização de itens individuais (não agrupados)
                                const isActive = isSubmenuActive(activeModule, submenu.id);
                                // Verificar permissão antes de renderizar o item
                                if (!hasPermissionForSubmenu(activeModule, submenu.id)) {
                                    return null; // Não renderizar se não tiver permissão
                                }
                                // Verificar se o submenu está em construção
                                const submenuKey = `${activeModule}.${submenu.id}`;
                                const isSubmenuUnderConstruction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constructionUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isUnderConstruction"])(activeModule, submenu.id);
                                const constructionMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constructionUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getConstructionMessage"])(activeModule, submenu.id);
                                // Estilo comum para ambos os tipos de botões
                                const buttonClassName = `
                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300
                ${isActive ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                     bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                     shadow-md dark:shadow-black/20` : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
              `;
                                // Se estiver em construção, usar o ConstructionButton
                                if (isSubmenuUnderConstruction) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
                                        className: buttonClassName,
                                        "aria-current": isActive ? 'page' : undefined,
                                        title: constructionMessage.title,
                                        content: constructionMessage.content,
                                        icon: constructionMessage.icon,
                                        position: "right",
                                        children: [
                                            submenu.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `
                      ${isActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark` : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`}
                    `,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(submenu.icon, {
                                                    size: 20,
                                                    "aria-hidden": "true"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                    lineNumber: 373,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                lineNumber: 367,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `font-medium text-left ${isActive ? 'dark:text-white' : ''}`,
                                                children: submenu.title
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                lineNumber: 379,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, submenu.id, true, {
                                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                        lineNumber: 357,
                                        columnNumber: 19
                                    }, this);
                                }
                                // Se não estiver em construção, usar o botão normal
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),
                                    className: buttonClassName,
                                    "aria-current": isActive ? 'page' : undefined,
                                    children: [
                                        submenu.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `
                      ${isActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark` : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`}
                    `,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(submenu.icon, {
                                                size: 20,
                                                "aria-hidden": "true"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                                lineNumber: 399,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                            lineNumber: 393,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `font-medium text-left ${isActive ? 'dark:text-white' : ''}`,
                                            children: submenu.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                            lineNumber: 405,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, submenu.id, true, {
                                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                    lineNumber: 386,
                                    columnNumber: 17
                                }, this);
                            }
                        })
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                        lineNumber: 200,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                lineNumber: 190,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-5 border-t border-gray-100 dark:border-gray-700 mt-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleBackToModules,
                    className: `
            group w-full py-3 px-4 rounded-lg flex items-center gap-3
            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10
            transition-all duration-300
          `,
                    "aria-label": "Voltar para o dashboard principal",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `
            flex items-center justify-center w-8 h-8 rounded-full
            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70
            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            shadow-sm dark:shadow-md dark:shadow-black/20
            group-hover:scale-110 transition-transform duration-200
          `,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                size: 20,
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                                lineNumber: 432,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                            lineNumber: 425,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium text-gray-800 dark:text-gray-200",
                            children: "Voltar a Tela Inicial"
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                            lineNumber: 434,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                    lineNumber: 415,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/Sidebar/index.js",
                lineNumber: 414,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/dashboard/Sidebar/index.js",
        lineNumber: 184,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Sidebar;
}}),
"[project]/src/components/dashboard/Sidebar/ClientSidebar.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// src/components/dashboard/Sidebar/ClientSidebar.js
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$CustomScrollArea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/CustomScrollArea.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
'use client';
;
;
;
;
// Client-specific submenu configuration
const clientModuleSubmenus = {
    people: [
        {
            id: 'persons',
            title: 'Pessoas',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            description: 'Gerenciar pessoas relacionadas'
        }
    ],
    scheduler: [
        {
            id: 'calendar',
            title: 'Calendário',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
            description: 'Visualizar agenda'
        },
        {
            id: 'appointments',
            title: 'Meus Agendamentos',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
            description: 'Visualizar meus agendamentos'
        }
    ],
    profile: [
        {
            id: 'profile',
            title: 'Meu Perfil',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"],
            description: 'Gerenciar meu perfil e dados pessoais'
        }
    ]
};
const ModuleTitle = ({ moduleId, title, icon: Icon })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `
        relative overflow-hidden rounded-xl p-4 mb-4
        bg-gradient-to-r from-white dark:from-gray-800 to-module-${moduleId}-bg/30 dark:to-module-${moduleId}-bg-dark/30
        border-l-4 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
        shadow-sm dark:shadow-md dark:shadow-black/20
      `,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            w-12 h-12 rounded-lg mr-3 flex items-center justify-center
            bg-module-${moduleId}-bg/70 dark:bg-module-${moduleId}-bg-dark/70
            text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark
          `,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                            size: 26
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                            lineNumber: 39,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-bold text-gray-800 dark:text-white",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                lineNumber: 44,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-500 dark:text-gray-400",
                                children: "Área do Cliente"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                lineNumber: 45,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                lineNumber: 32,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
            lineNumber: 26,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
        lineNumber: 24,
        columnNumber: 5
    }, this);
};
const ClientSidebar = ({ activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen })=>{
    // Encontrar o objeto do módulo ativo para acessar seu ícone
    const ModuleIcon = activeModule === 'people' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"] : activeModule === 'scheduler' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"] : activeModule === 'profile' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
        className: `w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`,
        "aria-label": "Navegação lateral",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$CustomScrollArea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                className: "flex-1 p-5",
                moduleColor: activeModule,
                children: [
                    activeModule && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ModuleTitle, {
                        moduleId: activeModule,
                        title: activeModuleTitle,
                        icon: ModuleIcon
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "space-y-2",
                        "aria-labelledby": "sidebar-heading",
                        children: activeModule && clientModuleSubmenus[activeModule]?.map((submenu)=>{
                            const isActive = isSubmenuActive(activeModule, submenu.id);
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),
                                className: `
                  group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300
                  ${isActive ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                       bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                       shadow-md dark:shadow-black/20` : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
                `,
                                "aria-current": isActive ? 'page' : undefined,
                                children: [
                                    submenu.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `
                    ${isActive ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark` : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`}
                  `,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(submenu.icon, {
                                            size: 20,
                                            "aria-hidden": "true"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                            lineNumber: 113,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                        lineNumber: 107,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: `font-medium text-left ${isActive ? 'dark:text-white' : ''}`,
                                        children: submenu.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                        lineNumber: 119,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, submenu.id, true, {
                                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                lineNumber: 92,
                                columnNumber: 15
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                        lineNumber: 84,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-5 border-t border-gray-100 dark:border-gray-700 mt-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleBackToModules,
                    className: `
            group w-full py-3 px-4 rounded-lg flex items-center gap-3
            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10
            transition-all duration-300
          `,
                    "aria-label": "Voltar para o dashboard principal",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `
            flex items-center justify-center w-8 h-8 rounded-full
            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70
            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            shadow-sm dark:shadow-md dark:shadow-black/20
            group-hover:scale-110 transition-transform duration-200
          `,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                size: 20,
                                "aria-hidden": "true"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                                lineNumber: 145,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                            lineNumber: 138,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium text-gray-800 dark:text-gray-200",
                            children: "Voltar a Tela Inicial"
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                            lineNumber: 147,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                    lineNumber: 128,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
                lineNumber: 127,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/dashboard/Sidebar/ClientSidebar.js",
        lineNumber: 67,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ClientSidebar;
}}),
"[project]/src/components/tutorial/TutorialHighlight.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
"use client";
;
;
;
/**
 * Componente que cria um destaque visual em torno do elemento alvo para o tutorial
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.selector - Seletor CSS do elemento a ser destacado
 * @param {string} props.shape - Forma do destaque ('circle', 'rectangle', 'auto')
 * @param {number} props.padding - Espaço adicional ao redor do elemento em pixels
 * @param {boolean} props.pulsate - Se o destaque deve ter animação pulsante
 */ const TutorialHighlight = ({ selector, shape = 'auto', padding = 10, pulsate = true, children })=>{
    const [position, setPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        top: 0,
        left: 0,
        width: 0,
        height: 0
    });
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const highlightRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const targetRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Calcula a posição do elemento alvo
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!selector) return;
        const calculatePosition = ()=>{
            // Tenta encontrar todos os elementos que correspondem ao seletor
            const elements = document.querySelectorAll(selector);
            if (!elements || elements.length === 0) {
                console.warn(`TutorialHighlight: Elemento com seletor "${selector}" não encontrado.`);
                setIsVisible(false);
                return;
            }
            console.log(`TutorialHighlight: Encontrados ${elements.length} elementos com seletor "${selector}"`);
            // Encontra o primeiro elemento visível
            let targetElement = null;
            for(let i = 0; i < elements.length; i++){
                const element = elements[i];
                const rect = element.getBoundingClientRect();
                // Verifica se o elemento está visível na tela
                if (rect.width > 0 && rect.height > 0 && rect.top < window.innerHeight && rect.left < window.innerWidth && rect.bottom > 0 && rect.right > 0) {
                    // Verifica se o elemento não está oculto por CSS
                    const style = window.getComputedStyle(element);
                    if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                        targetElement = element;
                        console.log(`TutorialHighlight: Elemento visível encontrado na posição ${i + 1}`, rect);
                        break;
                    }
                }
            }
            // Se não encontrou nenhum elemento visível, usa o primeiro
            if (!targetElement && elements.length > 0) {
                targetElement = elements[0];
                console.log(`TutorialHighlight: Nenhum elemento visível encontrado, usando o primeiro elemento`);
            }
            if (!targetElement) {
                console.warn(`TutorialHighlight: Nenhum elemento visível encontrado com seletor "${selector}".`);
                setIsVisible(false);
                return;
            }
            // Armazenamos referência ao elemento para uso futuro
            targetRef.current = targetElement;
            const rect = targetElement.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
            // Determinar forma automática com base nas dimensões
            let finalShape = shape;
            if (shape === 'auto') {
                // Se o elemento for aproximadamente quadrado e pequeno, usar círculo
                const isSquarish = Math.abs(rect.width - rect.height) < Math.min(rect.width, rect.height) * 0.2;
                const isSmall = Math.max(rect.width, rect.height) < 100;
                finalShape = isSquarish && isSmall ? 'circle' : 'rectangle';
            }
            // Adicionar padding à posição
            let newPosition = {
                top: rect.top + scrollTop - padding,
                left: rect.left + scrollLeft - padding,
                width: rect.width + padding * 2,
                height: rect.height + padding * 2,
                shape: finalShape
            };
            setPosition(newPosition);
            setIsVisible(true);
        };
        // Calcular posição inicial
        calculatePosition();
        // Recalcular durante a rolagem da página
        const handleScroll = ()=>{
            requestAnimationFrame(calculatePosition);
        };
        // Recalcular em caso de redimensionamento
        window.addEventListener('resize', calculatePosition);
        window.addEventListener('scroll', handleScroll);
        return ()=>{
            window.removeEventListener('resize', calculatePosition);
            window.removeEventListener('scroll', handleScroll);
        };
    }, [
        selector,
        shape,
        padding
    ]);
    if (!isVisible) return null;
    const borderRadius = position.shape === 'circle' ? '50%' : '8px';
    const highlightStyles = {
        position: 'absolute',
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: `${position.width}px`,
        height: `${position.height}px`,
        borderRadius,
        boxShadow: '0 0 0 5000px rgba(0, 0, 0, 0.75)',
        zIndex: 9998,
        pointerEvents: 'none',
        animation: pulsate ? 'tutorial-highlight-pulse 2s infinite' : 'none'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "79aba806dabee7e",
                children: "@keyframes tutorial-highlight-pulse{0%{box-shadow:0 0 0 5000px #000000bf,0 0 0 4px #f939}50%{box-shadow:0 0 0 5000px #000000bf,0 0 0 8px #f93c}to{box-shadow:0 0 0 5000px #000000bf,0 0 0 4px #f939}}"
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: highlightRef,
                style: highlightStyles,
                className: "jsx-79aba806dabee7e" + " " + "tutorial-highlight"
            }, void 0, false, {
                fileName: "[project]/src/components/tutorial/TutorialHighlight.js",
                lineNumber: 161,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = TutorialHighlight;
}}),
"[project]/src/components/tutorial/TutorialDialog.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-ssr] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-ssr] (ecmascript) <export default as ArrowRight>");
"use client";
;
;
;
;
/**
 * Caixa de diálogo para exibir instruções do tutorial
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - Título da etapa
 * @param {string|React.ReactNode} props.content - Conteúdo/descrição da etapa
 * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')
 * @param {string} props.targetSelector - Seletor do elemento alvo para posicionamento
 * @param {number} props.offsetX - Deslocamento horizontal da posição padrão
 * @param {number} props.offsetY - Deslocamento vertical da posição padrão
 * @param {number} props.currentStep - Número da etapa atual
 * @param {number} props.totalSteps - Número total de etapas
 * @param {function} props.onNext - Função chamada ao clicar em "Próximo"
 * @param {function} props.onPrev - Função chamada ao clicar em "Anterior"
 * @param {function} props.onClose - Função chamada ao clicar em "Fechar"
 * @param {boolean} props.isFirstStep - Se é a primeira etapa
 * @param {boolean} props.isLastStep - Se é a última etapa
 */ const TutorialDialog = ({ title, content, position = 'auto', targetSelector, offsetX = 20, offsetY = 20, currentStep, totalSteps, onNext, onPrev, onClose, isFirstStep, isLastStep })=>{
    const dialogRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [dialogPosition, setDialogPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
    });
    const [calculatedPosition, setCalculatedPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(position);
    // Calcula a melhor posição para o diálogo com base no elemento alvo
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!targetSelector || !dialogRef.current) return;
        const calculatePosition = ()=>{
            // Tenta encontrar todos os elementos que correspondem ao seletor
            const elements = document.querySelectorAll(targetSelector);
            if (!elements || elements.length === 0) {
                console.warn(`TutorialDialog: Elemento com seletor "${targetSelector}" não encontrado para posicionar o diálogo.`);
                return;
            }
            console.log(`TutorialDialog: Encontrados ${elements.length} elementos com seletor "${targetSelector}"`);
            // Encontra o primeiro elemento visível
            let targetElement = null;
            for(let i = 0; i < elements.length; i++){
                const element = elements[i];
                const rect = element.getBoundingClientRect();
                // Verifica se o elemento está visível na tela
                if (rect.width > 0 && rect.height > 0 && rect.top < window.innerHeight && rect.left < window.innerWidth && rect.bottom > 0 && rect.right > 0) {
                    // Verifica se o elemento não está oculto por CSS
                    const style = window.getComputedStyle(element);
                    if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                        targetElement = element;
                        console.log(`TutorialDialog: Elemento visível encontrado na posição ${i + 1}`, rect);
                        break;
                    }
                }
            }
            // Se não encontrou nenhum elemento visível, usa o primeiro
            if (!targetElement && elements.length > 0) {
                targetElement = elements[0];
                console.log(`TutorialDialog: Nenhum elemento visível encontrado, usando o primeiro elemento`);
            }
            if (!targetElement) {
                console.warn(`TutorialDialog: Nenhum elemento visível encontrado com seletor "${targetSelector}" para posicionar o diálogo.`);
                return;
            }
            const targetRect = targetElement.getBoundingClientRect();
            const dialogRect = dialogRef.current.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            // Auto-calcular a melhor posição se for 'auto'
            let bestPosition = position;
            if (position === 'auto') {
                // Calcular espaço disponível em cada direção
                const spaceAbove = targetRect.top;
                const spaceBelow = viewportHeight - targetRect.bottom;
                const spaceLeft = targetRect.left;
                const spaceRight = viewportWidth - targetRect.right;
                // Determinar a direção com mais espaço
                const maxSpace = Math.max(spaceAbove, spaceBelow, spaceLeft, spaceRight);
                if (maxSpace === spaceBelow) bestPosition = 'bottom';
                else if (maxSpace === spaceAbove) bestPosition = 'top';
                else if (maxSpace === spaceRight) bestPosition = 'right';
                else bestPosition = 'left';
            }
            setCalculatedPosition(bestPosition);
            let newPosition = {};
            // Calcular posição com base na direção escolhida
            switch(bestPosition){
                case 'top':
                    newPosition = {
                        top: targetRect.top + scrollTop - dialogRect.height - offsetY,
                        left: targetRect.left + scrollLeft + targetRect.width / 2 - dialogRect.width / 2,
                        transform: 'none'
                    };
                    break;
                case 'right':
                    newPosition = {
                        top: targetRect.top + scrollTop + targetRect.height / 2 - dialogRect.height / 2,
                        left: targetRect.right + scrollLeft + offsetX,
                        transform: 'none'
                    };
                    break;
                case 'bottom':
                    newPosition = {
                        top: targetRect.bottom + scrollTop + offsetY,
                        left: targetRect.left + scrollLeft + targetRect.width / 2 - dialogRect.width / 2,
                        transform: 'none'
                    };
                    break;
                case 'left':
                    newPosition = {
                        top: targetRect.top + scrollTop + targetRect.height / 2 - dialogRect.height / 2,
                        left: targetRect.left + scrollLeft - dialogRect.width - offsetX,
                        transform: 'none'
                    };
                    break;
                default:
                    // Posição centralizada como fallback
                    newPosition = {
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)'
                    };
            }
            // Ajuste para garantir que o diálogo fique dentro da viewport
            if (newPosition.left < 20) newPosition.left = 20;
            if (newPosition.top < 20) newPosition.top = 20;
            if (newPosition.left + dialogRect.width > viewportWidth - 20) {
                newPosition.left = viewportWidth - dialogRect.width - 20;
            }
            if (newPosition.top + dialogRect.height > viewportHeight - 20) {
                newPosition.top = viewportHeight - dialogRect.height - 20;
            }
            setDialogPosition(newPosition);
        };
        // Pequeno atraso para garantir que o diálogo tenha sido renderizado com dimensões corretas
        const timer = setTimeout(calculatePosition, 300);
        // Recalcular em caso de redimensionamento da janela
        window.addEventListener('resize', calculatePosition);
        // Recalcular periodicamente para garantir que o diálogo esteja sempre posicionado corretamente
        const intervalTimer = setInterval(()=>{
            calculatePosition();
        }, 1000);
        return ()=>{
            clearTimeout(timer);
            clearInterval(intervalTimer);
            window.removeEventListener('resize', calculatePosition);
        };
    }, [
        targetSelector,
        position,
        offsetX,
        offsetY,
        dialogRef.current
    ]);
    // Classe para adicionar seta direcional
    const getPositionClass = ()=>{
        switch(calculatedPosition){
            case 'top':
                return 'tutorial-dialog-arrow-bottom';
            case 'right':
                return 'tutorial-dialog-arrow-left';
            case 'bottom':
                return 'tutorial-dialog-arrow-top';
            case 'left':
                return 'tutorial-dialog-arrow-right';
            default:
                return '';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "aad1caeb2440a043",
                children: '.tutorial-dialog-arrow-top:after{content:"";filter:drop-shadow(0 -2px 2px #0000001a);border:10px solid #0000;border-top-width:0;border-bottom-color:#fff;position:absolute;top:-10px;left:50%;transform:translate(-50%)}.tutorial-dialog-arrow-right:after{content:"";filter:drop-shadow(2px 0 2px #0000001a);border:10px solid #0000;border-left-color:#fff;border-right-width:0;position:absolute;top:50%;right:-10px;transform:translateY(-50%)}.tutorial-dialog-arrow-bottom:after{content:"";filter:drop-shadow(0 2px 2px #0000001a);border:10px solid #0000;border-top-color:#fff;border-bottom-width:0;position:absolute;bottom:-10px;left:50%;transform:translate(-50%)}.tutorial-dialog-arrow-left:after{content:"";filter:drop-shadow(-2px 0 2px #0000001a);border:10px solid #0000;border-left-width:0;border-right-color:#fff;position:absolute;top:50%;left:-10px;transform:translateY(-50%)}.dark .tutorial-dialog-arrow-top:after,.dark .tutorial-dialog-arrow-right:after,.dark .tutorial-dialog-arrow-bottom:after,.dark .tutorial-dialog-arrow-left:after{border-color:#0000}.dark .tutorial-dialog-arrow-top:after{border-bottom-color:#1f2937}.dark .tutorial-dialog-arrow-right:after{border-left-color:#1f2937}.dark .tutorial-dialog-arrow-bottom:after{border-top-color:#1f2937}.dark .tutorial-dialog-arrow-left:after{border-right-color:#1f2937}'
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dialogRef,
                style: {
                    top: dialogPosition.top,
                    left: dialogPosition.left,
                    transform: dialogPosition.transform
                },
                className: "jsx-aad1caeb2440a043" + " " + `fixed z-[9999] w-80 p-5 rounded-lg bg-white dark:bg-gray-800 shadow-xl dark:shadow-hard-dark ${getPositionClass()}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-aad1caeb2440a043" + " " + "flex justify-between items-start mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "jsx-aad1caeb2440a043" + " " + "text-lg font-bold text-primary-600 dark:text-primary-400",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                lineNumber: 292,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: onClose,
                                "aria-label": "Fechar tutorial",
                                className: "jsx-aad1caeb2440a043" + " " + "p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full transition-colors",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    size: 18
                                }, void 0, false, {
                                    fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                    lineNumber: 300,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                lineNumber: 295,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                        lineNumber: 291,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-aad1caeb2440a043" + " " + "mb-4 text-gray-700 dark:text-gray-300",
                        children: content
                    }, void 0, false, {
                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                        lineNumber: 305,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-aad1caeb2440a043" + " " + "w-full bg-gray-200 dark:bg-gray-700 h-1 rounded mb-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                width: `${(currentStep + 1) / totalSteps * 100}%`
                            },
                            className: "jsx-aad1caeb2440a043" + " " + "bg-primary-500 dark:bg-primary-600 h-1 rounded"
                        }, void 0, false, {
                            fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                            lineNumber: 311,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                        lineNumber: 310,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-aad1caeb2440a043" + " " + "flex justify-between items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-aad1caeb2440a043" + " " + "text-sm text-gray-500 dark:text-gray-400",
                                children: [
                                    "Passo ",
                                    currentStep + 1,
                                    " de ",
                                    totalSteps
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                lineNumber: 319,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-aad1caeb2440a043" + " " + "flex gap-2",
                                children: [
                                    !isFirstStep && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: onPrev,
                                        "aria-label": "Anterior",
                                        className: "jsx-aad1caeb2440a043" + " " + "p-1.5 rounded-md border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                            size: 16
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                            lineNumber: 329,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                        lineNumber: 324,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: onNext,
                                        "aria-label": isLastStep ? "Concluir" : "Próximo",
                                        className: "jsx-aad1caeb2440a043" + " " + "px-3 py-1.5 rounded-md bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white flex items-center gap-1.5 transition-colors",
                                        children: [
                                            isLastStep ? "Concluir" : "Próximo",
                                            !isLastStep && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                                lineNumber: 338,
                                                columnNumber: 31
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                        lineNumber: 332,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                                lineNumber: 322,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                        lineNumber: 318,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/tutorial/TutorialDialog.js",
                lineNumber: 281,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = TutorialDialog;
}}),
"[project]/src/components/tutorial/TutorialStep.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialHighlight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialHighlight.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialDialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialDialog.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/TutorialContext.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
/**
 * Componente que combina o destaque com o diálogo para uma etapa do tutorial
 * Com scroll automático ao navegar entre etapas
 */ const TutorialStep = ()=>{
    const { isActive, currentStep, currentStepIndex, steps, isFirstStep, isLastStep, nextStep, prevStep, endTutorial } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTutorial"])();
    // Efeito para rolar para o elemento destacado quando a etapa mudar
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isActive && currentStep && currentStep.selector) {
            const scrollToElement = ()=>{
                // Tenta encontrar todos os elementos que correspondem ao seletor
                const elements = document.querySelectorAll(currentStep.selector);
                if (!elements || elements.length === 0) {
                    console.warn(`TutorialStep: Elemento com seletor "${currentStep.selector}" não encontrado.`);
                    return;
                }
                console.log(`TutorialStep: Encontrados ${elements.length} elementos com seletor "${currentStep.selector}"`);
                // Encontra o primeiro elemento visível
                let targetElement = null;
                for(let i = 0; i < elements.length; i++){
                    const element = elements[i];
                    const rect = element.getBoundingClientRect();
                    // Verifica se o elemento está visível na tela
                    if (rect.width > 0 && rect.height > 0 && rect.top < window.innerHeight && rect.left < window.innerWidth && rect.bottom > 0 && rect.right > 0) {
                        // Verifica se o elemento não está oculto por CSS
                        const style = window.getComputedStyle(element);
                        if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                            targetElement = element;
                            console.log(`TutorialStep: Elemento visível encontrado na posição ${i + 1}`, rect);
                            break;
                        }
                    }
                }
                // Se não encontrou nenhum elemento visível, usa o primeiro
                if (!targetElement && elements.length > 0) {
                    targetElement = elements[0];
                    console.log(`TutorialStep: Nenhum elemento visível encontrado, usando o primeiro elemento`);
                }
                if (targetElement) {
                    // Pequeno atraso para garantir que a UI esteja pronta
                    setTimeout(()=>{
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });
                        console.log(`TutorialStep: Rolando para o elemento no passo ${currentStepIndex + 1}`);
                    }, 100);
                }
            };
            // Pequeno atraso para garantir que a página esteja completamente carregada
            setTimeout(scrollToElement, 300);
        }
    }, [
        isActive,
        currentStep,
        currentStepIndex
    ]);
    if (!isActive || !currentStep) return null;
    const { title, content, selector, position = 'auto', shape = 'auto', highlightPadding = 10, pulsate = true, dialogOffsetX = 20, dialogOffsetY = 20 } = currentStep;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialHighlight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        selector: selector,
        shape: shape,
        padding: highlightPadding,
        pulsate: pulsate,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialDialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            title: title,
            content: content,
            position: position,
            targetSelector: selector,
            offsetX: dialogOffsetX,
            offsetY: dialogOffsetY,
            currentStep: currentStepIndex,
            totalSteps: steps.length,
            onNext: nextStep,
            onPrev: prevStep,
            onClose: endTutorial,
            isFirstStep: isFirstStep,
            isLastStep: isLastStep
        }, void 0, false, {
            fileName: "[project]/src/components/tutorial/TutorialStep.js",
            lineNumber: 107,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/tutorial/TutorialStep.js",
        lineNumber: 101,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TutorialStep;
}}),
"[project]/src/components/tutorial/TutorialManager.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialStep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialStep.js [app-ssr] (ecmascript)");
"use client";
;
;
;
/**
 * Componente que gerencia a exibição de tutoriais em uma página
 * Deve ser incluído uma vez em cada página que usa tutoriais
 */ const TutorialManager = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialStep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/components/tutorial/TutorialManager.js",
        lineNumber: 11,
        columnNumber: 10
    }, this);
};
const __TURBOPACK__default__export__ = TutorialManager;
}}),
"[project]/src/tutorials/tutorialMapping.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Este arquivo mapeia tutoriais específicos para diferentes rotas da aplicação
 * Cada rota principal tem seu próprio tutorial com passos específicos
 */ // Tutorial para o Dashboard principal
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "getTutorialForRoute": (()=>getTutorialForRoute)
});
const dashboardTutorial = [
    {
        title: "Bem-vindo ao Dashboard",
        content: "Este é o seu painel central, onde você pode visualizar informações importantes e acessar todos os módulos do sistema.",
        selector: "#Main-Container-Dashboard",
        position: "bottom"
    },
    {
        title: "Cartão de Boas-vindas",
        content: "Aqui você visualiza informações personalizadas conforme o horário do dia e seu perfil de usuário.",
        selector: ".bg-gradient-to-r.from-white.to-gray-50",
        position: "bottom"
    },
    {
        title: "Módulos Disponíveis",
        content: "Cada card representa um módulo do sistema. Clique em um deles para acessar suas funcionalidades específicas.",
        selector: ".grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-5 > div:first-child",
        position: "right"
    },
    {
        title: "Próximos Agendamentos",
        content: "Visualize seus próximos agendamentos com informações detalhadas sobre pacientes, profissionais e horários.",
        selector: ".bg-white.dark\\:bg-gray-800.rounded-xl h3:has(.calendar)",
        position: "top"
    },
    {
        title: "Ações Rápidas",
        content: "Acesse diretamente as funcionalidades mais utilizadas sem precisar navegar pelos diferentes módulos.",
        selector: "div:has(> h3:has(.activity))",
        position: "left",
        highlightPadding: 15
    }
];
//----------------------------------------------AGENDAMENTO--------------------------------------------------------------------------------------
// Tutorial para Calendário de Agendamentos
const calendarTutorial = [
    {
        title: "Calendário de Agendamentos",
        content: "Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.",
        selector: ".fc-view-harness",
        position: "top"
    },
    {
        title: "Filtros",
        content: "Use estes filtros para encontrar agendamentos específicos por profissional, paciente, tipo de serviço ou local.",
        selector: ".filter-section",
        position: "bottom"
    },
    {
        title: "Criar Agendamento",
        content: "Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário.",
        selector: ".fc-daygrid-day:not(.fc-day-other):nth-child(3)",
        position: "bottom"
    },
    {
        title: "Visualizações",
        content: "Alterne entre visualizações de mês, semana ou dia para ver seus agendamentos com diferentes níveis de detalhe.",
        selector: ".fc-toolbar-chunk:last-child",
        position: "bottom"
    }
];
// Tutorial para página de horários de trabalho
const workingHoursTutorial = [
    {
        title: "Horários de Trabalho",
        content: "Gerencie os Horários de Trabalho dos funcionários cadastrados no sistema.",
        selector: "table",
        position: "top"
    },
    {
        title: "Exportar Dados",
        content: "Exporte os horários de trabalho em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Horários de Trabalho pelo dia da Semana",
        content: "Clique aqui para mudar os dias a serem preenchidos.",
        selector: "#bordaDiaDaSemana",
        position: "left"
    },
    {
        title: "Horários selecionáveis",
        content: "Clique aqui para selecionar os horários daquele dia da semana que o profissional está disponível.",
        selector: "#bordaSelecaoHorario",
        position: "left"
    },
    {
        title: "Horários por Funcionário",
        content: "Clique aqui para selecionar os horários da semana completa de um único funcionário.",
        selector: "#bordaNomeFuncionario",
        position: "left"
    },
    {
        title: "Salve seus Horários e Utilize os Filtros",
        content: "Clique aqui para salvar os horários, e para visualizar utilize os filtros.",
        selector: "#bordaSalvarHorarioTrabalho",
        position: "right"
    }
];
// Tutorial para página de localizações
const locationsTutorial = [
    {
        title: "Localizações",
        content: "Gerencie os locais de atendimento disponíveis no sistema.",
        selector: "table",
        position: "top"
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de localizações em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Adicionar Localização",
        content: "Clique aqui para cadastrar um novo local de atendimento.",
        selector: "button:has(.lucide-plus)",
        position: "left"
    }
];
// Tutorial para página de ocupações
const ocuppancyTutorial = [
    {
        title: "Análise de Ocupação",
        content: "Visualize a taxa de Ocupação de locais, profissionais e até pelo tipo do serviço caso deseje limitar.",
        selector: "#bordaTaxaOcupacao",
        position: "top"
    },
    {
        title: "Exportar Dados",
        content: "Exporte os dados de ocupação em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Use os filtros!",
        content: "Clique para selecionar o periodo do tempo, a data referência, os profissionais, serviços ou até os locais.",
        selector: "#bordaFiltroOcupacao",
        position: "left"
    }
];
// Tutorial para tipos de serviço
const serviceTypesTutorial = [
    {
        title: "Tipos de Serviço",
        content: "Gerencie os serviços oferecidos pela sua clínica ou consultório.",
        selector: "table",
        position: "top"
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de tipos de serviço em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Adicionar Tipo de Serviço",
        content: "Clique aqui para cadastrar um novo tipo de serviço.",
        selector: "button:has(.lucide-plus)",
        position: "left"
    }
];
//----------------------------------------------PESSOAS--------------------------------------------------------------------------------------
// Tutorial para página de Clientes
const clientsListTutorial = [
    {
        title: "Lista de Clientes",
        content: "Aqui você pode visualizar, filtrar e gerenciar todos os clientes cadastrados no sistema.",
        selector: "table",
        position: "top"
    },
    {
        title: "Pesquisa e Filtros",
        content: "Use estes campos para buscar clientes por nome ou email, e filtrar por status.",
        selector: "form",
        position: "bottom"
    },
    {
        title: "Adicionar Cliente",
        content: "Clique aqui para cadastrar um novo cliente no sistema.",
        selector: "button:has(.lucide-plus)",
        position: "left"
    }
];
// Tutorial para página de Pacientes
const patientsListTutorial = [
    {
        title: "Lista de Pacientes",
        content: "Aqui você pode visualizar, filtrar e gerenciar todos os pacientes cadastrados no sistema.",
        selector: "table",
        position: "top"
    },
    {
        title: "Pesquisa e Filtros",
        content: "Use estes campos para buscar pacientes por nome, email ou CPF, e filtrar por status.",
        selector: "form",
        position: "bottom"
    },
    {
        title: "Adicionar Paciente",
        content: "Clique aqui para cadastrar um novo paciente no sistema.",
        selector: "button:has(.lucide-plus)",
        position: "left"
    },
    {
        title: "Ações por Paciente",
        content: "Nesta coluna você pode visualizar detalhes, editar, ativar/desativar ou excluir um paciente.",
        selector: "td:last-child",
        position: "left"
    }
];
// Tutorial para página de detalhes do paciente
const patientDetailsTutorial = [
    {
        title: "Detalhes do Paciente",
        content: "Esta página mostra informações detalhadas sobre o paciente selecionado.",
        selector: "h1",
        position: "bottom"
    },
    {
        title: "Informações Pessoais",
        content: "Aqui você encontra os dados pessoais e de contato do paciente.",
        selector: "h2",
        position: "right"
    },
    {
        title: "Documentos",
        content: "Nesta seção você pode visualizar e gerenciar documentos relacionados ao paciente.",
        selector: "h3",
        position: "bottom"
    }
];
// Tutorial para página de convênios
const insurancesTutorial = [
    {
        title: "Convênios",
        content: "Gerencie os convênios disponíveis para seus pacientes nesta página.",
        selector: "table",
        position: "top"
    },
    {
        title: "Adicionar Convênio",
        content: "Clique aqui para cadastrar um novo convênio no sistema.",
        selector: "button:has(.lucide-plus)",
        position: "left"
    }
];
// Tutorial para página de Usuários
const admUsersTutorial = [
    {
        title: "Gerenciamento de Usuários",
        content: "Esta página permite gerenciar todos os usuários do sistema. Aqui você pode criar, editar, ativar/desativar e excluir usuários, além de gerenciar suas permissões e acesso aos módulos.",
        selector: ".ModuleHeader h1",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de usuários em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Adicionar Novo Usuário",
        content: "Clique aqui para criar um novo usuário no sistema. Você poderá definir informações pessoais, credenciais de acesso, profissão, função e permissões.",
        selector: "button:has(.lucide-plus)",
        position: "left",
        dialogOffsetX: 15,
        highlightPadding: 5
    },
    {
        title: "Pesquisa e Filtros",
        content: "Use estes campos para buscar usuários por nome, email ou profissão. Você também pode filtrar por status (ativo/inativo) e por função (administrador, funcionário, etc).",
        selector: "#filtroUsuario",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Tabela de Usuários",
        content: "Esta tabela exibe todos os usuários do sistema com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.",
        selector: "#admin-users-table",
        position: "top",
        dialogOffsetY: 10
    },
    {
        title: "Ações de Usuário",
        content: "Aqui você encontra botões para editar informações, gerenciar módulos, permissões, função, ativar/desativar ou excluir usuários.",
        selector: "td:last-child .flex.justify-end",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Editar Usuário",
        content: "Clique neste botão para modificar as informações pessoais e credenciais do usuário selecionado.",
        selector: "#edicaoUsuario",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Gerenciar Módulos",
        content: "Este botão permite definir a quais módulos do sistema o usuário terá acesso. Administradores de sistema e de empresa têm acesso automático a todos os módulos.",
        selector: "#gerenciarModulo",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Gerenciar Permissões",
        content: "Controle detalhado das permissões do usuário dentro de cada módulo. Você pode definir o que o usuário pode visualizar, criar, editar ou excluir.",
        selector: "#gerenciarPermissoes",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Fluxo de Criação de Usuário",
        content: "O fluxo recomendado para criar um usuário é: 1) Informações pessoais, 2) Documentos, 3) Definir função, 4) Atribuir módulos (para funcionários), 5) Configurar permissões específicas.",
        selector: "#admin-users-table",
        position: "bottom",
        dialogOffsetY: 10
    }
];
//----------------------------------------------ADMINISTRAÇÃO--------------------------------------------------------------------------------------
// Tutorial para página de Profissões/Grupos
const professionsGroupsTutorial = [
    {
        title: "Gerenciamento de Profissões e Grupos",
        content: "Esta página permite gerenciar as profissões e grupos de profissões disponíveis no sistema. Estas informações são essenciais para a criação de usuários e definição de permissões.",
        selector: ".text-2xl.font-bold",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Abas de Navegação",
        content: "Alterne entre as abas 'Profissões' e 'Grupos de Profissões' para gerenciar cada tipo de cadastro separadamente.",
        selector: "button[role='tab']",
        position: "bottom",
        dialogOffsetY: 15
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de profissões ou grupos em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Adicionar Nova Profissão/Grupo",
        content: "Clique neste botão para criar uma nova profissão ou grupo no sistema. Você poderá definir nome, descrição, associações e status.",
        selector: "button:has(.lucide-plus)",
        position: "left",
        highlightPadding: 5,
        dialogOffsetX: 15
    },
    {
        title: "Filtros de Busca",
        content: "Use estes campos para buscar profissões ou grupos por nome, filtrar por status ou outras propriedades.",
        selector: "input[type='text']",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Tabela de Profissões/Grupos",
        content: "Esta tabela exibe todas as profissões ou grupos cadastrados com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.",
        selector: "table",
        position: "top",
        dialogOffsetY: 10
    },
    {
        title: "Ações Disponíveis",
        content: "Aqui você encontra botões para ver usuários associados, editar ou excluir profissões e grupos.",
        selector: "td:last-child .flex.justify-end",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Visibilidade de Empresa",
        content: "Administradores de sistema podem ver a qual empresa cada profissão ou grupo pertence. Administradores de empresa só veem registros da própria empresa.",
        selector: "th:nth-child(3)",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Dica Importante",
        content: "Configure primeiro os grupos de profissões antes de criar as profissões individuais. Isso facilitará a organização e atribuição de permissões posteriormente.",
        selector: "table",
        position: "bottom",
        dialogOffsetY: 10
    }
];
// Tutorial para página de Configurações
const settingsTutorial = [
    {
        title: "Configurações do Sistema",
        content: "Esta página permite personalizar diversos aspectos do sistema de acordo com as necessidades da sua empresa ou organização.",
        selector: "h1, .text-2xl.font-bold",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Navegação por Abas",
        content: "Use estas abas para navegar entre diferentes categorias de configurações. As opções disponíveis dependem do seu nível de acesso no sistema.",
        selector: "button.flex.items-center.gap-2",
        position: "bottom",
        dialogOffsetY: 15
    },
    {
        title: "Configurações Gerais",
        content: "Defina informações básicas como nome do site, URL, email administrativo, formato de data e hora, e outras configurações globais.",
        selector: "button:has(.lucide-cog), button:has(.lucide-settings)",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Configurações de Empresa",
        content: "Gerencie informações da empresa como nome, CNPJ, endereço, contatos e configurações específicas. Administradores de sistema podem gerenciar múltiplas empresas.",
        selector: "button:has(.lucide-building)",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Configurações de Unidades",
        content: "Administre as unidades ou filiais da empresa, incluindo endereços, horários de funcionamento e configurações específicas de cada local.",
        selector: "button:has(.lucide-map-pin)",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Configurações de Email",
        content: "Configure os servidores de email, modelos de mensagens e notificações automáticas enviadas pelo sistema.",
        selector: "button:has(.lucide-mail)",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Configurações de Backup",
        content: "Configure as opções de backup automático do sistema, incluindo frequência, horário e tipos de arquivos a serem salvos.",
        selector: "button:has(.lucide-database), button:has(.lucide-hard-drive)",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Configurações de Segurança",
        content: "Defina políticas de senha, autenticação de dois fatores e outras configurações de segurança para proteger o sistema.",
        selector: "button:has(.lucide-shield), button:has(.lucide-lock)",
        position: "left",
        dialogOffsetX: 15
    },
    {
        title: "Formulários de Configuração",
        content: "Preencha os formulários com as informações necessárias para configurar o sistema de acordo com as necessidades da sua empresa.",
        selector: "input[type='text'], select",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Opções de Localização",
        content: "Configure o fuso horário, formato de data e hora para todo o sistema de acordo com as necessidades da sua região.",
        selector: "select",
        position: "bottom",
        dialogOffsetY: 10
    },
    {
        title: "Salvar Alterações",
        content: "Lembre-se de clicar em 'Salvar' após fazer modificações em cada seção para que as alterações sejam aplicadas.",
        selector: "button.bg-primary-500, button.bg-orange-500, button[type='submit']",
        position: "left",
        dialogOffsetX: 15
    }
];
//----------------------------------------------DASHBOARDS NO GERAL--------------------------------------------------------------------------------------
// Tutorial para o Dashboard de Administração
const adminDashboardTutorial = [
    {
        title: "Dashboard de Administração",
        content: "Este painel oferece uma visão geral do sistema com métricas importantes, estatísticas de uso e informações sobre usuários e atividades recentes.",
        selector: "h1",
        position: "bottom"
    },
    {
        title: "Exportar Dados",
        content: "Exporte os dados do dashboard em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Seletor de Empresa",
        content: "Administradores de sistema podem selecionar diferentes empresas para visualizar dados específicos de cada uma. Administradores de empresa veem apenas dados da própria empresa.",
        selector: "select",
        position: "bottom"
    },
    {
        title: "Cartões de Estatísticas",
        content: "Estes cartões mostram métricas importantes como total de usuários, usuários ativos, clientes e agendamentos, com indicadores de crescimento.",
        selector: ".grid",
        position: "bottom"
    },
    {
        title: "Gráficos e Visualizações",
        content: "Visualize dados importantes do sistema através de gráficos interativos que mostram tendências e distribuições.",
        selector: ".lg\\:col-span-2",
        position: "bottom"
    },
    {
        title: "Seletor de Período",
        content: "Filtre os dados do gráfico por diferentes períodos de tempo para análises mais específicas.",
        selector: "select:nth-of-type(2)",
        position: "bottom"
    },
    {
        title: "Distribuição de Usuários",
        content: "Este gráfico mostra a distribuição de usuários por módulo, ajudando a identificar quais áreas do sistema são mais utilizadas.",
        selector: ".h-80",
        position: "left"
    },
    {
        title: "Tabelas de Informações",
        content: "Visualize informações detalhadas sobre usuários ativos e atividades recentes no sistema.",
        selector: "table",
        position: "bottom"
    },
    {
        title: "Informações do Sistema",
        content: "Visualize detalhes técnicos como versão do sistema, status do servidor, uso de recursos e outras informações relevantes para administradores.",
        selector: ".grid:last-child",
        position: "bottom"
    }
];
// Tutorial para todos os tipos de Dashboards
const dashboardsTutorial = [
    {
        title: "Dashboard",
        content: "Use filtros no seu Dashboard ou busque para visualizar as informações que deseja",
        selector: "#bordadashboards",
        position: "bottom"
    },
    {
        title: "Tipo de Dashboard",
        content: "Cada Dashboard tem suas informações retiradas dependendo de seu módulo.",
        selector: "#gradedashboards",
        position: "top"
    }
];
// Mapa de rotas para tutoriais
// Cada chave é um padrão de URL, e o valor é o tutorial correspondente
const tutorialMap = {
    '/dashboard': dashboardTutorial,
    '/dashboard/admin/users': admUsersTutorial,
    '/dashboard/admin/dashboard': adminDashboardTutorial,
    '/dashboard/admin/professions': professionsGroupsTutorial,
    '/dashboard/admin/settings': settingsTutorial,
    '/dashboard/scheduler/appointments-dashboard': dashboardsTutorial,
    '/dashboard/scheduler/working-hours': workingHoursTutorial,
    '/dashboard/scheduler/occupancy': ocuppancyTutorial,
    '/dashboard/scheduler/calendar': calendarTutorial,
    '/dashboard/people/persons': patientsListTutorial,
    '/dashboard/people/clients': clientsListTutorial,
    '/dashboard/people/persons/[id]': patientDetailsTutorial,
    '/dashboard/people/insurances': insurancesTutorial,
    '/dashboard/scheduler/locations': locationsTutorial,
    '/dashboard/scheduler/service-types': serviceTypesTutorial
};
const getTutorialForRoute = (pathname)=>{
    // Tenta encontrar uma correspondência exata primeiro
    if (tutorialMap[pathname]) {
        return {
            steps: tutorialMap[pathname],
            name: pathname.replace(/\//g, '-').replace(/^-/, '') // Converte '/dashboard' para 'dashboard'
        };
    }
    // Verifica rotas dinâmicas com parâmetros (ex: /dashboard/people/persons/123)
    for(const route in tutorialMap){
        if (route.includes('[id]') && pathname.match(new RegExp(route.replace('[id]', '\\d+')))) {
            return {
                steps: tutorialMap[route],
                name: route.replace(/\//g, '-').replace(/^-/, '').replace('[id]', 'details')
            };
        }
    }
    // Se não encontrou nenhuma correspondência
    return null;
};
const __TURBOPACK__default__export__ = tutorialMap;
}}),
"[project]/src/components/tutorial/ContextualHelpButton.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/TutorialContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$tutorials$2f$tutorialMapping$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/tutorials/tutorialMapping.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/circle-help.js [app-ssr] (ecmascript) <export default as HelpCircle>");
"use client";
;
;
;
;
;
;
/**
 * Botão de ajuda contextual que mostra tutoriais específicos com base na rota atual
 */ const ContextualHelpButton = ()=>{
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const { startTutorial, isActive } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTutorial"])();
    const [currentTutorial, setCurrentTutorial] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isHovering, setIsHovering] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Busca o tutorial apropriado quando a rota muda
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const tutorialData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$tutorials$2f$tutorialMapping$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTutorialForRoute"])(pathname);
        setCurrentTutorial(tutorialData);
    }, [
        pathname
    ]);
    // Função para iniciar o tutorial contextual
    const handleStartTutorial = ()=>{
        if (currentTutorial && currentTutorial.steps && currentTutorial.steps.length > 0) {
            startTutorial(currentTutorial.steps, currentTutorial.name);
        } else {
            // Se não temos um tutorial para esta página, podemos mostrar uma mensagem
            console.log('Nenhum tutorial disponível para esta página');
        // Você pode adicionar aqui uma notificação para o usuário
        }
    };
    // Se o tutorial já estiver ativo, não mostramos o botão
    if (isActive) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-6 right-6 z-50",
        children: [
            isHovering && currentTutorial && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-16 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 mb-2 w-48 text-sm text-gray-700 dark:text-gray-300 animate-fade-in",
                children: [
                    "Clique para ver o tutorial desta página",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-0 right-5 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800"
                    }, void 0, false, {
                        fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
                        lineNumber: 44,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
                lineNumber: 42,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: handleStartTutorial,
                onMouseEnter: ()=>setIsHovering(true),
                onMouseLeave: ()=>setIsHovering(false),
                className: `
          w-12 h-12 rounded-full flex items-center justify-center shadow-lg 
          transition-all duration-300 hover:shadow-xl
          ${currentTutorial ? 'bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'}
        `,
                "aria-label": "Mostrar tutorial da página",
                disabled: !currentTutorial,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__["HelpCircle"], {
                        size: currentTutorial ? 28 : 24
                    }, void 0, false, {
                        fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this),
                    currentTutorial && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "absolute -top-1 -right-1 h-3 w-3 rounded-full bg-white border-2 border-primary-500 dark:border-primary-600"
                    }, void 0, false, {
                        fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
                        lineNumber: 67,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
                lineNumber: 49,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/tutorial/ContextualHelpButton.js",
        lineNumber: 39,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ContextualHelpButton;
}}),
"[project]/src/app/dashboard/layout.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>DashboardLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PrivateRoute$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/PrivateRoute.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/dashboard/components.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$ClientHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/dashboard/ClientHeader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$Sidebar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/dashboard/Sidebar/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$Sidebar$2f$ClientSidebar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/dashboard/Sidebar/ClientSidebar.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/usePermissions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
// Componentes de tutorial
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialManager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$ContextualHelpButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/ContextualHelpButton.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function DashboardLayout({ children }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const { isClient } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    // Verificar se o usuário é um cliente
    const isClientUser = ()=>{
        return user?.isClient || user?.role === 'CLIENT';
    };
    const [activeModule, setActiveModule] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [activeSubmenu, setActiveSubmenu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isSidebarOpen, setIsSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Determina o módulo ativo e submenu baseado na URL
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (pathname) {
            // Exemplo: /dashboard/people/persons/ID => people, persons
            const path = pathname.split('/');
            if (path.length >= 3) {
                const moduleFromUrl = path[2]; // O módulo é o terceiro segmento da URL
                setActiveModule(moduleFromUrl);
                if (path.length >= 4) {
                    const submenuFromUrl = path[3]; // O submenu é o quarto segmento da URL
                    setActiveSubmenu(submenuFromUrl);
                }
            } else {
                setActiveModule(null);
                setActiveSubmenu(null);
            }
        }
    }, [
        pathname
    ]);
    // Memoize handlers para evitar recriações em cada render
    const handleBackToModules = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        router.push('/dashboard');
    }, [
        router
    ]);
    const handleModuleSubmenuClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((moduleId, submenuId)=>{
        router.push(`/dashboard/${moduleId}/${submenuId}`);
    }, [
        router
    ]);
    // Verifica se um submenu está ativo (mesmo quando estamos em uma página de detalhes)
    const isSubmenuActive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((moduleId, submenuId)=>{
        return pathname.startsWith(`/dashboard/${moduleId}/${submenuId}`);
    }, [
        pathname
    ]);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsSidebarOpen((prev)=>!prev);
    }, []);
    // Obter o título do módulo ativo
    const activeModuleTitle = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["modules"].find((m)=>m.id === activeModule)?.title || '';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PrivateRoute$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PrivateRoute"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex",
                    children: [
                        pathname !== '/dashboard' && pathname !== '/dashboard/profile' && (isClientUser() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$Sidebar$2f$ClientSidebar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            activeModule: activeModule,
                            activeModuleTitle: activeModuleTitle,
                            isSubmenuActive: isSubmenuActive,
                            handleModuleSubmenuClick: handleModuleSubmenuClick,
                            handleBackToModules: handleBackToModules,
                            isSidebarOpen: isSidebarOpen
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/layout.js",
                            lineNumber: 80,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$Sidebar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            activeModule: activeModule,
                            activeModuleTitle: activeModuleTitle,
                            isSubmenuActive: isSubmenuActive,
                            handleModuleSubmenuClick: handleModuleSubmenuClick,
                            handleBackToModules: handleBackToModules,
                            isSidebarOpen: isSidebarOpen
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/layout.js",
                            lineNumber: 89,
                            columnNumber: 15
                        }, this)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1",
                            children: [
                                isClientUser() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$ClientHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    toggleSidebar: toggleSidebar,
                                    isSidebarOpen: isSidebarOpen
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/layout.js",
                                    lineNumber: 103,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$components$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Header"], {
                                    toggleSidebar: toggleSidebar,
                                    isSidebarOpen: isSidebarOpen
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/layout.js",
                                    lineNumber: 108,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                                    className: "p-6",
                                    id: "main-content",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "max-w-7xl mx-auto",
                                        children: children
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/layout.js",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/layout.js",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/layout.js",
                            lineNumber: 101,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/layout.js",
                    lineNumber: 76,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/dashboard/layout.js",
                    lineNumber: 126,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$ContextualHelpButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/dashboard/layout.js",
                    lineNumber: 127,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/dashboard/layout.js",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/layout.js",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/dashboard/layout.js [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__e573af._.js.map